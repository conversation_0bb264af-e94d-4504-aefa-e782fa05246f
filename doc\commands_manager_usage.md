# 统一命令管理器 (commands_manager) 使用指南

## 概述

新的 `commands_manager` 工具整合了所有命令管理功能，通过 `action` 参数来执行不同的操作。这个设计大大简化了工具数量，从原来的19个工具减少到1个统一工具。

## 核心原理

- **所有命令 = 黑名单 + 白名单 + 插件自身注册的命令**
- **只能执行白名单中的命令**
- **MCP核心工具受保护，不可移动**
- **新发现的命令自动添加到黑名单**

## 6个Action详解

### Action 1: list_whitelist - 列出白名单

**描述**: 列出所有可执行的白名单命令

```json
{
  "action": "list_whitelist"
}
```

**返回**: 白名单命令列表及统计信息

---

### Action 2: list_blacklist - 列出黑名单

**描述**: 列出所有不可执行的黑名单命令

```json
{
  "action": "list_blacklist"
}
```

**返回**: 黑名单命令列表及统计信息

---

### Action 3: check_execution - 检查执行权限

**描述**: 检查指定命令是否可执行（是否在白名单中）

```json
{
  "action": "check_execution",
  "command_ids": ["workbench.action.quickOpen", "editor.action.formatDocument"]
}
```

**返回**: 每个命令的执行权限状态

---

### Action 4: move_to_whitelist - 移动到白名单

**描述**: 将命令从黑名单移动到白名单，使其可执行

```json
{
  "action": "move_to_whitelist",
  "command_ids": ["editor.action.formatDocument", "workbench.action.files.save"]
}
```

**返回**: 移动操作结果统计

---

### Action 5: execute_command - 执行白名单命令

**描述**: 执行白名单中的命令，如果不在白名单则拒绝执行

```json
{
  "action": "execute_command",
  "command_id": "workbench.action.quickOpen",
  "args": []
}
```

**返回**: 命令执行结果或错误信息

---

### Action 6: capture_refresh - 捕获刷新命令

**描述**: 重新捕获所有VS Code命令，自动分类并更新黑白名单

```json
{
  "action": "capture_refresh"
}
```

**功能**:
- 发现新命令 → 自动添加到黑名单
- 发现失效命令 → 从所有列表中移除
- 差值计算和自动分类
- 保护MCP核心工具不被移除

**返回**: 捕获统计和分类信息

## 使用示例

### 1. 查看当前白名单
```json
{
  "tool": "commands_manager",
  "parameters": {
    "action": "list_whitelist"
  }
}
```

### 2. 检查命令是否可执行
```json
{
  "tool": "commands_manager",
  "parameters": {
    "action": "check_execution",
    "command_ids": ["editor.action.formatDocument"]
  }
}
```

### 3. 将命令移至白名单并执行
```json
// 第一步：移动到白名单
{
  "tool": "commands_manager",
  "parameters": {
    "action": "move_to_whitelist",
    "command_ids": ["editor.action.formatDocument"]
  }
}

// 第二步：执行命令
{
  "tool": "commands_manager",
  "parameters": {
    "action": "execute_command",
    "command_id": "editor.action.formatDocument"
  }
}
```

### 4. 刷新命令系统
```json
{
  "tool": "commands_manager",
  "parameters": {
    "action": "capture_refresh"
  }
}
```

## 安全机制

1. **MCP核心工具保护**: 以下命令不可从白名单移除
   - `list_all_vscode_commands`
   - `execute_vscode_command`
   - `search_commands`
   - `show_message`
   - `capture_context`
   - `add_command_to_whitelist`
   - `remove_command_from_whitelist`
   - `get_whitelisted_commands`
   - `get_available_commands`
   - `get_mcp_core_tools`
   - `reply_to_user`

2. **执行权限检查**: 只有白名单中的命令才能被执行

3. **自动分类**: 新发现的命令自动添加到黑名单，需要手动移至白名单才能执行

## 工具数量对比

- **之前**: 19个独立工具
- **现在**: 1个统一工具 + 6个action
- **优势**: 更简洁、更易管理、功能更集中

## 注意事项

1. 使用 `execute_command` 前，确保命令在白名单中
2. 使用 `capture_refresh` 会重新扫描所有命令，可能需要一些时间
3. MCP核心工具始终在白名单中且受保护
4. 插件自身注册的命令会被自动识别和分类
