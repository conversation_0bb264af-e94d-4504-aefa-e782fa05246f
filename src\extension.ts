import * as vscode from 'vscode';

// 1. 入口层 (Entry Layer)
import { register_manage_whitelist_command } from './entry/commands/manageWhitelist.js';
import { register_prompt_manager_command } from './entry/commands/promptManager.js';
import { add_new_model_config } from './entry/commands/addModelConfig.js';
import { RequestEntryPoint } from './entry/RequestEntryPoint.js';
// 2. 出口层 (Exit Layer)
import { ChatPanelProvider } from './exit/ChatPanelProvider.js';
import { CommandManagerPanel } from './exit/CommandManagerPanel.js';
import type { McpServer } from './exit/mcp_server.js';
import { start_mcp_server } from './exit/systems/McpServerBootstrap.js';
// 3. 宾语层 (Object Layer)
import { CommandStorageModule } from './object/CommandStorageModule.js';
import { ContextCapturerModule } from './object/ContextCapturerModule.js';
import { PromptSerializerModule } from './object/PromptSerializerModule.js';
// 4. 主语层 (Subject Layer)
import { ExternalLlmClientModule } from './subject/ExternalLlmClientModule.js';
import type { ModelConfig } from './subject/modelConfigs.js';
import { all_model_configs } from './subject/modelConfigs.js';
// 5. 动词层 (Verb Layer)
import { CommandExecutorModule } from './verb/CommandExecutorModule.js';
import { CommandParserModule } from './verb/CommandParserModule.js';
import { Orchestrator } from './verb/Orchestrator.js';
import { register_all_tools } from './verb/tools/registerAllTools.js';

// 全局变量
let mcp_server: McpServer | null = null;



export type AllModelConfigs = Map<string, ModelConfig>;

/**
 * 注册MCP工具对应的VS Code命令
 *
 * 🎯 解决核心问题：
 * 1. MCP工具注册后，大模型调用时服务端需要找到对应的VS Code命令来执行
 * 2. 如果没有对应的VS Code命令注册，服务端会报"命令未找到"错误
 * 3. 每个MCP工具必须有一个同名的VS Code命令注册，形成1:1映射关系
 *
 * 🔧 实现机制：
 * - 遍历所有MCP工具，为每个工具注册同名的VS Code命令
 * - VS Code命令直接调用对应MCP工具的handler
 * - 保持参数验证和错误处理的一致性
 * - 提供详细的执行日志，便于调试和监控
 *
 * 🛡️ 安全保障：
 * - 所有MCP核心工具自动加入白名单且不可移除
 * - 参数通过Zod schema验证，确保类型安全
 * - 完整的错误处理和用户反馈机制
 */
function register_mcp_tool_commands(context: vscode.ExtensionContext, all_tools: Map<string, any>): void {
  // 为每个MCP工具注册对应的VS Code命令
  for (const [tool_name, tool] of all_tools.entries()) {
    context.subscriptions.push(
      vscode.commands.registerCommand(tool_name, async (params: unknown): Promise<unknown> => {
        try {
          console.log(`🔧 执行MCP工具命令: ${tool_name}`, params);

          // 验证参数
          const validated_params = tool.parameters_schema ? tool.parameters_schema.parse(params || {}) : (params || {});

          // 执行工具处理器
          const result = await tool.handler(validated_params);

          console.log(`✅ MCP工具命令执行成功: ${tool_name}`, result);
          return result;
        } catch (error) {
          const error_message = error instanceof Error ? error.message : String(error);
          console.error(`❌ MCP工具命令执行失败: ${tool_name}`, error_message);
          void vscode.window.showErrorMessage(`MCP工具执行出错 [${tool_name}]: ${error_message}`);
          throw error;
        }
      })
    );
  }

  console.log(`📝 已注册 ${all_tools.size} 个MCP工具对应的VS Code命令`);
}

export async function activate(context: vscode.ExtensionContext): Promise<void> {
  // ======== 1. 初始化配置管理器 ========
  const { configuration_manager } = await import('./config/ConfigurationManager.js');
  await configuration_manager.initialize();

  // ======== 2. 加载配置 (宾语) ========
  const model_configs = configuration_manager.get_all_configs();
  const command_storage = new CommandStorageModule(context);

  // 注册配置管理器清理
  context.subscriptions.push({
    dispose: () => configuration_manager.dispose()
  });

  // ======== 2. 初始化核心模块 (主语、动词、宾语) ========
  const request_entry_point = new RequestEntryPoint();
  const chat_panel_provider = new ChatPanelProvider(context, request_entry_point, model_configs);
  const all_tools_map = register_all_tools(command_storage, chat_panel_provider);
  const all_tools_record = Object.fromEntries(all_tools_map.entries());

  const command_executor = new CommandExecutorModule(all_tools_map);
  const external_llm_client = new ExternalLlmClientModule(model_configs);
  const command_parser = new CommandParserModule();
  const context_capturer = new ContextCapturerModule();
  const prompt_serializer = new PromptSerializerModule();

  // ======== 3. 初始化 UI 和核心逻辑 (出口、动词) ========
  const orchestrator = new Orchestrator(
    chat_panel_provider, // 出口
    context_capturer, // 宾语
    prompt_serializer, // 宾语
    external_llm_client, // 主语
    command_parser, // 动词
    command_executor, // 动词
  );
  request_entry_point.set_orchestrator(orchestrator);

  // ======== 4. 启动 MCP 服务 (出口) ========
  mcp_server = start_mcp_server(all_tools_record);

  // ======== 5. 注册 UI 和命令 (入口) ========
  // 为每个模型动态注册独立的webview provider实例
  console.log('🔍 模型配置加载情况:', Array.from(model_configs.entries()));
  for (const [model_id, _config] of model_configs.entries()) {
    const view_type = `chatPanel-${model_id}`;
    console.log(`📝 注册webview: ${view_type} for model: ${model_id}`);

    // 为每个模型创建独立的Provider实例
    const model_specific_provider = new ChatPanelProvider(context, request_entry_point, model_configs);
    context.subscriptions.push(
      vscode.window.registerWebviewViewProvider(view_type, model_specific_provider),
    );
  }

  register_manage_whitelist_command(context, command_storage);
  register_prompt_manager_command(context, all_tools_record);

  // ======== 注册MCP工具对应的VS Code命令 ========
  register_mcp_tool_commands(context, all_tools_map);

  // ======== 注册缺失的VS Code命令 ========
  // command_manager命令 - 打开命令管理面板
  context.subscriptions.push(
    vscode.commands.registerCommand('command_manager', () => {
      CommandManagerPanel.create_or_show(context, command_storage);
    }),
  );

  // long_running_tool命令 - 长时间运行工具的示例实现
  context.subscriptions.push(
    vscode.commands.registerCommand('long_running_tool', async () => {
      void vscode.window.showInformationMessage('长时间运行工具已启动，请查看输出面板获取详细信息。');
      console.log('🔄 长时间运行工具已启动');
      // 这里可以添加具体的长时间运行逻辑
    }),
  );

  // 注册添加配置命令
  context.subscriptions.push(
    vscode.commands.registerCommand('llm-bridge.addConfig', async () => {
      await add_new_model_config(context);
    }),
  );

  // 注册设置管理器
  const { register_settings_manager } = await import('./entry/commands/settingsManager.js');
  register_settings_manager(context);





  context.subscriptions.push(
    vscode.commands.registerCommand('llm-bridge.switchChat', async (args?: { model?: string }) => {
      const model =
        args?.model ?? (await vscode.window.showInputBox({ prompt: '请输入模型英文名（如mistral、spark）' }));
      if (!model) return;

      void vscode.commands.executeCommand('workbench.action.webview.postMessage', {
        type: 'newChat',
        model,
      });
      void vscode.window.showInformationMessage(`已切换到${model}新对话，历史已归档。`);
    }),
  );
}

export function deactivate(): void {
  if (mcp_server) {
    mcp_server.stop();
  }
}
