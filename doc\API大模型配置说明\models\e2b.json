{"name": "E2B GPT-4", "provider": "e2b", "api_key": "e2b_7a0f53892160e53d10bb817491d4f04eb95a09e7", "base_url": "https://api.e2b.dev/v1/chat/completions", "api_base": "https://api.e2b.dev/v1/chat/completions", "model": "gpt-4", "alternate_models": ["gpt-4-turbo", "gpt-4-1106-preview"], "max_tokens": 8192, "temperature": 0.3, "timeout": 180, "retry_count": 3, "retry_delay": 5, "priority": 1, "system_prompt": "你是GPT-4模型，一个专业的Windows驱动程序开发专家。请利用你对内核架构、KMDF/WDM驱动模型和Windows系统开发的深入理解，对驱动程序代码进行全面分析。\n\n请特别注意：\n1. 内存管理和资源泄漏\n2. 并发问题和竞态条件\n3. IRQL级别不匹配问题\n4. 硬件交互和DMA操作\n5. 资源锁定和同步机制\n6. 可能的性能瓶颈\n\n请提供深入的中文注释和解释，并指出潜在的问题或优化建议。比如修改竞态问题，或改进驱动的可靠性和安全性的设计模式。", "code_analysis_specialist": true}