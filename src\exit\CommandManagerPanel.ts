import * as vscode from 'vscode';
import { z } from 'zod';

import type { Command, CommandStorageModule } from '../object/CommandStorageModule.js';

/**
 * Generates a random nonce string.
 * @returns A 32-character random string.
 */
function get_nonce(): string {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

// Zod schemas for type-safe message passing from the webview
const message_from_webview_schema = z.union([
  z.object({
    command: z.literal('saveWhitelist'),
    whitelist: z.array(z.string()),
  }),
  z.object({
    command: z.literal('resetWhitelist'),
  }),
  z.object({
    command: z.literal('autoSyncCommands'),
  }),
]);

/**
 * Manages the Command Manager webview panel.
 */
export class CommandManagerPanel {
  public static current_panel: CommandManagerPanel | undefined;
  public static readonly view_type = 'llmBridgeCommandManager';

  private readonly _panel: vscode.WebviewPanel;
  private readonly _extension_context: vscode.ExtensionContext;
  private readonly _command_storage: CommandStorageModule;
  private _disposables: vscode.Disposable[] = [];

  /**
   * Creates or reveals the command manager panel.
   */
  public static create_or_show(
    extension_context: vscode.ExtensionContext,
    command_storage: CommandStorageModule,
  ): void {
    const column = vscode.window.activeTextEditor?.viewColumn;

    if (CommandManagerPanel.current_panel) {
      CommandManagerPanel.current_panel._panel.reveal(column);
      return;
    }

    const panel = vscode.window.createWebviewPanel(
      CommandManagerPanel.view_type,
      'Manage Command Whitelist',
      column || vscode.ViewColumn.One,
      {
        // These must be camelCase for the VS Code API
        // eslint-disable-next-line @typescript-eslint/naming-convention
        enableScripts: true,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        localResourceRoots: [vscode.Uri.joinPath(extension_context.extensionUri, 'media')],
      },
    );

    CommandManagerPanel.current_panel = new CommandManagerPanel(panel, extension_context, command_storage);
  }

  private constructor(
    panel: vscode.WebviewPanel,
    extension_context: vscode.ExtensionContext,
    command_storage: CommandStorageModule,
  ) {
    this._panel = panel;
    this._extension_context = extension_context;
    this._command_storage = command_storage;

    // Set the webview's initial html content
    this._panel.webview.html = this._get_html_for_webview(this._panel.webview);

    // Listen for when the panel is disposed
    // This happens when the user closes the panel or when the panel is closed programmatically
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

    // Handle messages from the webview
    this._panel.webview.onDidReceiveMessage(
      async message => {
        const parsed_message = message_from_webview_schema.safeParse(message);
        if (!parsed_message.success) {
          void vscode.window.showErrorMessage(`Invalid message from webview: ${parsed_message.error.message}`);
          return;
        }

        switch (parsed_message.data.command) {
          case 'saveWhitelist':
            await this._command_storage.save_whitelist(parsed_message.data.whitelist);
            void vscode.window.showInformationMessage('Whitelist saved successfully.');
            break;
          case 'resetWhitelist':
            await this._command_storage.save_whitelist([]);
            void vscode.window.showInformationMessage('Whitelist has been reset.');
            await this._update_webview_content();
            break;
          case 'autoSyncCommands':
            await vscode.commands.executeCommand('llm-bridge.recaptureCommands');
            void vscode.window.showInformationMessage('Auto-sync complete.');
            await this._update_webview_content();
            break;
        }
      },
      null,
      this._disposables,
    );

    // Update the content based on view changes
    this._panel.onDidChangeViewState(
      () => {
        if (this._panel.visible) {
          void this._update_webview_content();
        }
      },
      null,
      this._disposables,
    );

    // Initial content update
    void this._update_webview_content();
  }

  public dispose(): void {
    CommandManagerPanel.current_panel = undefined;

    // Clean up our resources
    this._panel.dispose();

    while (this._disposables.length) {
      const x = this._disposables.pop();
      if (x) {
        x.dispose();
      }
    }
  }

  private async _update_webview_content(): Promise<void> {
    const all_commands = this._command_storage.get_all_commands();
    const whitelist_ids = new Set(this._command_storage.get_whitelist_ids());
    const mcp_core_tools = new Set(this._command_storage.get_mcp_core_tools());

    const available_commands = all_commands.filter((cmd: Command) => !whitelist_ids.has(cmd.id));
    const whitelisted_commands = all_commands.filter((cmd: Command) => whitelist_ids.has(cmd.id));

    // 为白名单命令添加MCP核心工具标识
    const enhanced_whitelisted_commands = whitelisted_commands.map(cmd => ({
      ...cmd,
      is_mcp_core: mcp_core_tools.has(cmd.id),
    }));

    await this._panel.webview.postMessage({
      command: 'updateLists',
      available: available_commands,
      whitelist: enhanced_whitelisted_commands,
      mcp_core_tools: Array.from(mcp_core_tools),
    });
  }

  private _get_html_for_webview(webview: vscode.Webview): string {
    // Local path to main script run in the webview
    const script_path_on_disk = vscode.Uri.joinPath(this._extension_context.extensionUri, 'media', 'commandManager.js');

    // And the uri we can use to load this script in the webview
    const script_uri = webview.asWebviewUri(script_path_on_disk);

    // Use a nonce to only allow specific scripts to be run
    const nonce = get_nonce();

    return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <!--
                    Use a content security policy to only allow loading images from https or from our extension directory,
                    and only allow scripts that have a specific nonce.
                -->
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; img-src ${webview.cspSource} https:; script-src 'nonce-${nonce}';">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Command Whitelist Manager</title>
                <style>
                    body { font-family: var(--vscode-font-family); color: var(--vscode-foreground); background-color: var(--vscode-editor-background); }
                    .container { display: flex; gap: 2em; }
                    .list-container { flex: 1; }
                    .filter-input { width: 100%; padding: 0.5em; margin-bottom: 1em; background-color: var(--vscode-input-background); color: var(--vscode-input-foreground); border: 1px solid var(--vscode-input-border); }
                    .command-list { list-style: none; padding: 0; height: 300px; overflow-y: auto; border: 1px solid var(--vscode-editor-widget-border); }
                    .command-list li { display: flex; align-items: center; padding: 0.5em; cursor: pointer; }
                    .command-list li:hover { background-color: var(--vscode-list-hover-background); }
                    .command-list li .id { flex-grow: 1; }
                    .move-btn { background: none; border: none; color: var(--vscode-foreground); cursor: pointer; font-size: 1.2em; }
                    .button-container { margin-top: 1em; display: flex; gap: 1em; }
                    .button-container button { padding: 0.5em 1em; background-color: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; cursor: pointer; }
                    .button-container button:hover { background-color: var(--vscode-button-hover-background); }
                </style>
            </head>
            <body>
                <h1>Manage Command Whitelist</h1>
                <div class="container">
                    <div class="list-container">
                        <h2>Available Commands (<span id="available-count">0</span>)</h2>
                        <input type="text" id="filter-available" class="filter-input" placeholder="Filter available commands...">
                        <ul id="available-list" class="command-list"></ul>
                    </div>
                    <div class="list-container">
                        <h2>Whitelisted Commands (<span id="whitelist-count">0</span>)</h2>
                        <input type="text" id="filter-whitelist" class="filter-input" placeholder="Filter whitelisted commands...">
                        <ul id="whitelist-list" class="command-list"></ul>
                    </div>
                </div>
                <div class="button-container">
                    <button id="save-button">Save Whitelist</button>
                    <button id="reset-button">Reset Whitelist</button>
                    <button id="autosync-button">Auto-Sync with VS Code Commands</button>
                </div>
                <script nonce="${nonce}" src="${script_uri.toString()}"></script>
            </body>
            </html>`;
  }
}
