<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VS Code 插件命令捕获测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #007acc;
            margin-top: 0;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007acc;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .loading {
            color: #007acc;
            font-style: italic;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007acc;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>VS Code 插件命令捕获测试</h1>
    
    <div class="container">
        <div class="test-section">
            <h3>1. 基础命令捕获测试</h3>
            <button onclick="testBasicCapture()">获取所有命令</button>
            <button onclick="testAnalyzeCommands()">分析插件命令</button>
            <button onclick="testGenerateReport()">生成详细报告</button>
            <div id="basicResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 插件命令搜索</h3>
            <input type="text" id="searchQuery" placeholder="输入搜索关键词，如：eslint, git, python">
            <button onclick="testSearchCommands()">搜索命令</button>
            <div id="searchResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 特定插件命令</h3>
            <input type="text" id="pluginName" placeholder="输入插件名称，如：eslint, git, typescript">
            <button onclick="testGetPluginCommands()">获取插件命令</button>
            <div id="pluginResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 命令统计概览</h3>
            <div id="statsContainer" class="stats" style="display: none;"></div>
        </div>
    </div>

    <script>
        const MCP_SERVER_URL = 'http://localhost:3000';

        async function callMcpTool(toolName, arguments = {}) {
            try {
                const response = await fetch(`${MCP_SERVER_URL}/invoke`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: toolName,
                        arguments: arguments
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result.content[0].text;
            } catch (error) {
                throw new Error(`调用MCP工具失败: ${error.message}`);
            }
        }

        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = content;
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = '正在加载...';
        }

        async function testBasicCapture() {
            showLoading('basicResult');
            try {
                const result = await callMcpTool('list_all_vscode_commands');
                const commands = JSON.parse(result);
                showResult('basicResult', `总命令数量: ${commands.length}\n\n前20个命令:\n${commands.slice(0, 20).join('\n')}`);
            } catch (error) {
                showResult('basicResult', `错误: ${error.message}`, true);
            }
        }

        async function testAnalyzeCommands() {
            showLoading('basicResult');
            try {
                const result = await callMcpTool('analyze_plugin_commands');
                const analysis = JSON.parse(result);
                
                let output = `=== VS Code 命令分析报告 ===\n\n`;
                output += `总命令数量: ${analysis.totalCommands}\n`;
                output += `内置命令数量: ${analysis.builtinCommands}\n`;
                output += `扩展命令数量: ${analysis.extensionCommands}\n`;
                output += `活跃插件数量: ${analysis.activePlugins}\n\n`;
                
                output += `=== 前10个插件 ===\n`;
                analysis.topPlugins.forEach((plugin, index) => {
                    output += `${index + 1}. ${plugin.name}: ${plugin.commandCount} 个命令\n`;
                    output += `   示例命令: ${plugin.sampleCommands.join(', ')}\n\n`;
                });
                
                showResult('basicResult', output);
                
                // 显示统计卡片
                showStats(analysis);
            } catch (error) {
                showResult('basicResult', `错误: ${error.message}`, true);
            }
        }

        async function testGenerateReport() {
            showLoading('basicResult');
            try {
                const result = await callMcpTool('generate_command_report');
                const report = JSON.parse(result);
                
                let output = `=== VS Code 命令详细报告 ===\n\n`;
                output += `总命令数量: ${report.summary.totalCommands}\n`;
                output += `内置命令数量: ${report.summary.builtinCommands}\n`;
                output += `扩展命令数量: ${report.summary.extensionCommands}\n`;
                output += `活跃插件数量: ${report.summary.activePlugins}\n\n`;
                
                output += `=== 命令类别统计 ===\n`;
                Object.entries(report.categories).forEach(([category, count]) => {
                    output += `${category}: ${count} 个命令\n`;
                });
                
                output += `\n=== 前10个插件 ===\n`;
                report.topPlugins.forEach((plugin, index) => {
                    output += `${index + 1}. ${plugin.name}: ${plugin.commandCount} 个命令\n`;
                    output += `   示例: ${plugin.sampleCommands.join(', ')}\n\n`;
                });
                
                showResult('basicResult', output);
            } catch (error) {
                showResult('basicResult', `错误: ${error.message}`, true);
            }
        }

        async function testSearchCommands() {
            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                showResult('searchResult', '请输入搜索关键词', true);
                return;
            }

            showLoading('searchResult');
            try {
                const result = await callMcpTool('search_plugin_commands', { query });
                const searchResult = JSON.parse(result);
                
                let output = `=== 搜索 "${query}" 的结果 ===\n\n`;
                output += `找到 ${searchResult.resultCount} 个匹配的命令\n\n`;
                
                searchResult.commands.forEach((cmd, index) => {
                    output += `${index + 1}. ${cmd.id}\n`;
                    output += `   插件: ${cmd.pluginName}\n`;
                    output += `   类别: ${cmd.category}\n`;
                    output += `   类型: ${cmd.isBuiltin ? '内置' : '扩展'}\n\n`;
                });
                
                showResult('searchResult', output);
            } catch (error) {
                showResult('searchResult', `错误: ${error.message}`, true);
            }
        }

        async function testGetPluginCommands() {
            const pluginName = document.getElementById('pluginName').value.trim();
            if (!pluginName) {
                showResult('pluginResult', '请输入插件名称', true);
                return;
            }

            showLoading('pluginResult');
            try {
                const result = await callMcpTool('get_plugin_commands', { plugin_name: pluginName });
                const pluginResult = JSON.parse(result);
                
                let output = `=== ${pluginResult.pluginName} 插件命令 ===\n\n`;
                output += `总命令数量: ${pluginResult.commandCount}\n\n`;
                
                pluginResult.commands.forEach((cmd, index) => {
                    output += `${index + 1}. ${cmd.id}\n`;
                    output += `   命令名: ${cmd.commandName}\n`;
                    output += `   类别: ${cmd.category}\n\n`;
                });
                
                showResult('pluginResult', output);
            } catch (error) {
                showResult('pluginResult', `错误: ${error.message}`, true);
            }
        }

        function showStats(analysis) {
            const container = document.getElementById('statsContainer');
            container.style.display = 'grid';
            
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${analysis.totalCommands}</div>
                    <div class="stat-label">总命令数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.builtinCommands}</div>
                    <div class="stat-label">内置命令</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.extensionCommands}</div>
                    <div class="stat-label">扩展命令</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${analysis.activePlugins}</div>
                    <div class="stat-label">活跃插件</div>
                </div>
            `;
        }

        // 页面加载时自动测试连接
        window.addEventListener('load', async () => {
            try {
                await testAnalyzeCommands();
            } catch (error) {
                console.log('MCP服务器未启动或连接失败:', error.message);
            }
        });
    </script>
</body>
</html> 