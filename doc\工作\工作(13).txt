每个 LLM API（如 mistral、openai、chatglm 等）都能自动拥有你插件里定义的所有 MCP 工具（如 list_all_vscode_commands、prompt_manager、manage_whitelist_command 等），并且每个工具的 handler 都自动绑定各自的 API 配置，实现“多工人多工具多后端”统一自动化。
1. 实现思路总结
你已经有一套很清晰的 MCP 工具定义（本地 VS Code 插件功能）。
你希望每个 LLM API 都能“远程调用”这些 MCP 工具，即每个工人都能用同一套“VS Code 能力”。
只要在 api_config.json 里新增一个 API，系统就会自动为它注册一整套 MCP 工具，handler 里自动绑定自己的 API 配置。
2. 实现方式建议
A. 工具注册逻辑
启动时读取 api_config.json，遍历每个 API 配置。
为每个 API 动态注册一套 MCP 工具（如 list_all_vscode_commands、prompt_manager 等）。
工具名可加前缀（如 mistral_list_all_vscode_commands），也可不加（如果你有工人隔离机制）。
B. handler 绑定 API 配置
每个工具的 handler 内部，自动用当前 API 的 base_url、api_key、model 调用 LLM 或远程服务。
这样每个工人都能用同一套 MCP 工具，但 handler 实际调用的是“自己”API的后端。
C. 插件端自动发现和调用
插件端通过 MCP 协议自动发现所有工人的所有工具，UI/自动化流程可灵活分流和调用。
读取 api_config.json，获取所有 API 配置。
为每个 API 动态生成一套 MCP 工具（如 list_all_vscode_commands、prompt_manager 等）。
工具名可加前缀（如 mistral_list_all_vscode_commands），防止冲突。
每个工具的 handler 内部自动绑定当前 API 的 base_url、api_key、model。
handler 里通过 axios/fetch 调用各自的 LLM API 或远程 MCP 服务。
所有工具注册到 MCP Server，插件端自动发现和调用。
启动时自动读取 config/api_config.json，遍历每个 API 配置。
为每个 API 动态注册一套 MCP 工具（如 list_all_vscode_commands、prompt_manager、manage_whitelist_command），工具名加上 API 前缀，handler 自动绑定各自 API 配置。
所有工具注册到 MCP Server，实现多工人多工具多后端自动化。
保留 UI、命令面板等原有功能，后续可继续扩展更多 MCP 工具。
你只需维护 MCP 工具模板和 api_config.json，系统即可自动支持多工人多后端，所有 handler 都自动调用各自 API。
如需扩展更多工具、参数 schema 或有特殊业务需求，随时告知！


你的 api_config.json 已经升级为多 endpoint+多 model 结构，下一步就是让 handler 路由逻辑支持“按工具/能力自动分发到不同 endpoint 和 model”。
下面给出通用设计思路、伪代码结构、关键注意点，并结合 VS Code 插件/Node.js 场景举例。
1. 核心目标
handler 能根据“工具类型/能力”自动选择正确的 endpoint 和 model
支持未来扩展（如新增 OCR、Agent、批处理等）
代码结构清晰、易维护
2. 推荐 handler 路由逻辑设计
A. 统一入口：根据工具类型分发
假设你有如下 handler 入口（伪代码/TypeScript）：
async function handleLlmRequest(toolType: string, payload: any) {
    // 1. 读取 api_config.json
    const config = getApiConfig('mistral');
    // 2. 路由到对应 endpoint 和 model
    const endpoint = config.endpoints[toolType];
    const model = config.models[toolType];
    if (!endpoint || !model) {
        throw new Error(`不支持的工具类型: ${toolType}`);
    }
    // 3. 构造请求体
    const requestBody = buildRequestBody(toolType, payload, model);
    // 4. 发起 HTTP 请求
    const result = await axios.post(endpoint, requestBody, {
        headers: { 'Authorization': `Bearer ${config.api_key}` }
    });
    return result.data;
}
B. 构造请求体的分支
function buildRequestBody(toolType: string, payload: any, model: string) {
    switch (toolType) {
        case 'chat':
            return {
                model,
                messages: payload.messages,
                ...payload.options // 其他参数
            };
        case 'embedding':
            return {
                model,
                input: payload.input
            };
        case 'fim':
            return {
                model,
                prompt: payload.prompt,
                suffix: payload.suffix
            };
        case 'moderation':
            return {
                model,
                input: payload.input
            };
        // 可扩展更多类型
        default:
            throw new Error(`不支持的工具类型: ${toolType}`);
    }
}
C. 工具注册与 handler 绑定
工具注册时，给每个工具定义 toolType 字段（如 chat/embedding/fim/moderation/agent）
handler 只需根据 toolType 自动路由，无需硬编码 endpoint/model
3. 典型调用流程举例
用户/秘书/工人调用 smart_infer 工具（toolType=chat），handler 自动走 chat endpoint
调用 embedding 工具，handler 自动走 embedding endpoint
调用 fim 工具，handler 自动走 fim endpoint
调用 moderation 工具，handler 自动走 moderation endpoint
4. 关键注意点
配置解耦：所有 endpoint/model 都从 config 读取，方便后续扩展和维护
错误处理：未配置的 toolType 要有清晰报错
易扩展：新增工具类型只需在 config 和 buildRequestBody 里加分支即可
安全性：api_key 只在 handler 内部使用，避免泄漏
5. 进阶建议
支持多工人时，handler 可根据 api_name+toolType 二级路由
支持 MCP 工具箱时，MCP 工人 handler 走 MCP 协议，LLM 工人 handler 走 LLM API
可加日志输出、调用统计、异常捕获等增强健壮性
6. 伪代码结构总结
// 入口
handleLlmRequest(toolType, payload)
// 路由
endpoint = config.endpoints[toolType]
model = config.models[toolType]
// 构造请求体
requestBody = buildRequestBody(toolType, payload, model)
// 发请求
axios.post(endpoint, requestBody, ...)


核心目标
所有能力（工具/接口/模型）都由配置驱动，无需硬编码
handler 路由自动适配配置，支持未来任意 API/能力扩展
新增/修改/下线能力只需改配置和少量分支代码
支持多工人/多后端/多种 LLM/MCP/自定义 API
2. 配置驱动的设计思想
A. 配置结构建议
每个 API/工人一条配置，包含 endpoints、models、可选参数
endpoints、models、参数都用 key-value，key 为能力/工具类型
支持未来任意能力扩展（如 ocr、agent、batch、customX）
B. handler 路由自动化
handler 只需根据 toolType（能力名）查配置
endpoint/model/参数都从配置查找
新增能力只需加配置和 buildRequestBody 分支
3. handler 路由伪代码（高度可扩展）
async function handleRequest(apiName, toolType, payload) {
    // 1. 查找 API 配置
    const config = getApiConfig(apiName);
    if (!config) throw new Error(`未找到API: ${apiName}`);

    // 2. 查找 endpoint/model
    const endpoint = config.endpoints[toolType];
    const model = config.models[toolType];
    if (!endpoint) throw new Error(`未配置endpoint: ${toolType}`);

    // 3. 构造请求体（支持自定义扩展）
    const requestBody = buildRequestBody(toolType, payload, model, config);

    // 4. 发请求
    const headers = {
        'Authorization': `Bearer ${config.api_key}`,
        ...config.extra?.custom_headers
    };
    const result = await axios.post(endpoint, requestBody, { headers, timeout: config.extra?.timeout || 30 * 1000 });
    return result.data;
}
buildRequestBody 只需为每种 toolType 写一分支，未来可用插件/模板自动生成。
4. 未来扩展场景举例
新增“文档OCR”能力：只需在 endpoints/models 里加 "ocr": "..."
新增“自定义API”能力：加 "customX": "..."，handler 自动支持
切换模型/参数：只需改配置，无需动代码
多工人/多后端：配置多条，handler 自动分流
5. 最佳实践建议
配置优先：所有能力、路由、参数都走配置，代码只做分发和兜底
分支最小化：buildRequestBody 只做能力适配，其他都靠配置
日志与异常：每次路由、请求、异常都输出日志，便于调试和维护
文档同步：配置结构、支持能力、参数说明要有文档，便于团队协作
6. 执行前Checklist
[x] 配置结构是否支持未来任意能力扩展？
[x] handler 路由是否完全由配置驱动？
[x] 新增/修改/下线能力是否只需改配置和少量分支？
[x] 是否支持多工人/多后端/多API？
[x] 是否有异常兜底和日志输出？

所有 LLM 能力（chat/embedding/fim/moderation/ocr/agent等）都可通过统一 handler（callLlmApi）自动路由到各自 endpoint/model，只需配置，无需硬编码。
UI 层、消息处理、handler 路由全部升级为“配置优先”模式，后续扩展/切换/下线能力只需改配置和极少分支代码。
主要变更点与检查
src/llm/llm_client.ts
重构为 callLlmApi(toolType, payload, apiConfig)，支持多能力自动路由，所有参数/路由/模型均由配置驱动。
支持 chat/embedding/fim/moderation/ocr/agent 等能力，未来可继续扩展。
保持异常处理、日志输出，便于调试和维护。
src/extension.ts
UI 消息处理统一调用新接口，自动查找对应 apiConfig，构造 toolType/payload，调用 handler。
兼容原有功能，支持未来多工人多工具多后端自动化。
只需在 api_config.json 里增删改 API，系统即可自动适配。
配置与类型
api_config.json 结构已支持多 endpoint、多 model、多能力，未来可直接扩展。
类型定义、handler 入口、payload 构造均已适配新模式。