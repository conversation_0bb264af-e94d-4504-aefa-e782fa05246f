<!DOCTYPE html>
<html>
<head>
    <title>LLM Bridge MCP Server Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px; background-color: #f4f7f9; color: #333; }
        h1, h2 { color: #005a9c; border-bottom: 2px solid #005a9c; padding-bottom: 10px; }
        .container { background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; }
        label { font-weight: bold; margin-right: 10px; }
        input[type="text"] { width: 400px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; }
        button { padding: 10px 20px; font-size: 16px; cursor: pointer; background-color: #0078d4; color: white; border: none; border-radius: 4px; margin: 5px; transition: background-color 0.3s; }
        button:hover { background-color: #005a9c; }
        #output { margin-top: 20px; padding: 15px; border: 1px solid #ccc; min-height: 200px; max-height: 500px; overflow-y: auto; background-color: #eef2f5; white-space: pre-wrap; font-family: "Courier New", Courier, monospace; border-radius: 4px; }
        .button-group { margin-top: 15px; }
    </style>
</head>
<body>
    <h1>LLM Bridge MCP Server Test</h1>

    <div class="container">
        <h2>Configuration</h2>
        <label for="serverUrlInput">Server URL:</label>
        <input type="text" id="serverUrlInput" value="http://localhost:3000">
    </div>

    <div class="container">
        <h2>Actions</h2>
        <div class="button-group">
            <button id="listResourcesBtn">1. List Available Tools (/offerings)</button>
            <button id="invokeToolBtn">2. Invoke 'list_workers' Tool (/invoke)</button>
        </div>
    </div>

    <div class="container">
        <h2>Output</h2>
        <div id="output">Ready to test. Please ensure the LLM Bridge extension is running in VS Code.</div>
    </div>

    <script>
        const serverUrlInput = document.getElementById('serverUrlInput');
        const output = document.getElementById('output');
        const listResourcesBtn = document.getElementById('listResourcesBtn');
        const invokeToolBtn = document.getElementById('invokeToolBtn');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearLog() {
            output.textContent = '';
        }

        async function listResources() {
            clearLog();
            const baseUrl = serverUrlInput.value;
            const url = `${baseUrl}/offerings`;
            log(`Fetching available tools from ${url}...`);

            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                log('Success! Available tools:');
                log(JSON.stringify(data, null, 2));
            } catch (error) {
                log(`Error fetching resources: ${error.message}`);
                log('Is the LLM Bridge extension running? Is the URL correct?');
                console.error(error);
            }
        }

        async function invokeTool() {
            clearLog();
            const baseUrl = serverUrlInput.value;
            const url = `${baseUrl}/invoke`;
            const payload = {
                tool: 'list_workers',
                parameters: {}
            };
            log(`Invoking tool 'list_workers' at ${url}...`);
            log(`Sending payload: ${JSON.stringify(payload)}`);

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify(payload),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                log('Resources list received:');
                log(JSON.stringify(data, null, 2));

            } catch (error) {
                log(`Error: ${error.message}`);
                console.error('Error:', error);
            }
        }

        listResourcesBtn.addEventListener('click', listResources);
        invokeToolBtn.addEventListener('click', invokeTool);
    </script>
</body>
</html>
