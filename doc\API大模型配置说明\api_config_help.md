# API配置帮助文档

本文档提供了各类API集成的详细配置说明，帮助您正确设置和使用不同的AI服务提供商。

## 配置文件结构

API配置文件(`config/api_config.json`)包含与各种AI服务提供商集成所需的配置信息。以下是每个字段的说明：

| 字段名 | 描述 |
|--------|------|
| `name` | API提供商名称，用于在代码中引用 |
| `api_key` | API密钥，需要替换为您的实际密钥 |
| `base_url` | API请求的基础URL |
| `model` | 使用的模型名称 |
| `max_tokens` | 单次请求的最大令牌数 |
| `cost_per_1k_tokens` | 每1000个令牌的费用(美元) |
| `max_requests_per_minute` | 速率限制(每分钟最大请求数) |

## 支持的API提供商

### 1. OpenAI

```json
{
    "name": "openai",
    "api_key": "your-openai-api-key",
    "base_url": "https://api.openai.com/v1/chat/completions",
    "model": "gpt-4",
    "max_tokens": 2000,
    "cost_per_1k_tokens": 0.03,
    "max_requests_per_minute": 60
}
```

**说明：**
- **可选的替代基础URL**:
  - 标准OpenAI: `https://api.openai.com/v1/chat/completions`
  - ChatAnywhere: `https://api.chatanywhere.tech`
  - API36: `https://free.v36.cm`
- **可用模型**:
  - `gpt-4o` (最新通用模型)
  - `gpt-4o-mini` (更经济的选择)
  - `gpt-4` (较早的模型)
  - `gpt-3.5-turbo` (经济实惠)

### 2. Anthropic Claude

```json
{
    "name": "anthropic",
    "api_key": "your-anthropic-api-key",
    "base_url": "https://api.anthropic.com/v1/messages",
    "model": "claude-3-opus-20240229",
    "max_tokens": 4000,
    "cost_per_1k_tokens": 0.015,
    "max_requests_per_minute": 50
}
```

**说明：**
- **可用模型**:
  - `claude-3-opus-20240229` (最高性能)
  - `claude-3-sonnet-20240229` (性能与成本的平衡)
  - `claude-3-haiku-20240307` (最快速)

### 3. Mistral AI

```json
{
    "name": "mistral",
    "api_key": "your-mistral-api-key",
    "base_url": "https://api.mistral.ai/v1/chat/completions",
    "model": "mistral-large-latest",
    "max_tokens": 2000,
    "cost_per_1k_tokens": 0.008,
    "max_requests_per_minute": 40
}
```

**说明：**
- **可用模型**:
  - `mistral-large-latest` (最高性能)
  - `mistral-medium-latest` (中等性能)
  - `mistral-small-latest` (最经济)
  - `open-mistral-7b` (开源模型)

### 4. DeepSeek

```json
{
    "name": "deepseek",
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com/v1/chat/completions",
    "model": "deepseek-chat",
    "max_tokens": 4000,
    "cost_per_1k_tokens": 0.01,
    "max_requests_per_minute": 45
}
```

**说明：**
- DeepSeek模型支持中英双语，特别适合中文应用场景
- 提供了强大的代码理解和生成能力

### 5. Google Gemini

```json
{
    "name": "gemini",
    "api_key": "zaSyB66wIbZKJJR4fgrgf59CKkaELXG67WDdE",
    "base_url": "https://generativelanguage.googleapis.com/v1beta/models/",
    "model": "gemini-2.0-pro",
    "max_tokens": 8192,
    "cost_per_1k_tokens": 0.007,
    "max_requests_per_minute": 60
}
```

**说明：**
- **可用模型**:
  - `gemini-2.0-pro` (最新)
  - `gemini-2.0-flash` (更快、更经济)
  - `gemini-1.5-pro` (之前版本)
- **请求格式示例**:
```bash
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=API_KEY" \
-H 'Content-Type: application/json' \
-X POST \
-d '{
  "contents": [{
    "parts":[{"text": "Explain how AI works"}]
    }]
   }'
```

### 6. OpenRouter (多模型访问)

```json
{
    "name": "openrouter",
    "api_key": "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",
    "base_url": "https://openrouter.ai/api/v1/chat/completions",
    "model": "google/gemini-2.0-pro-exp-02-05:free",
    "max_tokens": 4000,
    "cost_per_1k_tokens": 0.0,
    "max_requests_per_minute": 10
}
```

**说明：**
- **可用模型示例**:
  - `google/gemini-2.0-pro-exp-02-05:free`
  - `anthropic/claude-3-opus-28k:latest`
  - `meta-llama/llama-3-70b-instruct:latest`
- 完整模型列表请参考: https://openrouter.ai/docs
- **调用示例**:
```python
from openai import OpenAI

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="<OPENROUTER_API_KEY>",
)

completion = client.chat.completions.create(
  extra_headers={
    "HTTP-Referer": "<YOUR_SITE_URL>", # 可选
    "X-Title": "<YOUR_SITE_NAME>", # 可选
  },
  extra_body={},
  model="google/gemini-2.0-pro-exp-02-05:free",
  messages=[
    {
      "role": "user",
      "content": "写一个Python函数来计算斐波那契数列"
    }
  ]
)
```

### 7. Cloudflare Workers AI

```json
{
    "name": "cloudflare",
    "api_key": "c3bca45b1fb78720ccbae68aab295b064a77b",
    "base_url": "https://api.cloudflare.com/client/v4/accounts/edfc96ded4826f3aed180debc6331126/ai/run/",
    "model": "@cf/meta/llama-3.1-8b-instruct",
    "max_tokens": 4096,
    "cost_per_1k_tokens": 0.0,
    "max_requests_per_minute": 50
}
```

**说明：**
- **可用模型**:
  - `@cf/meta/llama-3-8b-instruct`
  - `@cf/meta/llama-3.1-8b-instruct`
  - `@cf/mistral/mistral-7b-instruct`
- **调用示例**:
```bash
curl -X POST \
  https://api.cloudflare.com/client/v4/accounts/{ACCOUNT_ID}/ai/run/@cf/meta/llama-3.1-8b-instruct \
  -H "Authorization: Bearer {API_TOKEN}" \
  -d '{ "prompt": "Where did the phrase Hello World come from" }'
```

### 8. GLHF Chat (Hugging Face模型接口)

```json
{
    "name": "glhf",
    "api_key": "glhf_a445936b32812a4d03ad7599e4da50f0",
    "base_url": "https://api.glhf.chat/v1/chat/completions",
    "model": "hf:mistralai/Mistral-7B-Instruct-v0.3",
    "max_tokens": 2048,
    "cost_per_1k_tokens": 0.0,
    "max_requests_per_minute": 20
}
```

**说明：**
- **模型格式**:
  - 必须以`hf:`开头，例如:
  - `hf:meta-llama/llama-3.1-405B-Instruct`
  - `hf:mistralai/Mistral-7B-Instruct-v0.3`
- GLHF提供了对Hugging Face模型的简便访问
- 支持OpenAI兼容的API接口，可以使用OpenAI客户端库

## 安全注意事项

1. **API密钥安全**:
   - 不要在公共代码库中存储真实API密钥
   - 考虑使用环境变量或专用密钥管理服务
   - 定期更新API密钥，特别是在怀疑泄露的情况下

2. **速率限制**:
   - 遵守各服务提供商设置的速率限制
   - 实现重试机制和退避策略
   - 监控API使用情况，避免意外账单

3. **数据安全**:
   - 注意不要向API发送敏感或个人识别信息
   - 了解各提供商的数据隐私政策
   - 考虑数据在传输和处理过程中的加密需求

## 故障排除

**常见错误类型**:

1. **认证错误**: 通常表示API密钥错误或过期
2. **速率限制错误**: 请求频率超过限制
3. **模型不可用**: 指定的模型名称不正确或该模型当前不可用
4. **请求格式错误**: 请求参数不符合API要求
5. **令牌限制错误**: 请求或响应超出最大令牌限制

**解决步骤**:

1. 验证API密钥是否正确
2. 检查API端点URL是否正确
3. 确认API请求格式是否符合文档要求
4. 检查是否达到速率限制或令牌限制
5. 查看服务提供商状态页面是否存在已知问题

## 更多资源

- [OpenAI API文档](https://platform.openai.com/docs/api-reference)
- [Anthropic Claude API文档](https://docs.anthropic.com/claude/reference/getting-started-with-the-api)
- [Mistral AI文档](https://docs.mistral.ai/)
- [DeepSeek API文档](https://platform.deepseek.com/)
- [Google Generative AI文档](https://ai.google.dev/docs)
- [OpenRouter文档](https://openrouter.ai/docs)
- [Cloudflare Workers AI文档](https://developers.cloudflare.com/workers-ai/)
- [GLHF Chat文档](https://glhf.chat/docs)
