{"jsonrpc": "2.0", "result": ["noop", "undo", "default:undo", "redo", "default:redo", "editor.action.selectAll", "columnSelect", "cursorColumnSelectLeft", "cursorColumnSelectRight", "cursorColumnSelectUp", "cursorColumnSelectPageUp", "cursorColumnSelectDown", "cursorColumnSelectPageDown", "cursor<PERSON>ove", "cursor<PERSON><PERSON>t", "cursorLeftSelect", "cursorRight", "cursorRightSelect", "cursorUp", "cursorUpSelect", "cursorPageUp", "cursorPageUpSelect", "cursorDown", "cursorDownSelect", "cursorPageDown", "cursorPageDownSelect", "createCursor", "cursorHome", "cursorHomeSelect", "cursorLineStart", "cursorLineStartSelect", "cursorEnd", "cursorEndSelect", "cursorLineEnd", "cursorLineEndSelect", "cursorTop", "cursorTopSelect", "cursor<PERSON>ott<PERSON>", "cursorBottomSelect", "editorS<PERSON>roll", "scrollLineUp", "scrollPageUp", "scrollEditorTop", "scrollLineDown", "scrollPageDown", "scrollEditorBottom", "scrollLeft", "scrollRight", "lastCursorWordSelect", "lastCursorLineSelect", "lastCursorLineSelectDrag", "cancelSelection", "removeSecondaryCursors", "revealLine", "setSelection", "lineBreakInsert", "outdent", "tab", "deleteLeft", "deleteRight", "default:type", "type", "default:replacePreviousChar", "replacePreviousChar", "default:compositionType", "compositionType", "default:compositionStart", "compositionStart", "default:compositionEnd", "compositionEnd", "default:paste", "paste", "default:cut", "cut", "diffEditor.toggleCollapseUnchangedRegions", "diffEditor.toggleShowMovedCodeBlocks", "diffEditor.toggleUseInlineViewWhenSpaceIsLimited", "diffEditor.revert", "diffEditor.switchSide", "diffEditor.exitCompareMove", "diffEditor.collapseAllUnchangedRegions", "diffEditor.showAllUnchangedRegions", "editor.action.diffReview.next", "editor.action.accessibleDiffViewer.next", "editor.action.diffReview.prev", "editor.action.accessibleDiffViewer.prev", "editor.action.setSelectionAnchor", "editor.action.goToSelectionAnchor", "editor.action.selectFromAnchorToCursor", "editor.action.cancelSelectionAnchor", "editor.action.selectToBracket", "editor.action.jumpToBracket", "editor.action.removeBrackets", "editor.action.moveCarretLeftAction", "editor.action.moveCarretRightAction", "editor.action.transposeLetters", "editor.cancelOperation", "leaveEditorMessage", "hideCodeActionWidget", "selectPrevCodeAction", "selectNextCodeAction", "acceptSelectedCodeAction", "previewSelectedCodeAction", "editor.action.clipboardCutAction", "editor.action.clipboardCopyAction", "editor.action.clipboardPasteAction", "editor.action.clipboardCopyWithSyntaxHighlightingAction", "editor.action.quickFix", "editor.action.refactor", "editor.action.sourceAction", "editor.action.organizeImports", "editor.action.autoFix", "editor.action.fixAll", "editor.action.codeAction", "codelens.showLensesInCurrentLine", "togglePeekWidgetFocus", "goToNextReference", "goToPreviousReference", "goToNextReferenceFromEmbeddedEditor", "goToPreviousReferenceFromEmbeddedEditor", "closeReferenceSearchEditor", "closeReferenceSearch", "revealReference", "openReferenceToSide", "openReference", "editor.gotoNextSymbolFromResult", "editor.gotoNextSymbolFromResult.cancel", "editor.action.goToDeclaration", "editor.action.revealDefinition", "editor.action.openDeclarationToTheSide", "editor.action.revealDefinitionAside", "editor.action.previewDeclaration", "editor.action.peekDefinition", "editor.action.revealDeclaration", "editor.action.peekDeclaration", "editor.action.goToTypeDefinition", "editor.action.peekTypeDefinition", "editor.action.goToImplementation", "editor.action.peekImplementation", "editor.action.goToReferences", "editor.action.referenceSearch.trigger", "editor.action.goToLocations", "editor.action.peekLocations", "editor.action.findReferences", "editor.action.showReferences", "editor.action.hideColorPicker", "editor.action.insertColorWithStandaloneColorPicker", "editor.action.showOrFocusStandaloneColorPicker", "editor.action.commentLine", "editor.action.addCommentLine", "editor.action.removeCommentLine", "editor.action.blockComment", "editor.action.showContextMenu", "cursorUndo", "cursorRedo", "editor.changePasteType", "editor.hidePasteWidget", "editor.action.pasteAs", "editor.action.pasteAsText", "editor.changeDropType", "editor.hideDropW<PERSON>t", "history.showPrevious", "history.showNext", "actions.find", "editor.action.startFindReplaceAction", "editor.actions.findWithArgs", "actions.findWithSelection", "editor.action.nextMatchFindAction", "editor.action.previousMatchFindAction", "editor.action.goToMatchFindAction", "editor.action.nextSelectionMatchFindAction", "editor.action.previousSelectionMatchFindAction", "closeFindWidget", "toggleFindCaseSensitive", "toggleFindWholeWord", "toggleFindRegex", "toggleFindInSelection", "togglePreserveCase", "editor.action.replaceOne", "editor.action.replaceAll", "editor.action.selectAllMatches", "editor.unfold", "editor.unfoldRecursively", "editor.fold", "editor.foldRecursively", "editor.toggleFoldRecursively", "editor.<PERSON><PERSON><PERSON>", "editor.unfold<PERSON>ll", "editor.fold<PERSON>llBlockComments", "editor.foldAllMarkerRegions", "editor.unfoldAllMarkerRegions", "editor.foldAllExcept", "editor.unfoldAllExcept", "editor.toggleFold", "editor.gotoParentFold", "editor.gotoPreviousFold", "editor.gotoNextFold", "editor.createFoldingRangeFromSelection", "editor.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.toggleImportFold", "editor.foldLevel1", "editor.foldLevel2", "editor.foldLevel3", "editor.foldLevel4", "editor.foldLevel5", "editor.foldLevel6", "editor.foldLevel7", "editor.action.fontZoomIn", "editor.action.fontZoomOut", "editor.action.fontZoomReset", "editor.action.formatDocument", "editor.action.formatSelection", "editor.action.format", "jumpToNextSnippetPlaceholder", "jumpToPrevSnippetPlaceholder", "leaveSnippet", "acceptSnippet", "editor.action.triggerSuggest", "acceptSelectedSuggestion", "acceptAlternativeSelectedSuggestion", "acceptSelectedSuggestionOnEnter", "hideSuggestWidget", "selectNextSuggestion", "selectNextPageSuggestion", "selectLastSuggestion", "selectPrevSuggestion", "selectPrevPageSuggestion", "selectFirstSuggestion", "focusSuggestion", "focusAndAcceptSuggestion", "toggleSuggestionDetails", "toggleExplainMode", "toggleSuggestionFocus", "insertBestCompletion", "insertNextSuggestion", "insertPrevSuggestion", "editor.action.resetSuggestSize", "editor.action.inlineSuggest.trigger", "editor.action.inlineSuggest.triggerInlineEditExplicit", "editor.action.inlineSuggest.triggerInlineEdit", "editor.action.inlineSuggest.showNext", "editor.action.inlineSuggest.showPrevious", "editor.action.inlineSuggest.acceptNextWord", "editor.action.inlineSuggest.acceptNextLine", "editor.action.inlineSuggest.commit", "editor.action.inlineSuggest.toggleShowCollapsed", "editor.action.inlineSuggest.hide", "editor.action.inlineSuggest.jump", "editor.action.inlineSuggest.toggleAlwaysShowToolbar", "editor.action.inlineSuggest.dev.extractRepro", "editor.action.marker.next", "editor.action.marker.prev", "editor.action.marker.nextInFiles", "editor.action.marker.prevInFiles", "closeMarkersNavigation", "editor.action.debugEditorGpuRenderer", "editor.action.showHover", "editor.action.showDefinitionPreviewHover", "editor.action.hideHover", "editor.action.scrollUpHover", "editor.action.scrollDownHover", "editor.action.scrollLeftHover", "editor.action.scrollRightHover", "editor.action.pageUpHover", "editor.action.pageDownHover", "editor.action.goToTopHover", "editor.action.goToBottomHover", "editor.action.increaseHoverVerbosityLevel", "editor.action.decreaseHoverVerbosityLevel", "editor.action.indentationToSpaces", "editor.action.indentationToTabs", "editor.action.indentUsingTabs", "editor.action.indentUsingSpaces", "editor.action.changeTabDisplaySize", "editor.action.detectIndentation", "editor.action.reindentlines", "editor.action.reindentselectedlines", "editor.action.inPlaceReplace.up", "editor.action.inPlaceReplace.down", "editor.action.insertFinalNewLine", "expandLineSelection", "editor.action.copyLinesUpAction", "editor.action.copyLinesDownAction", "editor.action.duplicateSelection", "editor.action.moveLinesUpAction", "editor.action.moveLinesDownAction", "editor.action.sortLinesAscending", "editor.action.sortLinesDescending", "editor.action.removeDuplicateLines", "editor.action.trimTrailingWhitespace", "editor.action.deleteLines", "editor.action.indentLines", "editor.action.outdentLines", "editor.action.insertLineBefore", "editor.action.insertLineAfter", "deleteAllLeft", "deleteAllRight", "editor.action.joinLines", "editor.action.transpose", "editor.action.transformToUppercase", "editor.action.transformToLowercase", "editor.action.transformToSnakecase", "editor.action.transformToCamelcase", "editor.action.transformToPascalcase", "editor.action.transformToTitlecase", "editor.action.transformToKebabcase", "cancelLinkedEditingInput", "editor.action.linkedEditing", "editor.action.openLink", "editor.action.insertCursorAbove", "editor.action.insertCursorBelow", "editor.action.insertCursorAtEndOfEachLineSelected", "editor.action.addSelectionToNextFindMatch", "editor.action.addSelectionToPreviousFindMatch", "editor.action.moveSelectionToNextFindMatch", "editor.action.moveSelectionToPreviousFindMatch", "editor.action.selectHighlights", "editor.action.changeAll", "editor.action.addCursorsToBottom", "editor.action.addCursorsToTop", "editor.action.focusNextCursor", "editor.action.focusPreviousCursor", "editor.action.triggerParameterHints", "closeParameterHints", "showPrevParameterHint", "showNextParameterHint", "editor.action.rename", "acceptRenameInput", "acceptRenameInputWithPreview", "cancelRenameInput", "focusNextRenameSuggestion", "focusPreviousRenameSuggestion", "editor.action.smartSelect.grow", "editor.action.smartSelect.expand", "editor.action.smartSelect.shrink", "editor.action.toggleStickyScroll", "editor.action.focusStickyScroll", "editor.action.selectPreviousStickyScrollLine", "editor.action.selectNextStickyScrollLine", "editor.action.goToFocusedStickyScrollLine", "editor.action.selectEditor", "editor.action.forceRetokenize", "editor.action.toggleTabFocusMode", "editor.action.unicodeHighlight.disableHighlightingOfAmbiguousCharacters", "editor.action.unicodeHighlight.disableHighlightingOfInvisibleCharacters", "editor.action.unicodeHighlight.disableHighlightingOfNonBasicAsciiCharacters", "editor.action.unicodeHighlight.showExcludeOptions", "editor.action.wordHighlight.next", "editor.action.wordHighlight.prev", "editor.action.wordHighlight.trigger", "cursorWordStartLeft", "cursorWordEndLeft", "cursorWordLeft", "cursorWordStartLeftSelect", "cursorWordEndLeftSelect", "cursorWordLeftSelect", "cursorWordStartRight", "cursorWordEndRight", "cursorWordRight", "cursorWordStartRightSelect", "cursorWordEndRightSelect", "cursorWordRightSelect", "cursorWordAccessibilityLeft", "cursorWordAccessibilityLeftSelect", "cursorWordAccessibilityRight", "cursorWordAccessibilityRightSelect", "deleteWordStartLeft", "deleteWordEndLeft", "deleteWordLeft", "deleteWordStartRight", "deleteWordEndRight", "deleteWordRight", "deleteInsideWord", "cursorWordPartStartLeft", "cursorWordPartStartLeftSelect", "deleteWordPartLeft", "deleteWordPartRight", "cursorWordPartLeft", "cursorWordPartLeftSelect", "cursorWordPartRight", "cursorWordPartRightSelect", "toggle.window.commandCenter", "toggle.workbench.navigationControl.enabled", "toggle.workbench.layoutControl.enabled", "toggle.window.customTitleBarVisibility", "toggle.window.customTitleBarVisibility.windowed", "toggle.toggleCustomTitleBar", "showCustomTitleBar", "hideCustomTitleBar", "hideCustomTitleBarInFullScreen", "toggle.workbench.editor.editorActionsLocation", "toggle.chat.commandCenter.enabled", "workbench.action.files.openFileFolderInNewWindow", "workbench.action.files.openFolderInNewWindow", "workbench.action.files.openFileInNewWindow", "workbench.action.openWorkspaceInNewWindow", "addRootFolder", "setRootFolder", "vscode.openFolder", "vscode.newWindow", "vscode.removeFromRecentlyOpened", "workbench.extensions.action.showExtensionsForLanguage", "workbench.extensions.action.showExtensionsWithIds", "explorer.new<PERSON><PERSON>", "explorer.newFolder", "explorer.download", "explorer.upload", "workbench.action.addRootFolder", "workbench.action.removeRootFolder", "workbench.action.files.openFile", "workbench.action.files.openFolder", "workbench.action.files.openFolderViaWorkspace", "workbench.action.files.openFileFolder", "workbench.action.openWorkspace", "workbench.action.openWorkspaceConfigFile", "workbench.action.closeFolder", "workbench.action.saveWorkspaceAs", "workbench.action.duplicateWorkspaceInNewWindow", "breadcrumbs.toggle", "breadcrumbs.focusAndSelect", "breadcrumbs.focus", "breadcrumbs.toggleToOn", "breadcrumbs.focusNext", "breadcrumbs.focusPrevious", "breadcrumbs.focusNextWithPicker", "breadcrumbs.focusPreviousWithPicker", "breadcrumbs.selectFocused", "breadcrumbs.revealFocused", "breadcrumbs.selectEditor", "breadcrumbs.revealFocusedFromTreeAside", "workbench.action.toggleEditorGroupLayout", "workbench.action.files.saveAll", "openEditors.closeAll", "openEditors.newUntitledFile", "explorer.openToSide", "explorer.openAndPassFocus", "workbench.files.action.compareWithSaved", "selectForCompare", "compareSelected", "compareFiles", "copyFilePath", "copyRelativeFilePath", "workbench.action.files.copyPathOfActiveFile", "revealInExplorer", "explorer.openWith", "workbench.action.files.save", "workbench.action.files.saveWithoutFormatting", "workbench.action.files.saveAs", "saveAll", "workbench.files.action.saveAllInGroup", "workbench.action.files.saveFiles", "workbench.action.files.revert", "removeRootFolder", "previousCompressedFolder", "nextCompressedFolder", "firstCompressedFolder", "lastCompressedFolder", "workbench.action.files.newUntitledFile", "workbench.action.files.newFile", "workbench.files.action.compareFileWith", "workbench.files.action.focusFilesExplorer", "workbench.files.action.showActiveFileInExplorer", "workbench.files.action.compareWithClipboard", "workbench.files.action.compareNewUntitledTextFiles", "workbench.action.toggleAutoSave", "workbench.action.files.showOpenedFileInNewWindow", "workbench.action.files.setActiveEditorReadonlyInSession", "workbench.action.files.setActiveEditorWriteableInSession", "workbench.action.files.toggleActiveEditorReadonlyInSession", "workbench.action.files.resetActiveEditorReadonlyInSession", "renameFile", "moveFileToTrash", "deleteFile", "filesExplorer.cut", "filesExplorer.copy", "filesExplorer.paste", "filesExplorer.cancelCut", "filesExplorer.openFilePreserveFocus", "workbench.files.action.acceptLocalChanges", "workbench.files.action.revertLocalChanges", "chat.inlineResourceAnchor.addFileToChat", "chat.inlineResourceAnchor.copyResource", "chat.inlineResourceAnchor.openToSide", "chat.inlineSymbolAnchor.goToDefinition", "chat.inlineSymbolAnchor.goToTypeDefinitions", "chat.inlineSymbolAnchor.goToImplementations", "chat.inlineSymbolAnchor.goToReferences", "editor.action.inspectTMScopes", "insertSnippet", "workbench.files.action.createFileFromExplorer", "workbench.files.action.createFolderFromExplorer", "workbench.files.action.refreshFilesExplorer", "workbench.files.action.collapseExplorerFolders", "workbench.action.chat.addToChatAction", "workbench.action.chat.copyLink", "chatEditing.removeFileFromWorkingSet", "chatEditing.openFileInDiff", "chatEditing.acceptFile", "chatEditing.discardFile", "chatEditing.acceptAllFiles", "chatEditing.discardAllFiles", "chatEditing.clearWorkingSet", "chatEditing.viewChanges", "workbench.action.chat.undoEdits", "chat.openFileUpdatedBySnapshot", "chat.openFileSnapshot", "workbench.action.edits.addFilesFromReferences", "chatEditing.viewPreviousEdits", "notebook.cellOuput.showEmptyOutputs", "notebook.cellOutput.copy", "notebook.cellOutput.openInTextEditor", "notebook.cellOutput.openInOutputPreview", "workbench.action.chat.addDynamicVariable", "chat.toolOutput.save", "editor.action.toggleWordWrap", "notebook.developer.addViewZones", "workbench.action.editorDictation.start", "workbench.action.editorDictation.stop", "widgetNavigation.focusPrevious", "widgetNavigation.focusNext", "notebook.toggleLineNumbers", "notebook.cell.toggleLineNumbers", "notebook.fold", "notebook.unfold", "files.participants.resetChoice", "workbench.extensions.action.manageAuthorizedExtensionURIs", "commentsFocusViewFromFilter", "commentsClearFilterText", "commentsFocus<PERSON><PERSON><PERSON>", "workbench.actions.workbench.panel.comments.toggleUnResolvedComments", "workbench.actions.workbench.panel.comments.toggleResolvedComments", "workbench.actions.workbench.panel.comments.toggleSortByUpdatedAt", "workbench.actions.workbench.panel.comments.toggleSortByResource", "editor.action.accessibleViewNext", "editor.action.accessibleViewNextCodeBlock", "editor.action.accessibleViewPreviousCodeBlock", "editor.action.accessibleViewPrevious", "editor.action.accessibleViewGoToSymbol", "editor.action.accessibilityHelp", "editor.action.accessibleView", "editor.action.accessibleViewDisableHint", "editor.action.accessibilityHelpConfigureKeybindings", "editor.action.accessibilityHelpConfigureAssignedKeybindings", "editor.action.accessibilityHelpOpenHelpLink", "editor.action.accessibleViewAcceptInlineCompletion", "views.moveViewUp", "views.moveViewLeft", "views.moveViewDown", "views.moveViewRight", "vscode.moveViews", "workbench.action.inspectContextKeys", "workbench.action.toggleScreencastMode", "workbench.action.logStorage", "workbench.action.logWorkingCopies", "workbench.action.removeLargeStorageDatabaseEntries", "workbench.action.keybindingsReference", "workbench.action.openVideoTutorialsUrl", "workbench.action.openTipsAndTricksUrl", "workbench.action.openDocumentationUrl", "workbench.action.openNewsletterSignupUrl", "workbench.action.openYouTubeUrl", "workbench.action.openRequestFeatureUrl", "workbench.action.openLicenseUrl", "workbench.action.openPrivacyStatementUrl", "workbench.action.getStartedWithAccessibilityFeatures", "workbench.action.askVScode", "workbench.action.togglePanel", "workbench.action.closePanel", "workbench.action.focusPanel", "workbench.action.positionPanelTop", "workbench.action.positionPanelLeft", "workbench.action.positionPanelRight", "workbench.action.positionPanelBottom", "workbench.action.alignPanelLeft", "workbench.action.alignPanelRight", "workbench.action.alignPanelCenter", "workbench.action.alignPanelJustify", "workbench.action.previousPanelView", "workbench.action.nextPanelView", "workbench.action.toggleMaximizedPanel", "workbench.action.movePanelToSidePanel", "workbench.action.movePanelToSecondarySideBar", "workbench.action.moveSidePanelToPanel", "workbench.action.moveSecondarySideBarToPanel", "workbench.action.toggleAuxiliaryBar", "workbench.action.closeAuxiliaryBar", "workbench.action.focusAuxiliaryBar", "workbench.action.previousAuxiliaryBarView", "workbench.action.nextAuxiliaryBarView", "workbench.action.toggleCenteredLayout", "workbench.action.moveSideBarRight", "workbench.action.moveSideBarLeft", "workbench.action.toggleSidebarPosition", "workbench.action.toggleEditorVisibility", "workbench.action.toggleSidebarVisibility", "workbench.action.toggleStatusbarVisibility", "workbench.action.hideEditorTabs", "workbench.action.zenHideEditorTabs", "workbench.action.showMultipleEditorTabs", "workbench.action.zenShowMultipleEditorTabs", "workbench.action.showEditorTab", "workbench.action.zenShowEditorTab", "workbench.action.editorActionsTitleBar", "workbench.action.editorActionsDefault", "workbench.action.hideEditorActions", "workbench.action.showEditorActions", "workbench.action.configureEditorTabs", "workbench.action.configureEditor", "workbench.action.toggleSeparatePinnedEditorTabs", "workbench.action.toggleZenMode", "workbench.action.exitZenMode", "workbench.action.toggleMenuBar", "workbench.action.resetViewLocations", "workbench.action.moveView", "workbench.action.moveFocusedView", "workbench.action.resetFocusedViewLocation", "workbench.action.increaseViewSize", "workbench.action.increaseViewWidth", "workbench.action.increaseViewHeight", "workbench.action.decreaseViewSize", "workbench.action.decreaseViewWidth", "workbench.action.decreaseViewHeight", "workbench.action.alignQuickInputTop", "workbench.action.alignQuickInputCenter", "workbench.action.customizeLayout", "list.focusDown", "list.focusUp", "list.focusAnyDown", "list.focusAnyUp", "list.focusPageDown", "list.focusPageUp", "list.focusFirst", "list.focusLast", "list.focusAnyFirst", "list.focusAnyLast", "list.expandSelectionDown", "list.expandSelectionUp", "list.collapse", "list.stickyScroll.collapse", "list.collapseAll", "list.collapseAllToFocus", "list.focusParent", "list.expand", "list.select", "list.stickyScrollselect", "list.selectAndPreserveFocus", "list.selectAll", "list.toggleSelection", "list.showHover", "list.toggleExpand", "list.stickyScrolltoggleExpand", "list.clear", "list.triggerTypeNavigation", "list.toggleFindMode", "list.toggleFindMatchType", "list.toggleKeyboardNavigation", "list.toggleFilterOnType", "list.find", "list.closeFind", "list.scrollUp", "list.scrollDown", "list.scrollLeft", "list.scrollRight", "tree.toggleStickyScroll", "workbench.action.navigateLeft", "workbench.action.navigateRight", "workbench.action.navigateUp", "workbench.action.navigateDown", "workbench.action.focusNextPart", "workbench.action.focusPreviousPart", "workbench.action.newWindow", "workbench.action.toggleFullScreen", "workbench.action.quickOpenRecent", "workbench.action.openRecent", "workbench.action.reloadWindow", "workbench.action.showAboutDialog", "workbench.action.blur", "workbench.action.quickOpenNavigateNextInRecentFilesPicker", "workbench.action.quickOpenNavigatePreviousInRecentFilesPicker", "workbench.action.toggleConfirmBeforeClose", "workbench.action.closeQuickOpen", "workbench.action.acceptSelectedQuickOpenItem", "workbench.action.alternativeAcceptSelectedQuickOpenItem", "workbench.action.focusQuickOpen", "workbench.action.quickOpenNavigateNextInFilePicker", "workbench.action.quickOpenNavigatePreviousInFilePicker", "workbench.action.quickPickManyToggle", "workbench.action.quickInputBack", "workbench.action.quickOpen", "workbench.action.quickOpenWithModes", "workbench.action.quickOpenPreviousEditor", "workbench.action.quickOpenSelectNext", "workbench.action.quickOpenSelectPrevious", "workbench.action.quickOpenNavigateNext", "workbench.action.quickOpenNavigatePrevious", "remote.tunnel.label", "remote.tunnel.forwardInline", "remote.tunnel.forwardCommandPalette", "remote.tunnel.closeInline", "remote.tunnel.closeCommandPalette", "remote.tunnel.open", "remote.tunnel.openPreview", "remote.tunnel.openCommandPalette", "remote.tunnel.copyAddressInline", "remote.tunnel.copyAddressCommandPalette", "remote.tunnel.changeLocalPort", "remote.tunnel.setProtocolHttp", "remote.tunnel.setProtocolHttps", "workbench.action.editor.changeLanguageMode", "workbench.action.editor.changeEOL", "workbench.action.editor.changeEncoding", "workbench.action.navigateForward", "workbench.action.navigateBack", "workbench.action.nextEditor", "workbench.action.previousEditor", "workbench.action.nextEditorInGroup", "workbench.action.previousEditorInGroup", "workbench.action.firstEditorInGroup", "workbench.action.lastEditorInGroup", "workbench.action.openNextRecentlyUsedEditor", "workbench.action.openPreviousRecentlyUsedEditor", "workbench.action.openNextRecentlyUsedEditorInGroup", "workbench.action.openPreviousRecentlyUsedEditorInGroup", "workbench.action.reopenClosedEditor", "workbench.action.clearRecentFiles", "workbench.action.showAllEditors", "workbench.action.showAllEditorsByMostRecentlyUsed", "workbench.action.showEditorsInActiveGroup", "workbench.action.closeAllEditors", "workbench.action.closeAllGroups", "workbench.action.closeEditorsToTheLeft", "workbench.action.closeEditorsInOtherGroups", "workbench.action.closeEditorInAllGroups", "workbench.action.revertAndCloseActiveEditor", "workbench.action.splitEditor", "workbench.action.splitEditorOrthogonal", "workbench.action.splitEditorLeft", "workbench.action.splitEditorRight", "workbench.action.splitEditorUp", "workbench.action.splitEditorDown", "workbench.action.joinTwoGroups", "workbench.action.joinAllGroups", "workbench.action.navigateEditorGroups", "workbench.action.evenEditorWidths", "workbench.action.toggleEditorWidths", "workbench.action.maximizeEditorHideSidebar", "workbench.action.toggleMaximizeEditorGroup", "workbench.action.minimizeOtherEditors", "workbench.action.minimizeOtherEditorsHideSidebar", "workbench.action.moveEditorLeftInGroup", "workbench.action.moveEditorRightInGroup", "workbench.action.moveActiveEditorGroupLeft", "workbench.action.moveActiveEditorGroupRight", "workbench.action.moveActiveEditorGroupUp", "workbench.action.moveActiveEditorGroupDown", "workbench.action.duplicateActiveEditorGroupLeft", "workbench.action.duplicateActiveEditorGroupRight", "workbench.action.duplicateActiveEditorGroupUp", "workbench.action.duplicateActiveEditorGroupDown", "workbench.action.moveEditorToPreviousGroup", "workbench.action.moveEditorToNextGroup", "workbench.action.moveEditorToFirstGroup", "workbench.action.moveEditorToLastGroup", "workbench.action.moveEditorToLeftGroup", "workbench.action.moveEditorToRightGroup", "workbench.action.moveEditorToAboveGroup", "workbench.action.moveEditorToBelowGroup", "workbench.action.splitEditorToPreviousGroup", "workbench.action.splitEditorToNextGroup", "workbench.action.splitEditorToFirstGroup", "workbench.action.splitEditorToLastGroup", "workbench.action.splitEditorToLeftGroup", "workbench.action.splitEditorToRightGroup", "workbench.action.splitEditorToAboveGroup", "workbench.action.splitEditorToBelowGroup", "workbench.action.focusActiveEditorGroup", "workbench.action.focusFirstEditorGroup", "workbench.action.focusLastEditorGroup", "workbench.action.focusPreviousGroup", "workbench.action.focusNextGroup", "workbench.action.focusLeftGroup", "workbench.action.focusRightGroup", "workbench.action.focusAboveGroup", "workbench.action.focusBelowGroup", "workbench.action.newGroupLeft", "workbench.action.newGroupRight", "workbench.action.newGroupAbove", "workbench.action.newGroupBelow", "workbench.action.navigateLast", "workbench.action.navigateForwardInEditLocations", "workbench.action.navigateBackInEditLocations", "workbench.action.navigatePreviousInEditLocations", "workbench.action.navigateToLastEditLocation", "workbench.action.navigateForwardInNavigationLocations", "workbench.action.navigateBackInNavigationLocations", "workbench.action.navigatePreviousInNavigationLocations", "workbench.action.navigateToLastNavigationLocation", "workbench.action.clearEditorHistory", "workbench.action.editorLayoutSingle", "workbench.action.editorLayoutTwoColumns", "workbench.action.editorLayoutThreeColumns", "workbench.action.editorLayoutTwoRows", "workbench.action.editorLayoutThreeRows", "workbench.action.editorLayoutTwoByTwoGrid", "workbench.action.editorLayoutTwoRowsRight", "workbench.action.editorLayoutTwoColumnsBottom", "workbench.action.toggleEditorType", "workbench.action.reopenTextEditor", "workbench.action.quickOpenPreviousRecentlyUsedEditor", "workbench.action.quickOpenLeastRecentlyUsedEditor", "workbench.action.quickOpenPreviousRecentlyUsedEditorInGroup", "workbench.action.quickOpenLeastRecentlyUsedEditorInGroup", "workbench.action.openPreviousEditorFromHistory", "workbench.action.moveEditorToNewWindow", "workbench.action.copyEditorToNewWindow", "workbench.action.moveEditorGroupToNewWindow", "workbench.action.copyEditorGroupToNewWindow", "workbench.action.restoreEditorsToMainWindow", "workbench.action.newEmptyEditorWindow", "workbench.action.quickOpenNavigateNextInEditorPicker", "workbench.action.quickOpenNavigatePreviousInEditorPicker", "moveActiveEditor", "copyActiveEditor", "layoutEditorGroups", "vscode.setEditorLayout", "vscode.getEditorLayout", "workbench.action.compareEditor.nextChange", "workbench.action.compareEditor.previousChange", "toggle.diff.renderSideBySide", "workbench.action.compareEditor.focusPrimarySide", "workbench.action.compareEditor.focusSecondarySide", "workbench.action.compareEditor.focusOtherSide", "toggle.diff.ignoreTrimWhitespace", "workbench.action.compareEditor.swapSides", "vscode.open", "vscode.diff", "vscode.changes", "workbench.action.openEditorAtIndex", "workbench.action.openEditorAtIndex1", "workbench.action.openEditorAtIndex2", "workbench.action.openEditorAtIndex3", "workbench.action.openEditorAtIndex4", "workbench.action.openEditorAtIndex5", "workbench.action.openEditorAtIndex6", "workbench.action.openEditorAtIndex7", "workbench.action.openEditorAtIndex8", "workbench.action.openEditorAtIndex9", "workbench.action.closeActiveEditor", "workbench.action.closeActivePinnedEditor", "workbench.action.closeEditorsInGroup", "workbench.action.closeGroup", "workbench.action.closeUnmodifiedEditors", "workbench.action.closeOtherEditors", "workbench.action.closeEditorsToTheRight", "workbench.action.reopenWithEditor", "reopenActiveEditorWith", "workbench.action.closeEditorsAndGroup", "workbench.action.keepEditor", "workbench.action.toggleKeepEditors", "workbench.action.toggleEditorGroupLock", "workbench.action.lockEditorGroup", "workbench.action.unlockEditorGroup", "workbench.action.pinEditor", "workbench.action.compareEditor.openSide", "workbench.action.unpinEditor", "workbench.action.showEditorsInGroup", "workbench.action.splitEditorInGroup", "workbench.action.joinEditorInGroup", "workbench.action.toggleSplitEditorInGroup", "workbench.action.toggleSplitEditorInGroupLayout", "workbench.action.focusFirstSideEditor", "workbench.action.focusSecondSideEditor", "workbench.action.focusOtherSideEditor", "workbench.action.focusSecondEditorGroup", "workbench.action.focusThirdEditorGroup", "workbench.action.focusFourthEditorGroup", "workbench.action.focusFifthEditorGroup", "workbench.action.focusSixthEditorGroup", "workbench.action.focusSeventhEditorGroup", "workbench.action.focusEighthEditorGroup", "workbench.action.focusLeftGroupWithoutWrap", "workbench.action.focusRightGroupWithoutWrap", "workbench.action.focusAboveGroupWithoutWrap", "workbench.action.focusBelowGroupWithoutWrap", "workbench.action.toggleCompactAuxiliaryWindow", "workbench.action.enableCompactAuxiliaryWindow", "workbench.action.disableCompactAuxiliaryWindow", "workbench.action.closeSidebar", "workbench.action.focusSideBar", "workbench.action.activityBarLocation.default", "workbench.action.activityBarLocation.top", "workbench.action.activityBarLocation.bottom", "workbench.action.activityBarLocation.hide", "workbench.action.previousSideBarView", "workbench.action.nextSideBarView", "workbench.action.focusActivityBar", "workbench.banner.focusBanner", "workbench.banner.focusNextAction", "workbench.banner.focusPreviousAction", "workbench.action.focusBanner", "workbench.statusBar.focusPrevious", "workbench.statusBar.focusNext", "workbench.statusBar.focusFirst", "workbench.statusBar.focusLast", "workbench.statusBar.clearFocus", "workbench.action.focusStatusBar", "menu.resetHiddenStates", "extension.bisect.start", "extension.bisect.next", "extension.bisect.stop", "quickInput.pageNext", "quickInput.pagePrevious", "quickInput.first", "quickInput.last", "quickInput.next", "quickInput.previous", "quickInput.nextSeparatorWithQuickAccessFallback", "quickInput.nextSeparator", "quickInput.previousSeparatorWithQuickAccessFallback", "quickInput.previousSeparator", "quickInput.acceptInBackground", "quickInput.toggleHover", "getContextKeyInfo", "perfview.show", "perf.insta.printAsyncCycles", "perf.insta.printTraces", "perf.event.profiling", "notebook.cell.insertCodeCellAbove", "notebook.cell.insertCodeCellAboveAndFocusContainer", "notebook.cell.insertCodeCellBelow", "notebook.cell.insertCodeCellBelowAndFocusContainer", "notebook.cell.insertMarkdownCellAbove", "notebook.cell.insertMarkdownCellBelow", "notebook.cell.insertCodeCellAtTop", "notebook.cell.insertMarkdownCellAtTop", "notebook.renderAllMarkdownCells", "notebook.execute", "notebook.cell.execute", "notebook.cell.executeCellsAbove", "notebook.cell.executeCellAndBelow", "notebook.cell.executeAndFocusContainer", "notebook.cell.cancelExecution", "notebook.cell.executeAndSelectBelow", "notebook.cell.executeAndInsertBelow", "notebook.cancelExecution", "notebook.interruptExecution", "notebook.revealRunningCell", "notebook.revealLastFailedCell", "notebook.outline.toggleShowMarkdownHeadersOnly", "notebook.outline.toggleCodeCells", "notebook.outline.toggleCodeCellSymbols", "notebook.section.runSingleCell", "notebook.section.runCells", "notebook.section.foldSection", "notebook.section.expandSection", "workbench.notebook.layout.select", "workbench.notebook.layout.configure", "workbench.notebook.layout.configure.editorTitle", "notebook.toggleLineNumbersFromEditorTitle", "notebook.toggleCellToolbarPositionFromEditorTitle", "breadcrumbs.toggleFromEditorTitle", "notebook.saveMimeTypeOrder", "workbench.notebook.layout.webview.reset", "notebook.action.toggleNotebookStickyScroll", "notebook.action.indentUsingSpaces", "notebook.action.indentUsingTabs", "notebook.action.changeTabDisplaySize", "notebook.action.convertIndentationToSpaces", "notebook.action.convertIndentationToTabs", "workbench.debug.viewlet.action.addFunctionBreakpointAction", "workbench.debug.viewlet.action.addDataBreakpointOnAddress", "workbench.debug.viewlet.action.editDataBreakpointOnAddress", "workbench.debug.viewlet.action.toggleBreakpointsActivatedAction", "workbench.debug.viewlet.action.removeBreakpoint", "workbench.debug.viewlet.action.removeAllBreakpoints", "workbench.debug.viewlet.action.enableAllBreakpoints", "workbench.debug.viewlet.action.disableAllBreakpoints", "workbench.debug.viewlet.action.reapplyBreakpointsAction", "debug.editBreakpoint", "debug.editFunctionBreakpoint", "debug.editFunctionBreakpointHitCount", "debug.editBreakpointMode", "debug.copyStackTrace", "workbench.action.debug.reverseContinue", "workbench.action.debug.stepBack", "workbench.action.debug.terminateThread", "debug.jumpToCursor", "workbench.action.debug.callStackTop", "workbench.action.debug.callStackBottom", "workbench.action.debug.callStackUp", "workbench.action.debug.callStackDown", "workbench.action.debug.nextConsole", "workbench.action.debug.prevConsole", "workbench.action.debug.restart", "workbench.action.debug.stepOver", "workbench.action.debug.stepInto", "workbench.action.debug.stepOut", "workbench.action.debug.pause", "workbench.action.debug.stepIntoTarget", "workbench.action.debug.disconnect", "workbench.action.debug.disconnectAndSuspend", "workbench.action.debug.stop", "workbench.action.debug.restartFrame", "workbench.action.debug.continue", "workbench.action.debug.showLoadedScripts", "debug.startFromConfig", "workbench.action.debug.focusProcess", "workbench.action.debug.selectandstart", "workbench.action.debug.selectDebugConsole", "workbench.action.debug.selectDebugSession", "workbench.action.debug.start", "workbench.action.debug.run", "debug.toggleBreakpoint", "debug.enableOrDisableBreakpoint", "debug.renameWatchExpression", "debug.setWatchExpression", "debug.setVariable", "debug.removeWatchExpression", "debug.removeBreakpoint", "debug.installAdditionalDebuggers", "debug.addConfiguration", "editor.debug.action.toggleInlineBreakpoint", "debug.openBreakpointToSide", "debug.openView", "workbench.debug.viewlet.action.copyValue", "workbench.debug.viewlet.action.viewMemory", "debug.breakWhenValueChanges", "debug.breakWhenValueIsAccessed", "debug.breakWhenValueIsRead", "debug.copyEvaluatePath", "debug.addToWatchExpressions", "variables.collapse", "notebook.clearAllInlineValues", "notebook.cell.edit", "notebook.cell.quitEdit", "notebook.cell.delete", "notebook.cell.clearOutputs", "notebook.clearAllCellsOutputs", "notebook.cell.changeLanguage", "notebook.cell.detectLanguage", "notebook.selectIndentation", "notebook.commentSelectedCells", "search.action.clearHistory", "search.action.cancel", "search.action.refreshSearchResults", "search.action.collapseSearchResults", "search.action.expandSearchResults", "search.action.clearSearchResults", "search.action.viewAsTree", "search.action.viewAsList", "search.action.searchWithAI", "workbench.action.chat.assignSelectedAgent", "workbench.action.chat.startParameterizedPrompt", "notebook.cell.chat.accept", "notebook.cell.chat.arrowOutUp", "notebook.cell.chat.arrowOutDown", "notebook.cell.focusChatWidget", "notebook.cell.focusNextChatWidget", "notebook.cell.chat.stop", "notebook.cell.chat.close", "notebook.cell.chat.acceptChanges", "notebook.cell.chat.discard", "notebook.cell.chat.start", "notebook.cell.chat.startAtTop", "notebook.cell.chat.focus", "notebook.cell.chat.focusNextCell", "notebook.cell.chat.focusPreviousCell", "notebook.cell.chat.previousFromHistory", "notebook.cell.chat.nextFromHistory", "notebook.cell.chat.restore", "notebook.inlineChat.acceptChangesAndRun", "notebook.cellOutput.addToChat", "notebook.chat.selectAndInsertKernelVariable", "notebook.openVariablesView", "notebook.cell.copy", "notebook.cell.cut", "notebook.cell.paste", "notebook.cell.pasteAbove", "workbench.action.toggleNotebookClipboardLog", "notebook.cell.output.selectAll", "notebook.hideFind", "notebook.find", "notebook.selectAllFindMatches", "notebook.addFindMatchToSelection", "noteMultiCursor.exit", "noteMultiCursor.deleteLeft", "noteMultiCursor.deleteRight", "notebook.format", "notebook.formatCell", "workbench.notebook.layout.gettingStarted", "notebook.toggleCellToolbarPosition", "notebook.cell.nullAction", "notebook.focusNextEditor", "notebook.focusPreviousEditor", "notebook.focusTop", "notebook.focusBottom", "notebook.cell.focusInOutput", "notebook.cell.focusOutOutput", "notebook.centerActiveCell", "notebook.cell.cursorPageUp", "notebook.cell.cursorPageUpSelect", "notebook.cell.cursorPageDown", "notebook.cell.cursorPageDownSelect", "notebook.setProfile", "notebook.cell.moveUp", "notebook.cell.moveDown", "notebook.cell.copyUp", "notebook.cell.copyDown", "notebook.cell.split", "notebook.cell.joinAbove", "notebook.cell.joinBelow", "notebook.cell.joinSelected", "notebook.cell.changeToCode", "notebook.cell.changeToMarkdown", "notebook.cell.collapseCellInput", "notebook.cell.expandCellInput", "notebook.cell.collapseCellOutput", "notebook.cell.expandCellOutput", "notebook.cell.toggleOutputs", "notebook.cell.collapseAllCellInputs", "notebook.cell.expandAllCellInputs", "notebook.cell.collapseAllCellOutputs", "notebook.cell.expandAllCellOutputs", "notebook.cell.toggleOutputScrolling", "notebook.toggleLayoutTroubleshoot", "notebook.inspectLayout", "notebook.clearNotebookEdtitorTypeCache", "breakpointWidget.action.acceptInput", "closeBreakpointWidget", "notebook.cell.openFailureActions", "notebook.cell.chat.fixError", "notebook.cell.chat.explainError", "notebook.diff.openFile", "notebook.diff.cell.toggleCollapseUnchangedRegions", "notebook.diff.switchToText", "notebook.diffEditor.showUnchangedCells", "notebook.diffEditor.hideUnchangedCells", "notebook.diffEditor.2.goToCell", "notebook.diff.revertMetadata", "notebook.diffEditor.2.cell.revertInput", "notebook.diffEditor.2.cell.revertOutputs", "notebook.diffEditor.2.cell.revertMetadata", "notebook.diff.cell.revertMetadata", "notebook.diff.cell.switchOutputRenderingStyleToText", "notebook.diff.cell.revertOutputs", "notebook.toggle.diff.cell.ignoreTrimWhitespace", "notebook.diff.cell.revertInput", "notebook.diff.showOutputs", "notebook.diff.showMetadata", "notebook.diff.action.previous", "notebook.diff.action.next", "notebook.diff.inline.toggle", "notebook.clearNotebookKernelsMRUCache", "editor.action.formatDocument.multiple", "editor.action.formatSelection.multiple", "workbench.command.new.prompt", "workbench.command.new.instructions", "workbench.command.new.mode", "workbench.action.gotoSymbol", "workbench.action.chat.open", "workbench.action.chat.openAsk", "workbench.action.chat.openAgent", "workbench.action.chat.openEdit", "workbench.action.chat.toggle", "workbench.action.chat.history", "workbench.action.openChat", "workbench.action.chat.addParticipant", "workbench.action.chat.clearInputHistory", "workbench.action.chat.clearHistory", "chat.action.focus", "workbench.action.chat.focusInput", "workbench.action.chat.manageSettings", "workbench.action.chat.showExtensionsUsingCopilot", "workbench.action.chat.configureCodeCompletions", "workbench.action.chat.openQuotaExceededDialog", "workbench.action.chat.resetTrustedTools", "workbench.action.chat.copyAll", "workbench.action.chat.copyItem", "workbench.action.chat.copyCodeBlock", "workbench.action.chat.applyInEditor", "workbench.action.chat.insertCodeBlock", "workbench.action.chat.insertIntoNewFile", "workbench.action.chat.runInTerminal", "workbench.action.chat.nextCodeBlock", "workbench.action.chat.previousCodeBlock", "workbench.action.chat.applyCompareEdits", "workbench.action.chat.discardCompareEdits", "workbench.action.chat.nextFileTree", "workbench.action.chat.previousFileTree", "workbench.action.chat.markHelpful", "workbench.action.chat.markUnhelpful", "workbench.action.chat.reportIssueForBug", "workbench.action.chat.retry", "workbench.action.chat.insertIntoNotebook", "workbench.action.chat.submit", "workbench.action.edits.submit", "workbench.action.chat.submitWithoutDispatching", "workbench.action.chat.cancel", "workbench.action.chat.sendToNewChat", "workbench.action.chat.submitWithCodebase", "workbench.action.chat.toggleAgentMode", "workbench.action.chat.toggleRequestPaused", "workbench.action.chat.switchToNextModel", "workbench.action.chat.openModelPicker", "workbench.action.chat.changeModel", "workbench.action.quickchat.toggle", "workbench.action.openQuickChat", "workbench.action.quickchat.openInChatView", "workbench.action.quickchat.close", "workbench.action.chat.export", "workbench.action.chat.import", "workbench.action.chat.openInEditor", "workbench.action.chat.openInNewWindow", "workbench.action.chat.openInSidebar", "workbench.action.chatEditor.newChat", "workbench.action.chat.newChat", "workbench.action.chat.newEditSession", "workbench.action.chat.undoEdit", "workbench.action.chat.redoEdit", "workbench.action.chat.attachContext", "workbench.action.chat.attachFile", "workbench.action.chat.attachFolder", "workbench.action.chat.attachSelection", "workbench.action.chat.insertSearchResults", "workbench.action.chat.run-in-new-chat.prompt.current", "workbench.action.chat.run.prompt.current", "workbench.action.chat.run.prompt", "workbench.action.chat.configure.prompts", "workbench.action.chat.attach.instructions", "workbench.action.chat.configure.instructions", "workbench.action.chat.save-to-prompt", "workbench.action.chat.manage.mode", "workbench.action.chat.logInputHistory", "workbench.action.chat.logChatIndex", "chatEditor.action.navigateNext", "chatEditor.action.navigatePrevious", "chatEditor.action.reviewChanges", "chatEditor.action.accept", "chatEditor.action.reject", "chatEditor.action.acceptHunk", "chatEditor.action.undoHunk", "chatEditor.action.toggleDiff", "chatEditor.action.showAccessibleDiffView", "chatEditing.multidiff.acceptAllFiles", "chatEditing.multidiff.discardAllFiles", "workbench.action.chat.acceptTool", "workbench.action.chat.configureTools", "chat.configureToolSets", "interactiveEditor.start", "interactive.acceptChanges", "inlineChat2.keep", "inlineChat2.undo", "inlineChat2.close", "inlineChat2.reveal", "inlineChat2.cancelRequest", "inlineChat.startWithCurrentLine", "inlineChat.showHint", "inlineChat.hideHint", "inlineChat.start", "inlineChat.close", "inlineChat.configure", "inlineChat.unstash", "inlineChat.discardHunkChange", "inlineChat.regenerate", "inlineChat.moveToNextHunk", "inlineChat.moveToPreviousHunk", "inlineChat.arrowOutUp", "inlineChat.arrowOutDown", "inlineChat.focus", "inlineChat.viewInChat", "inlineChat.toggleDiff", "inlineChat.acceptChanges", "editor.action.extensioneditor.showfind", "editor.action.extensioneditor.findNext", "editor.action.extensioneditor.findPrevious", "extension.open", "workbench.extensions.installExtension", "workbench.extensions.uninstallExtension", "workbench.extensions.search", "workbench.action.showRuntimeExtensions", "workbench.mcp.listServer", "workbench.mcp.serverOptions", "workbench.mcp.resetTrust", "workbench.mcp.resetCachedTools", "workbench.mcp.addConfiguration", "workbench.mcp.removeStoredInput", "workbench.mcp.editStoredInput", "workbench.mcp.startServer", "workbench.mcp.stopServer", "workbench.mcp.showOutput", "workbench.mcp.installFromActivation", "workbench.mcp.restartServer", "workbench.mcp.showConfiguration", "workbench.mcp.browseServers", "workbench.mcp.browseResources", "workbench.mcp.configureSamplingModels", "workbench.mcp.startPromptForServer", "interactive.execute", "interactive.input.clear", "interactive.history.previous", "interactive.history.next", "interactive.scrollToTop", "interactive.scrollToBottom", "interactive.input.focus", "interactive.history.focus", "repl.action.acceptInput", "repl.action.copyAll", "repl.action.filter", "repl.action.find", "workbench.action.debug.selectRepl", "workbench.debug.panel.action.clearReplAction", "debug.collapseRepl", "debug.replPaste", "workbench.debug.action.copyAll", "debug.replCopy", "workbench.debug.action.focusRepl", "repl.focusLastItemExecuted", "repl.input.focus", "repl.execute", "list.find.replInputFocus", "testing.toggleInlineCoverage", "testing.coverageToggleToolbar", "testing.coverageFilterToTestInEditor", "testing.coverageFilterToTest", "testing.coverageViewChangeSorting", "callStackWidget.goToFile", "testing.callStack.run", "testing.callStack.debug", "vscode.pickMultipleTestProfiles", "vscode.pickTestProfile", "testing.cancelTestRefresh", "testing.cancelRun", "testing.coverage.close", "testing.clearTestResults", "testing.collapseAll", "testing.configureProfile", "testing.toggleContinuousRunForTest", "testing.continuousRunUsingForTest", "testing.coverage", "testing.coverageAll", "testing.coverageAtCursor", "testing.coverageCurrentFile", "testing.coverageLastRun", "testing.coverageSelected", "testing.coverage.uri", "testing.debug", "testing.debugAll", "testing.debugAtCursor", "testing.debugCurrentFile", "testing.debugFailTests", "testing.debugLastRun", "testing.debugSelected", "testing.debug.uri", "testing.getSelectedProfiles", "testing.goToRelatedCode", "testing.goToRelatedTest", "testing.editFocusedTest", "testing.hideTest", "testing.openCoverage", "testing.openOutputPeek", "testing.peekRelatedCode", "testing.peekRelatedTest", "testing.refreshTests", "testing.reRunFailTests", "testing.reRunLastRun", "testing.run", "testing.runAll", "testing.runAtCursor", "testing.runCurrentFile", "testing.runSelected", "testing.run.uri", "testing.runUsing", "testing.searchForTestExtension", "testing.selectDefaultTestProfiles", "testing.showMostRecentOutput", "testing.startContinuousRun", "testing.stopContinuousRun", "testing.sortByDuration", "testing.sortByLocation", "testing.sortByStatus", "testing.viewAsList", "testing.viewAsTree", "testing.toggleInlineTestOutput", "testing.unhideAllTests", "testing.unhideTest", "testing.openMessageInEditor", "testing.goToPreviousMessage", "testing.goToNextMessage", "editor.closeTestPeek", "testing.toggleTestingPeekHistory", "testing.collapsePeekStack", "testing.startContinuousRunFromExtension", "testing.stopContinuousRunFromExtension", "vscode.peekTestError", "vscode.revealTest", "vscode.runTestsById", "vscode.testing.getControllersWithTests", "vscode.testing.getTestsInFile", "workbench.action.setLogLevel", "workbench.action.setDefaultLogLevel", "workbench.action.clearCommandHistory", "workbench.action.showCommands", "workbench.action.openView", "workbench.action.quickOpenView", "workbench.action.quickOpenNavigateNextInViewPicker", "workbench.action.quickOpenNavigatePreviousInViewPicker", "refactorPreview.apply", "refactorPreview.discard", "refactorPreview.toggleCheckedState", "refactorPreview.groupByFile", "refactorPreview.groupByType", "refactorPreview.toggleGrouping", "search.action.copyMatch", "search.action.copyPath", "search.action.copyAll", "search.action.getSearchResults", "search.action.restrictSearchToFolder", "search.action.expandRecursively", "search.action.excludeFromSearch", "search.action.revealInSideBar", "workbench.action.findInFiles", "filesExplorer.findInFolder", "filesExplorer.findInWorkspace", "workbench.action.search.toggleQueryDetails", "closeReplaceInFilesWidget", "toggleSearchCaseSensitive", "toggleSearchWholeWord", "toggleSearchRegex", "toggleSearchPreserveCase", "search.action.openResult", "search.action.openResultToSide", "addCursorsAtSearchResults", "search.focus.nextInputBox", "search.focus.previousInputBox", "search.action.focusSearchFromResults", "workbench.action.toggleSearchOnType", "search.action.focusSearchList", "search.action.focusNextSearchResult", "search.action.focusPreviousSearchResult", "workbench.action.replaceInFiles", "search.action.remove", "search.action.replace", "search.action.replaceAllInFile", "search.action.replaceAllInFolder", "workbench.action.showAllSymbols", "workbench.action.quickTextSearch", "search.action.replaceAll", "cleanSearchEditorState", "search.searchEditor.action.deleteFileResults", "search.action.openNewEditor", "search.action.openEditor", "search.action.openNewEditorToSide", "search.action.openInEditor", "rerunSearchEditorSearch", "search.action.focusQueryEditorWidget", "search.action.focusFilesToInclude", "search.action.focusFilesToExclude", "toggleSearchEditorCaseSensitive", "toggleSearchEditorWholeWord", "toggleSearchEditorRegex", "toggleSearchEditorContextLines", "increaseSearchEditorContextLines", "decreaseSearchEditorContextLines", "selectAllSearchEditorMatches", "search.action.openNewEditorFromView", "workbench.scm.action.setListViewMode", "workbench.scm.action.setTreeViewMode", "workbench.scm.action.setListViewModeNavigation", "workbench.scm.action.setTreeViewModeNavigation", "workbench.scm.action.repositories.setSortKey.discoveryTime", "workbench.scm.action.repositories.setSortKey.name", "workbench.scm.action.repositories.setSortKey.path", "workbench.scm.action.setSortKey.name", "workbench.scm.action.setSortKey.path", "workbench.scm.action.setSortKey.status", "workbench.scm.action.collapseAllRepositories", "workbench.scm.action.expandAllRepositories", "scm.input.triggerSetup", "workbench.scm.action.graph.pickRepository", "workbench.scm.action.graph.pickHistoryItemRefs", "workbench.scm.action.graph.revealCurrentHistoryItem", "workbench.scm.action.graph.refresh", "workbench.scm.action.graph.setListViewMode", "workbench.scm.action.graph.setTreeViewMode", "workbench.scm.action.graph.viewChanges", "workbench.scm.action.graph.openFile", "editor.action.dirtydiff.previous", "editor.action.dirtydiff.next", "workbench.action.editor.<PERSON><PERSON><PERSON><PERSON>", "workbench.action.editor.nextChange", "closeQuickDiff", "workbench.scm.action.graph.addHistoryItemToChat", "workbench.scm.action.graph.summarizeHistoryItem", "scm.acceptInput", "scm.clearInput", "scm.viewNextCommit", "scm.viewPreviousCommit", "scm.forceViewNextCommit", "scm.forceViewPreviousCommit", "scm.openInIntegratedTerminal", "scm.openInTerminal", "scm.setActiveProvider", "workbench.scm.action.focusPreviousInput", "workbench.scm.action.focusNextInput", "workbench.scm.action.focusPreviousResourceGroup", "workbench.scm.action.focusNextResourceGroup", "workbench.debug.viewlet.action.copyWorkspaceVariableValue", "callStack.collapse", "editor.debug.action.copyAddress", "debug.action.openDisassemblyView", "debug.action.toggleDisassemblyViewSourceCode", "editor.debug.action.toggleBreakpoint", "editor.debug.action.conditionalBreakpoint", "editor.debug.action.addLogPoint", "editor.debug.action.triggerByBreakpoint", "editor.debug.action.editBreakpoint", "editor.debug.action.runToCursor", "editor.debug.action.stepIntoTargets", "editor.debug.action.selectionToRepl", "editor.debug.action.selectionToWatch", "editor.debug.action.showDebugHover", "editor.debug.action.goToNextBreakpoint", "editor.debug.action.goToPreviousBreakpoint", "editor.debug.action.closeExceptionWidget", "workbench.action.debug.configure", "debug.toggleReplIgnoreFocus", "loadedScripts.collapse", "watch.collapse", "workbench.debug.viewlet.action.addWatchExpression", "workbench.debug.viewlet.action.removeAllWatchExpressions", "debug.copyWatchExpression", "problems.action.open", "problems.action.openToSide", "workbench.action.showErrorsWarnings", "problems.action.showQuickFixes", "workbench.actions.table.workbench.panel.markers.view.viewAsTree", "workbench.actions.table.workbench.panel.markers.view.viewAsTable", "workbench.actions.workbench.panel.markers.view.toggleErrors", "workbench.actions.workbench.panel.markers.view.toggleWarnings", "workbench.actions.workbench.panel.markers.view.toggleInfos", "workbench.actions.workbench.panel.markers.view.toggleActiveFile", "workbench.actions.workbench.panel.markers.view.toggleExcludedFiles", "workbench.action.problems.focus", "problems.action.copy", "problems.action.copyMessage", "problems.action.copyRelatedInformationMessage", "problems.action.focusProblemsFromFilter", "problems.action.focusFilter", "problems.action.showMultilineMessage", "problems.action.showSinglelineMessage", "problems.action.clearFilterText", "workbench.actions.treeView.workbench.panel.markers.view.collapseAll", "workbench.actions.view.toggleProblems", "workbench.action.openProcessExplorer", "merge.openResult", "merge.mixedLayout", "merge.columnLayout", "merge.openBaseEditor", "merge.showNonConflictingChanges", "merge.showBase", "merge.showBaseTop", "merge.showBaseCenter", "merge.goToNextUnhandledConflict", "merge.goToPreviousUnhandledConflict", "merge.toggleActiveConflictInput1", "merge.toggleActiveConflictInput2", "mergeEditor.compareInput1WithBase", "mergeEditor.compareInput2WithBase", "merge.acceptAllInput1", "merge.acceptAllInput2", "mergeEditor.resetResultToBaseAndAutoMerge", "mergeEditor.acceptMerge", "mergeEditor.resetCloseWithConflictsChoice", "mergeEditor.acceptAllCombination", "mergeEditor.toggleBetweenInputs", "merge.dev.copyContents<PERSON>son", "merge.dev.saveContentsToFolder", "merge.dev.loadContentsFromFolder", "multiDiffEditor.goToFile", "multiDiffEditor.collapseAll", "multiDiffEditor.expandAll", "runCommands", "editor.action.nextCommentThreadAction", "editor.action.previousCommentThreadAction", "editor.action.nextCommentedRangeAction", "editor.action.previousCommentedRangeAction", "editor.action.nextCommentingRange", "editor.action.previousCommentingRange", "workbench.action.toggleCommenting", "workbench.action.addComment", "workbench.action.focusCommentOnCurrentLine", "workbench.action.collapseAllComments", "workbench.action.expandAllComments", "workbench.action.expandUnresolvedComments", "editor.action.submitComment", "workbench.action.hideComment", "comments.collapse", "comments.expand", "comments.reply", "workbench.action.url.openUrl", "workbench.action.manageTrustedDomain", "editor.action.webvieweditor.showFind", "editor.action.webvieweditor.hideFind", "editor.action.webvieweditor.findNext", "editor.action.webvieweditor.findPrevious", "workbench.action.webview.reloadWebviewAction", "iconSelectBox.focusUp", "iconSelectBox.focusDown", "iconSelectBox.focusNext", "iconSelectBox.focusPrevious", "iconSelectBox.selectFocused", "workbench.action.terminal.newInActiveWorkspace", "workbench.action.createTerminalEditor", "workbench.action.createTerminalEditorSameGroup", "workbench.action.createTerminalEditorSide", "workbench.action.terminal.moveToEditor", "workbench.action.terminal.moveIntoNewWindow", "workbench.action.terminal.moveToTerminalPanel", "workbench.action.terminal.focusPreviousPane", "workbench.action.terminal.focusNextPane", "workbench.action.terminal.resizePaneLeft", "workbench.action.terminal.resizePaneRight", "workbench.action.terminal.resizePaneUp", "workbench.action.terminal.resizePaneDown", "workbench.action.terminal.focus", "workbench.action.terminal.focusTabs", "workbench.action.terminal.focusNext", "workbench.action.terminal.focusPrevious", "workbench.action.terminal.runSelectedText", "workbench.action.terminal.runActiveFile", "workbench.action.terminal.scrollDown", "workbench.action.terminal.scrollDownPage", "workbench.action.terminal.scrollToBottom", "workbench.action.terminal.scrollUp", "workbench.action.terminal.scrollUpPage", "workbench.action.terminal.scrollToTop", "workbench.action.terminal.clearSelection", "workbench.action.terminal.changeIcon", "workbench.action.terminal.changeIconActiveTab", "workbench.action.terminal.changeColor", "workbench.action.terminal.changeColorActiveTab", "workbench.action.terminal.rename", "workbench.action.terminal.renameActiveTab", "workbench.action.terminal.detachSession", "workbench.action.terminal.attachToSession", "workbench.action.terminal.scrollToPreviousCommand", "workbench.action.terminal.scrollToNextCommand", "workbench.action.terminal.selectToPreviousCommand", "workbench.action.terminal.selectToNextCommand", "workbench.action.terminal.selectToPreviousLine", "workbench.action.terminal.selectToNextLine", "workbench.action.terminal.sendSequence", "workbench.action.terminal.newWithCwd", "workbench.action.terminal.renameWithArg", "workbench.action.terminal.relaunch", "workbench.action.terminal.split", "workbench.action.terminal.splitActiveTab", "workbench.action.terminal.unsplit", "workbench.action.terminal.joinActiveTab", "workbench.action.terminal.join", "workbench.action.terminal.splitInActiveWorkspace", "workbench.action.terminal.selectAll", "workbench.action.terminal.new", "workbench.action.terminal.kill", "workbench.action.terminal.killViewOrEditor", "workbench.action.terminal.killAll", "workbench.action.terminal.killEditor", "workbench.action.terminal.killActiveTab", "workbench.action.terminal.focusHover", "workbench.action.terminal.clear", "workbench.action.terminal.selectDefaultShell", "workbench.action.terminal.openSettings", "workbench.action.terminal.setDimensions", "workbench.action.terminal.sizeToContentWidth", "workbench.action.terminal.switchTerminal", "workbench.action.terminal.focusAtIndex1", "workbench.action.terminal.focusAtIndex2", "workbench.action.terminal.focusAtIndex3", "workbench.action.terminal.focusAtIndex4", "workbench.action.terminal.focusAtIndex5", "workbench.action.terminal.focusAtIndex6", "workbench.action.terminal.focusAtIndex7", "workbench.action.terminal.focusAtIndex8", "workbench.action.terminal.focusAtIndex9", "workbench.action.terminal.focusAccessibleBuffer", "workbench.action.terminal.accessibleBufferGoToNextCommand", "workbench.action.terminal.accessibleBufferGoToPreviousCommand", "workbench.action.terminal.scrollToBottomAccessibleView", "workbench.action.terminal.scrollToTopAccessibleView", "workbench.action.terminal.showTextureAtlas", "workbench.action.terminal.writeDataToTerminal", "workbench.action.terminal.recordSession", "workbench.action.terminal.restartPtyHost", "workbench.action.terminal.showEnvironmentContributions", "workbench.action.terminal.copyLastCommand", "workbench.action.terminal.copyLastCommandOutput", "workbench.action.terminal.copyLastCommandAndLastCommandOutput", "workbench.action.terminal.copySelection", "workbench.action.terminal.copyAndClearSelection", "workbench.action.terminal.copySelectionAsHtml", "workbench.action.terminal.paste", "workbench.action.terminal.focusFind", "workbench.action.terminal.hideFind", "workbench.action.terminal.toggleFindRegex", "workbench.action.terminal.toggleFindWholeWord", "workbench.action.terminal.toggleFindCaseSensitive", "workbench.action.terminal.findNext", "workbench.action.terminal.findPrevious", "workbench.action.terminal.searchWorkspace", "workbench.action.terminal.chat.start", "workbench.action.terminal.chat.close", "workbench.action.terminal.chat.runCommand", "workbench.action.terminal.chat.runFirstCommand", "workbench.action.terminal.chat.insertCommand", "workbench.action.terminal.chat.insertFirstCommand", "workbench.action.terminal.chat.rerunRequest", "workbench.action.terminal.chat.viewInChat", "workbench.action.terminal.clearPreviousSessionHistory", "workbench.action.terminal.goToRecentDirectory", "workbench.action.terminal.runRecentCommand", "workbench.action.terminal.openDetectedLink", "workbench.action.terminal.openUrlLink", "workbench.action.terminal.openFileLink", "workbench.action.terminal.fontZoomIn", "workbench.action.terminal.fontZoomOut", "workbench.action.terminal.fontZoomReset", "workbench.action.terminal.toggleStickyScroll", "workbench.action.quickOpenNavigateNextInTerminalPicker", "workbench.action.quickOpenNavigatePreviousInTerminalPicker", "workbench.action.quickOpenTerm", "workbench.action.terminal.showQuickFixes", "workbench.action.terminal.configureSuggestSettings", "workbench.action.terminal.requestCompletions", "workbench.action.terminal.resetSuggestWidgetSize", "workbench.action.terminal.selectPrevSuggestion", "workbench.action.terminal.selectPrevPageSuggestion", "workbench.action.terminal.selectNextSuggestion", "terminalSuggestToggleExplainMode", "workbench.action.terminal.suggestToggleDetailsFocus", "workbench.action.terminal.suggestToggleDetails", "workbench.action.terminal.selectNextPageSuggestion", "workbench.action.terminal.acceptSelectedSuggestion", "workbench.action.terminal.acceptSelectedSuggestionEnter", "workbench.action.terminal.hideSuggestWidget", "workbench.action.terminal.hideSuggestWidgetAndNavigateHistory", "openInTerminal", "openInIntegratedTerminal", "workbench.action.tasks.manageAutomaticRunning", "workbench.action.tasks.rerunForActiveTerminal", "workbench.action.triggerReconnect", "workbench.action.pauseSocketWriting", "resetGettingStartedProgress", "editor.emmet.action.expandAbbreviation", "editor.action.toggleScreenReaderAccessibilityMode", "workbench.action.inspectKeyMappings", "workbench.action.inspectKeyMappingsJSON", "workbench.action.gotoLine", "editor.action.formatC<PERSON><PERSON>", "editor.action.toggleColumnSelection", "editor.action.toggleMinimap", "editor.action.toggleOvertypeInsertMode", "workbench.action.toggleMultiCursorModifier", "editor.action.toggleRenderControlCharacter", "editor.action.toggleRenderWhitespace", "workbench.action.toggleKeybindingsLog", "editor.action.insertSnippet", "editor.action.showSnippets", "editor.action.surroundWithSnippet", "workbench.action.populateFileFromSnippet", "workbench.action.openSnippets", "editor.action.formatDocument.none", "inlayHints.startReadingLineWithHint", "inlayHints.stopReadingLineWithHint", "workbench.action.selectTheme", "workbench.action.selectIconTheme", "workbench.action.selectProductIconTheme", "workbench.action.previewColorTheme", "workbench.action.generateColorTheme", "workbench.action.toggleLightDarkThemes", "workbench.action.browseColorThemesInMarketplace", "update.showCurrentReleaseNotes", "developer.showCurrentFileAsReleaseNotes", "workbench.action.download", "update.checkForUpdate", "update.downloadUpdate", "update.installUpdate", "update.restartToUpdate", "workbench.action.openWalkthrough", "welcome.goBack", "walkthroughs.selectStep", "welcome.markStepComplete", "welcome.markStepIncomplete", "welcome.showAllWalkthroughs", "welcome.showNewWelcome", "welcome.newWorkspaceChat", "workbench.action.showInteractivePlayground", "workbench.action.interactivePlayground.arrowUp", "workbench.action.interactivePlayground.arrowDown", "workbench.action.interactivePlayground.pageUp", "workbench.action.interactivePlayground.pageDown", "welcome.showNewFileEntries", "editor.showCallHierarchy", "editor.showIncomingCalls", "editor.showOutgoingCalls", "editor.refocusCallHierarchy", "editor.closeCallHierarchy", "editor.showTypeHierarchy", "editor.showSupertypes", "editor.showSubtypes", "editor.refocusTypeHierarchy", "editor.closeTypeHierarchy", "outline.collapse", "outline.expand", "outline.followCursor", "outline.filterOnType", "outline.sortByPosition", "outline.sortByName", "outline.sortByKind", "editor.detectLanguage", "editor.inlayHints.Reset", "workbench.getCodeExchangeProxyEndpoints", "workbench.profiles.actions.createTemporaryProfile", "workbench.profiles.actions.cleanupProfiles", "workbench.profiles.actions.resetWorkspaces", "workbench.action.continueOn.extensions", "files.openTimeline", "workbench.action.localHistory.compareWithFile", "workbench.action.localHistory.compareWithPrevious", "workbench.action.localHistory.selectForCompare", "workbench.action.localHistory.compareWithSelected", "workbench.action.localHistory.open", "workbench.action.localHistory.restoreViaEditor", "workbench.action.localHistory.restore", "workbench.action.localHistory.restoreViaPicker", "workbench.action.localHistory.rename", "workbench.action.localHistory.delete", "workbench.action.localHistory.deleteAll", "workbench.action.localHistory.create", "workbench.trust.configure", "workbench.trust.manage", "workbench.action.openWorkspaceFromEditor", "list.resizeColumn", "signals.sounds.help", "accessibility.announcement.help", "toggle.workbench.experimental.share.enabled", "workbench.action.zoomIn", "workbench.action.zoomOut", "workbench.action.zoomReset", "workbench.action.switchWindow", "workbench.action.quickSwitchWindow", "workbench.action.closeWindow", "workbench.action.toggleWindowAlwaysOnTop", "workbench.action.enableWindowAlwaysOnTop", "workbench.action.disableWindowAlwaysOnTop", "workbench.action.quit", "workbench.action.reloadWindowWithExtensionsDisabled", "workbench.action.configureRuntimeArguments", "workbench.action.toggleDevTools", "workbench.action.revealUserDataFolder", "workbench.action.showGPUInfo", "workbench.action.stopTracing", "editor.action.measureExtHostLatency", "workbench.action.restartExtensionHost", "workbench.action.openLogsFolder", "workbench.action.openExtensionLogsFolder", "revealFileInOS", "workbench.action.files.revealActiveFileInWindows", "editor.action.startDebugTextMate", "workbench.extensions.action.debugExtensionHost", "workbench.extensions.action.extensionHostProfile", "workbench.extensions.action.stopExtensionHostProfile", "workbench.extensions.action.saveExtensionHostProfile", "workbench.extensions.action.openExtensionHostProfile", "workbench.action.troubleshootIssue.start", "workbench.action.troubleshootIssue.stop", "workbench.action.files.openLocalFile", "workbench.action.files.openLocalFolder", "workbench.action.files.saveLocalFile", "workbench.remote.action.closeUnusedPorts", "workbench.userData.actions.openSyncBackupsFolder", "workbench.userDataSync.actions.downloadSyncActivity", "workbench.action.terminal.openNativeConsole", "workbench.action.webview.openDeveloperTools", "workbench.action.localHistory.revealInOS", "merge.dev.openContentsJson", "merge.dev.openSelectionInTemporaryMergeEditor", "workbench.action.chat.startVoiceChat", "workbench.action.chat.installProviderForVoiceChat", "workbench.action.chat.voiceChatInChatView", "workbench.action.chat.holdToVoiceChatInChatView", "workbench.action.chat.quickVoiceChat", "workbench.action.chat.inlineVoiceChat", "workbench.action.chat.stopListening", "workbench.action.chat.stopListeningAndSubmit", "workbench.action.chat.readChatResponseAloud", "workbench.action.chat.stopReadChatItemAloud", "workbench.action.speech.stopReadAloud", "workbench.action.chat.openStorageFolder", "inlineChat.holdForSpeech", "workbench.panel.chat.resetViewContainerLocation", "workbench.view.extensions.resetViewContainerLocation", "workbench.view.extension.test.resetViewContainerLocation", "workbench.view.explorer.resetViewContainerLocation", "workbench.view.search.resetViewContainerLocation", "workbench.view.scm.resetViewContainerLocation", "workbench.view.debug.resetViewContainerLocation", "workbench.view.remote.resetViewContainerLocation", "workbench.panel.testResults.resetViewContainerLocation", "refactorPreview.resetViewContainerLocation", "workbench.panel.repl.resetViewContainerLocation", "workbench.panel.markers.resetViewContainerLocation", "workbench.panel.output.resetViewContainerLocation", "terminal.resetViewContainerLocation", "workbench.panel.chat", "workbench.panel.chat.view.copilot.focus", "workbench.panel.chat.view.copilot.resetViewLocation", "workbench.views.mcp.installed.open", "workbench.views.mcp.installed.focus", "workbench.views.mcp.installed.resetViewLocation", "workbench.views.mcp.marketplace.open", "workbench.views.mcp.marketplace.focus", "workbench.views.mcp.marketplace.resetViewLocation", "workbench.view.extensions", "workbench.view.testing.open", "workbench.view.testing.focus", "workbench.view.testing.resetViewLocation", "workbench.view.testCoverage.open", "workbench.view.testCoverage.focus", "workbench.view.testCoverage.resetViewLocation", "workbench.view.extension.test", "outline.open", "outline.focus", "outline.resetViewLocation", "timeline.open", "timeline.focus", "timeline.resetViewLocation", "workbench.view.explorer", "workbench.view.search", "workbench.view.search.focus", "workbench.view.search.resetViewLocation", "workbench.scm.repositories.open", "workbench.scm.repositories.focus", "workbench.scm.repositories.resetViewLocation", "workbench.view.scm", "workbench.scm.focus", "workbench.scm.resetViewLocation", "workbench.scm.history.open", "workbench.scm.history.focus", "workbench.scm.history.resetViewLocation", "workbench.debug.welcome.open", "workbench.debug.welcome.focus", "workbench.debug.welcome.resetViewLocation", "workbench.debug.variablesView.open", "workbench.debug.action.focusVariablesView", "workbench.debug.variablesView.resetViewLocation", "workbench.debug.watchExpressionsView.open", "workbench.debug.action.focusWatchView", "workbench.debug.watchExpressionsView.resetViewLocation", "workbench.debug.callStackView.open", "workbench.debug.action.focusCallStackView", "workbench.debug.callStackView.resetViewLocation", "workbench.debug.loadedScriptsView.open", "workbench.debug.loadedScriptsView.focus", "workbench.debug.loadedScriptsView.resetViewLocation", "workbench.debug.breakPointsView.open", "workbench.debug.action.focusBreakpointsView", "workbench.debug.breakPointsView.resetViewLocation", "workbench.view.debug", "workbench.view.remote", "workbench.panel.testResults.view.open", "workbench.panel.testResults.view.focus", "workbench.panel.testResults.view.resetViewLocation", "refactorPreview.open", "refactorPreview.focus", "refactorPreview.resetViewLocation", "refactorPreview", "workbench.debug.action.toggleRepl", "workbench.panel.repl.view.focus", "workbench.panel.repl.view.resetViewLocation", "workbench.actions.view.problems", "workbench.panel.markers.view.focus", "workbench.panel.markers.view.resetViewLocation", "workbench.action.output.toggleOutput", "workbench.panel.output.focus", "workbench.panel.output.resetViewLocation", "workbench.action.terminal.toggleTerminal", "terminal.focus", "terminal.resetViewLocation", "workbench.action.focusTitleBar", "registerWindowTitleVariable", "workbench.action.openSettings", "workbench.action.openSettings2", "workbench.action.openSettingsJson", "workbench.action.openApplicationSettingsJson", "workbench.action.openGlobalSettings", "workbench.action.openRawDefaultSettings", "workbench.action.configureLanguageBasedSettings", "workbench.action.openWorkspaceSettings", "workbench.action.openAccessibilitySettings", "workbench.action.openWorkspaceSettingsFile", "workbench.action.openFolderSettings", "workbench.action.openFolderSettingsFile", "settings.filterByOnline", "settings.action.toggleAiSearch", "settings.filterUntrusted", "settings.filterByTelemetry", "settings.action.search", "settings.action.clearSearchResults", "settings.action.focusSettingsFile", "settings.action.focusSettingsFromSearch", "settings.action.focusSettingsList", "settings.action.focusTOC", "settings.action.focusSettingControl", "settings.action.showContextMenu", "settings.action.focusLevelUp", "workbench.action.openGlobalKeybindings", "workbench.action.openDefaultKeybindingsFile", "workbench.action.openGlobalKeybindingsFile", "keybindings.editor.showDefaultKeybindings", "keybindings.editor.showExtensionKeybindings", "keybindings.editor.showUserKeybindings", "keybindings.editor.clearSearchResults", "keybindings.editor.clearSearchHistory", "keybindings.editor.defineKeybinding", "keybindings.editor.addKeybinding", "keybindings.editor.defineWhenExpression", "keybindings.editor.removeKeybinding", "keybindings.editor.resetKeybinding", "keybindings.editor.searchKeybindings", "keybindings.editor.recordSearchKeys", "keybindings.editor.toggleSortByPrecedence", "keybindings.editor.showConflicts", "keybindings.editor.copyKeybindingEntry", "keybindings.editor.copyCommandKeybindingEntry", "keybindings.editor.copyCommandTitle", "keybindings.editor.focusKeybindings", "keybindings.editor.rejectWhenExpression", "keybindings.editor.acceptWhenExpression", "editor.action.defineKeybinding", "workbench.action.toggleActivityBarVisibility", "workbench.action.remote.showMenu", "workbench.action.remote.close", "workbench.action.remote.extensions", "changeEditorIndentation2", "workbench.action.chat.triggerSetup", "workbench.action.chat.triggerSetupFromAccounts", "workbench.action.chat.triggerSetupWithoutDialog", "workbench.action.chat.hideSetup", "workbench.action.chat.upgradePlan", "workbench.action.chat.manageOverages", "workbench.profiles.actions.manageProfiles", "workbench.profiles.actions.switchProfile", "workbench.profiles.actions.newWindowWithProfile", "workbench.profiles.actions.profileEntry.__default__profile__", "workbench.action.openProfile.默认", "workbench.profiles.actions.exportProfile", "workbench.profiles.actions.createFromCurrentProfile", "workbench.profiles.actions.createProfile", "workbench.profiles.actions.deleteProfile", "workbench.profiles.actions.help", "showEditorScreenReaderNotification", "workbench.action.showHover", "notifications.showList", "notifications.hideList", "notifications.toggleList", "notification.clear", "notification.expand", "notification.acceptPrimaryAction", "notification.collapse", "notification.toggle", "notifications.hideToasts", "notifications.focusToasts", "notifications.focusNextToast", "notifications.focusPreviousToast", "notifications.focusFirstToast", "notifications.focusLastToast", "notifications.clearAll", "notifications.toggleDoNotDisturbMode", "notifications.toggleDoNotDisturbModeBySource", "workbench.explorer.openEditorsView.open", "workbench.files.action.focusOpenEditorsView", "workbench.explorer.openEditorsView.resetViewLocation", "workbench.explorer.fileView.open", "workbench.explorer.fileView.focus", "workbench.explorer.fileView.resetViewLocation", "workbench.view.extension.references-view.resetViewContainerLocation", "workbench.view.extension.references-view", "workbench.view.extension.codegeex-sidebar.resetViewContainerLocation", "workbench.view.extension.codegeex-sidebar", "workbench.view.extension.llm-bridge-container.resetViewContainerLocation", "workbench.view.extension.llm-bridge-container", "jsBrowserBreakpoints.open", "jsBrowserBreakpoints.focus", "jsBrowserBreakpoints.resetViewLocation", "jsExcludedCallers.open", "jsExcludedCallers.focus", "jsExcludedCallers.resetViewLocation", "jsDebugNetworkTree.open", "jsDebugNetworkTree.focus", "jsDebugNetworkTree.resetViewLocation", "npm.open", "npm.focus", "npm.resetViewLocation", "references-view.tree.open", "references-view.tree.focus", "references-view.tree.resetViewLocation", "codegeex-qa.open", "codegeex-qa.focus", "codegeex-qa.resetViewLocation", "CppReferencesView.open", "CppReferencesView.focus", "CppReferencesView.resetViewLocation", "CppSshTargetsView.open", "CppSshTargetsView.focus", "CppSshTargetsView.resetViewLocation", "llm-bridge.chatView.open", "llm-bridge.chatView.focus", "llm-bridge.chatView.resetViewLocation", "workbench.view.search.toggleVisibility", "workbench.view.search.removeView", "workbench.scm.toggleVisibility", "workbench.scm.removeView", "workbench.debug.welcome.toggleVisibility", "workbench.debug.welcome.removeView", "workbench.panel.markers.view.toggleVisibility", "workbench.panel.markers.view.removeView", "workbench.panel.output.toggleVisibility", "workbench.panel.output.removeView", "terminal.toggleVisibility", "terminal.removeView", "codegeex-qa.toggleVisibility", "codegeex-qa.remove<PERSON>iew", "llm-bridge.chatView.toggleVisibility", "llm-bridge.chatView.removeView", "workbench.action.openRemoteSettings", "workbench.action.openRemoteSettingsFile", "workbench.action.tasks.runTask", "workbench.action.tasks.reRunTask", "workbench.action.tasks.restartTask", "workbench.action.tasks.terminate", "workbench.action.tasks.showLog", "workbench.action.tasks.build", "workbench.action.tasks.test", "workbench.action.tasks.configureTaskRunner", "workbench.action.tasks.configureDefaultBuildTask", "workbench.action.tasks.configureDefaultTestTask", "workbench.action.tasks.showTasks", "workbench.action.tasks.toggleProblems", "workbench.action.tasks.openUserTasks", "workbench.action.tasks.openWorkspaceFileTasks", "workbench.action.showTelemetry", "settings.switchToJSON", "workbench.extensions.action.focusExtensionsView", "workbench.extensions.action.installExtensions", "workbench.extensions.action.showRecommendedKeymapExtensions", "workbench.extensions.action.showLanguageExtensions", "workbench.extensions.action.checkForUpdates", "workbench.extensions.action.enableAutoUpdate", "workbench.extensions.action.disableAutoUpdate", "workbench.extensions.action.updateAllExtensions", "workbench.extensions.action.enableAll", "workbench.extensions.action.enableAllWorkspace", "workbench.extensions.action.disableAll", "workbench.extensions.action.disableAllWorkspace", "workbench.extensions.action.installVSIX", "workbench.extensions.command.installFromVSIX", "workbench.extensions.action.installExtensionFromLocation", "extensions.filter.featured", "workbench.extensions.action.showPopularExtensions", "workbench.extensions.action.showRecommendedExtensions", "workbench.extensions.action.recentlyPublishedExtensions", "extensions.actions.searchByCategory.AI", "extensions.actions.searchByCategory.Azure", "extensions.actions.searchByCategory.Chat", "extensions.actions.searchByCategory.Data Science", "extensions.actions.searchByCategory.Debuggers", "extensions.actions.searchByCategory.Extension Packs", "extensions.actions.searchByCategory.Education", "extensions.actions.searchByCategory.Formatters", "extensions.actions.searchByCategory.Keymaps", "extensions.actions.searchByCategory.Language Packs", "extensions.actions.searchByCategory.Linters", "extensions.actions.searchByCategory.Machine Learning", "extensions.actions.searchByCategory.Notebooks", "extensions.actions.searchByCategory.Programming Languages", "extensions.actions.searchByCategory.SCM Providers", "extensions.actions.searchByCategory.Snippets", "extensions.actions.searchByCategory.Testing", "extensions.actions.searchByCategory.Themes", "extensions.actions.searchByCategory.Visualization", "extensions.actions.searchByCategory.Other", "workbench.extensions.action.installedExtensions", "workbench.extensions.action.listBuiltInExtensions", "workbench.extensions.action.extensionUpdates", "workbench.extensions.action.listWorkspaceUnsupportedExtensions", "workbench.extensions.action.showEnabledExtensions", "workbench.extensions.action.showDisabledExtensions", "extensions.sort.installs", "extensions.sort.rating", "extensions.sort.name", "extensions.sort.publishedDate", "extensions.sort.updateDate", "workbench.extensions.action.clearExtensionsSearchResults", "workbench.extensions.action.refreshExtension", "workbench.extensions.action.installWorkspaceRecommendedExtensions", "workbench.extensions.action.configureWorkspaceFolderRecommendedExtensions", "workbench.extensions.action.install.specificVersion", "workbench.extensions.action.setColorTheme", "workbench.extensions.action.setFileIconTheme", "workbench.extensions.action.setProductIconTheme", "workbench.extensions.action.showPreReleaseVersion", "workbench.extensions.action.showReleasedVersion", "workbench.extensions.action.toggleAutoUpdateForExtension", "workbench.extensions.action.toggleAutoUpdatesForPublisher", "workbench.extensions.action.switchToPreRlease", "workbench.extensions.action.switchToRelease", "workbench.extensions.action.clearLanguage", "workbench.extensions.action.installUnsigned", "workbench.extensions.action.installAndDonotSync", "workbench.extensions.action.installPrereleaseAndDonotSync", "workbench.extensions.action.install.anotherVersion", "workbench.extensions.action.copyExtension", "workbench.extensions.action.copyExtensionId", "workbench.extensions.action.copyLink", "workbench.extensions.action.configure", "workbench.extensions.action.download", "workbench.extensions.action.downloadPreRelease", "workbench.extensions.action.downloadSpecificVersion", "workbench.extensions.action.manageAccountPreferences", "workbench.extensions.action.configureKeybindings", "workbench.extensions.action.toggleApplyToAllProfiles", "workbench.extensions.action.toggleIgnoreExtension", "workbench.extensions.action.ignoreRecommendation", "workbench.extensions.action.undoIgnoredRecommendation", "workbench.extensions.action.addExtensionToWorkspaceRecommendations", "workbench.extensions.action.removeExtensionFromWorkspaceRecommendations", "workbench.extensions.action.addToWorkspaceRecommendations", "workbench.extensions.action.addToWorkspaceFolderRecommendations", "workbench.extensions.action.addToWorkspaceIgnoredRecommendations", "workbench.extensions.action.addToWorkspaceFolderIgnoredRecommendations", "workbench.extensions.action.configureWorkspaceRecommendedExtensions", "workbench.extensions.action.manageTrustedPublishers", "workbench.views.extensions.installed.open", "workbench.views.extensions.installed.focus", "workbench.views.extensions.installed.resetViewLocation", "workbench.views.extensions.popular.open", "workbench.views.extensions.popular.focus", "workbench.views.extensions.popular.resetViewLocation", "extensions.recommendedList.open", "extensions.recommendedList.focus", "extensions.recommendedList.resetViewLocation", "workbench.views.extensions.enabled.open", "workbench.views.extensions.enabled.focus", "workbench.views.extensions.enabled.resetViewLocation", "workbench.views.extensions.disabled.open", "workbench.views.extensions.disabled.focus", "workbench.views.extensions.disabled.resetViewLocation", "workbench.views.extensions.marketplace.open", "workbench.views.extensions.marketplace.focus", "workbench.views.extensions.marketplace.resetViewLocation", "workbench.views.extensions.searchInstalled.open", "workbench.views.extensions.searchInstalled.focus", "workbench.views.extensions.searchInstalled.resetViewLocation", "workbench.views.extensions.searchRecentlyUpdated.open", "workbench.views.extensions.searchRecentlyUpdated.focus", "workbench.views.extensions.searchRecentlyUpdated.resetViewLocation", "workbench.views.extensions.searchEnabled.open", "workbench.views.extensions.searchEnabled.focus", "workbench.views.extensions.searchEnabled.resetViewLocation", "workbench.views.extensions.searchDisabled.open", "workbench.views.extensions.searchDisabled.focus", "workbench.views.extensions.searchDisabled.resetViewLocation", "workbench.views.extensions.searchOutdated.open", "workbench.views.extensions.searchOutdated.focus", "workbench.views.extensions.searchOutdated.resetViewLocation", "workbench.views.extensions.searchBuiltin.open", "workbench.views.extensions.searchBuiltin.focus", "workbench.views.extensions.searchBuiltin.resetViewLocation", "workbench.views.extensions.searchWorkspaceUnsupported.open", "workbench.views.extensions.searchWorkspaceUnsupported.focus", "workbench.views.extensions.searchWorkspaceUnsupported.resetViewLocation", "workbench.views.extensions.workspaceRecommendations.open", "workbench.views.extensions.workspaceRecommendations.focus", "workbench.views.extensions.workspaceRecommendations.resetViewLocation", "workbench.views.extensions.otherRecommendations.open", "workbench.views.extensions.otherRecommendations.focus", "workbench.views.extensions.otherRecommendations.resetViewLocation", "workbench.views.extensions.builtinFeatureExtensions.open", "workbench.views.extensions.builtinFeatureExtensions.focus", "workbench.views.extensions.builtinFeatureExtensions.resetViewLocation", "workbench.views.extensions.builtinThemeExtensions.open", "workbench.views.extensions.builtinThemeExtensions.focus", "workbench.views.extensions.builtinThemeExtensions.resetViewLocation", "workbench.views.extensions.builtinProgrammingLanguageExtensions.open", "workbench.views.extensions.builtinProgrammingLanguageExtensions.focus", "workbench.views.extensions.builtinProgrammingLanguageExtensions.resetViewLocation", "workbench.views.extensions.untrustedUnsupportedExtensions.open", "workbench.views.extensions.untrustedUnsupportedExtensions.focus", "workbench.views.extensions.untrustedUnsupportedExtensions.resetViewLocation", "workbench.views.extensions.untrustedPartiallySupportedExtensions.open", "workbench.views.extensions.untrustedPartiallySupportedExtensions.focus", "workbench.views.extensions.untrustedPartiallySupportedExtensions.resetViewLocation", "workbench.views.extensions.virtualUnsupportedExtensions.open", "workbench.views.extensions.virtualUnsupportedExtensions.focus", "workbench.views.extensions.virtualUnsupportedExtensions.resetViewLocation", "workbench.views.extensions.virtualPartiallySupportedExtensions.open", "workbench.views.extensions.virtualPartiallySupportedExtensions.focus", "workbench.views.extensions.virtualPartiallySupportedExtensions.resetViewLocation", "workbench.views.extensions.deprecatedExtensions.open", "workbench.views.extensions.deprecatedExtensions.focus", "workbench.views.extensions.deprecatedExtensions.resetViewLocation", "workbench.action.terminal.newWithProfile", "workbench.action.showWindowLog", "workbench.output.action.switchBetweenOutputs", "workbench.action.output.show.tasks", "workbench.action.output.show.main", "workbench.action.output.show.shared", "workbench.action.output.show.userDataSync", "workbench.action.output.show.ptyhost", "workbench.action.output.show.rendererLog", "workbench.action.output.show.terminal", "workbench.action.output.addCompoundLog", "workbench.action.output.remove", "workbench.action.showOutputChannels", "workbench.output.action.clearOutput", "workbench.output.action.toggleAutoScroll", "workbench.action.openActiveLogOutputFile", "workbench.action.openActiveLogOutputFileInNewWindow", "workbench.action.saveActiveLogOutputAs", "workbench.action.showLogs", "workbench.action.openLogFile", "workbench.action.output.activeOutputLogLevel.1", "workbench.action.output.activeOutputLogLevel.2", "workbench.action.output.activeOutputLogLevel.3", "workbench.action.output.activeOutputLogLevel.4", "workbench.action.output.activeOutputLogLevel.5", "workbench.action.output.activeOutputLogLevel.0", "workbench.action.output.activeOutputLogLevelDefault", "workbench.actions.workbench.panel.output.toggle.trace", "workbench.actions.workbench.panel.output.toggle.debug", "workbench.actions.workbench.panel.output.toggle.info", "workbench.actions.workbench.panel.output.toggle.warn", "workbench.actions.workbench.panel.output.toggle.error", "workbench.actions.workbench.panel.output.clearFilterText", "workbench.action.exportLogs", "workbench.action.importLog", "~remote.forwardedPortsContainer.resetViewContainerLocation", "~remote.forwardedPortsContainer", "~remote.forwardedPorts.open", "~remote.forwardedPorts.focus", "~remote.forwardedPorts.resetViewLocation", "~remote.forwardedPorts.toggleVisibility", "~remote.forwardedPorts.removeView", "update.check", "update.checking", "update.downloadNow", "update.downloading", "update.install", "update.updating", "update.showUpdateReleaseNotes", "update.restart", "workbench.action.removeDynamicAuthenticationProviders", "workbench.userDataSync.actions.turnOn", "workbench.userDataSync.actions.turnOff", "workbench.userData.actions.turningOn", "workbench.userData.actions.cancelTurnOn", "workbench.userData.actions.signin", "workbench.userDataSync.actions.showConflicts", "workbench.userDataSync.actions.showSyncedData", "workbench.userDataSync.actions.manage", "workbench.userDataSync.actions.syncNow", "workbench.userDataSync.actions.configure", "workbench.userDataSync.actions.settings", "workbench.userDataSync.actions.help", "workbench.userDataSync.actions.showLog", "workbench.actions.syncData.reset", "workbench.userDataSync.actions.acceptMerges", "workbench.view.sync.resetViewContainerLocation", "workbench.view.sync", "workbench.views.sync.conflicts.open", "workbench.views.sync.conflicts.focus", "workbench.views.sync.conflicts.resetViewLocation", "workbench.views.sync.remoteActivity.open", "workbench.views.sync.remoteActivity.focus", "workbench.views.sync.remoteActivity.resetViewLocation", "workbench.actions.sync.workbench.views.sync.remoteActivity.resolveResource", "workbench.actions.sync.workbench.views.sync.remoteActivity.compareWithLocal", "workbench.actions.sync.workbench.views.sync.remoteActivity.replaceCurrent", "workbench.views.sync.machines.open", "workbench.views.sync.machines.focus", "workbench.views.sync.machines.resetViewLocation", "workbench.actions.sync.editMachineName", "workbench.actions.sync.turnOffSyncOnMachine", "workbench.views.sync.localActivity.open", "workbench.views.sync.localActivity.focus", "workbench.views.sync.localActivity.resetViewLocation", "workbench.actions.sync.workbench.views.sync.localActivity.resolveResource", "workbench.actions.sync.workbench.views.sync.localActivity.compareWithLocal", "workbench.actions.sync.workbench.views.sync.localActivity.replaceCurrent", "workbench.views.sync.troubleshoot.open", "workbench.views.sync.troubleshoot.focus", "workbench.views.sync.troubleshoot.resetViewLocation", "workbench.views.sync.externalActivity.open", "workbench.views.sync.externalActivity.focus", "workbench.views.sync.externalActivity.resetViewLocation", "workbench.actions.sync.loadActivity", "workbench.editSessions.actions.signIn", "workbench.editSessions.actions.resetAuth", "workbench.editSessions.actions.resumeLatest", "workbench.editSessions.actions.resumeFromSerializedPayload", "workbench.editSessions.actions.storeCurrent", "workbench.editSessions.actions.showEditSessions", "workbench.editSessions.actions.showOutputChannel", "workbench.view.editSessions.resetViewContainerLocation", "workbench.views.editSessions.data.open", "workbench.views.editSessions.data.focus", "workbench.views.editSessions.data.resetViewLocation", "workbench.editSessions.actions.resume", "workbench.editSessions.actions.store", "workbench.editSessions.actions.delete", "workbench.editSessions.actions.deleteAll", "workbench.extensions.action.openExtensionsFolder", "workbench.accounts.actions.signin", "workbench.action.openIssueReporter", "vscode.openIssueReporter", "workbench.action.reportPerformanceIssueUsingReporter", "workbench.action.output.show.remoteTunnelService", "workbench.remoteTunnel.actions.turnOn", "workbench.remoteTunnel.actions.manage", "workbench.remoteTunnel.actions.connecting", "workbench.remoteTunnel.actions.turnOff", "workbench.remoteTunnel.actions.showLog", "workbench.remoteTunnel.actions.configure", "workbench.remoteTunnel.actions.copyToClipboard", "workbench.remoteTunnel.actions.learnMore", "keywordActivation.status.command", "workbench.extensions.installMissingDependencies", "workbench.action.toggleLockedScrolling", "workbench.action.holdLockedScrolling", "workbench.action.configureLocale", "workbench.action.clearLocalePreference", "workbench.action.extensionHostProfiler.stop", "workbench.action.output.show.exthost", "workbench.panel.repl.view.toggleVisibility", "workbench.panel.repl.view.removeView", "editor.emmet.action.updateImageSize", "editor.emmet.action.wrapWithAbbreviation", "emmet.expandAbbreviation", "editor.emmet.action.removeTag", "editor.emmet.action.updateTag", "editor.emmet.action.matchTag", "editor.emmet.action.balanceOut", "editor.emmet.action.balanceIn", "editor.emmet.action.splitJoinTag", "editor.emmet.action.mergeLines", "editor.emmet.action.toggleComment", "editor.emmet.action.nextEditPoint", "editor.emmet.action.prevEditPoint", "editor.emmet.action.selectNextItem", "editor.emmet.action.selectPrevItem", "editor.emmet.action.evaluateMathExpression", "editor.emmet.action.incrementNumberByOneTenth", "editor.emmet.action.incrementNumberByOne", "editor.emmet.action.incrementNumberByTen", "editor.emmet.action.decrementNumberByOneTenth", "editor.emmet.action.decrementNumberByOne", "editor.emmet.action.decrementNumberByTen", "editor.emmet.action.reflectCSSValue", "workbench.action.showEmmetCommands", "typescript.reloadProjects", "javascript.reloadProjects", "typescript.selectTypeScriptVersion", "typescript.openTsServerLog", "typescript.restartTsServer", "typescript.goToProjectConfig", "javascript.goToProjectConfig", "typescript.tsserverRequest", "typescript.experimental.enableTsgo", "typescript.experimental.disableTsgo", "git-base.api.getRemoteSources", "json.clearCache", "json.validate", "json.sort", "workbench.action.output.show.vscode.github-authentication.GitHub Authentication", "workbench.action.output.show.AMiner.codegeex.CodeGeeX", "workbench.action.output.show.AMiner.codegeex.CodeGeeX Local", "workbench.action.output.show.vscode.json-language-features.JSON 语言服务器", "git.showOutput", "git.refresh", "git.openResource", "git.openAllChanges", "git.openMergeEditor", "git.continueInLocalClone", "git.clone", "git.cloneRecursive", "git.init", "git.openRepository", "git.reopenClosedRepositories", "git.close", "git.closeOtherRepositories", "git.openFile", "git.openFile2", "git.openHEADFile", "git.openChange", "git.rename", "git.stage", "git.stageAll", "git.stageAllTracked", "git.stageAllUntracked", "git.stageAllMerge", "git.stage<PERSON>hange", "git.diff.stageHunk", "git.diff.stageSelection", "git.stageSelectedRanges", "git.stageFile", "git.acceptMerge", "git.runGitMerge", "git.runGitMergeDiff3", "git.revertChange", "git.revertSelectedRanges", "git.unstage", "git.unstageAll", "git.unstageSelectedRanges", "git.unstageFile", "git.unstageChange", "git.clean", "git.cleanAll", "git.cleanAllTracked", "git.cleanAllUntracked", "git.commit", "git.commitAmend", "git.commitSigned", "git.commitStaged", "git.commitStagedSigned", "git.commitStagedAmend", "git.commitAll", "git.commitAllSigned", "git.commitAllAmend", "git.commitMessageAccept", "git.commitMessageDiscard", "git.commitEmpty", "git.commitNoVerify", "git.commitStagedNoVerify", "git.commitStagedSignedNoVerify", "git.commitAmendNoVerify", "git.commitSignedNoVerify", "git.commitStagedAmendNoVerify", "git.commitAllNoVerify", "git.commitAllSignedNoVerify", "git.commitAllAmendNoVerify", "git.commitEmptyNoVerify", "git.restoreCommitTemplate", "git.undoCommit", "git.checkout", "git.graph.checkout", "git.checkoutDetached", "git.graph.checkoutDetached", "git.branch", "git.branchFrom", "git.deleteBranch", "git.graph.deleteBranch", "git.deleteRemoteBranch", "git.renameBranch", "git.merge", "git.mergeAbort", "git.rebase", "git.createTag", "git.deleteTag", "git.graph.deleteTag", "git.deleteRemoteTag", "git.fetch", "git.fetch<PERSON>rune", "git.fetchAll", "git.fetchRef", "git.pullFrom", "git.pull", "git.pullRebase", "git.pullRef", "git.push", "git.pushForce", "git.pushWithTags", "git.pushWithTagsForce", "git.pushRef", "git.cherry<PERSON>ick", "git.graph.cherryPick", "git.cherryPickAbort", "git.pushTo", "git.pushToForce", "git.pushTags", "git.addRemote", "git.removeRemote", "git.sync", "git._syncAll", "git.syncRebase", "git.publish", "git.ignore", "git.revealInExplorer", "git.revealFileInOS.windows", "git.revealFileInOS.mac", "git.revealFileInOS.linux", "git.stash", "git.stashStaged", "git.stashIncludeUntracked", "git.stashPop", "git.stashPopLatest", "git.stashPopEditor", "git.stashApply", "git.stashApplyLatest", "git.stashApplyEditor", "git.stashDrop", "git.stashDropAll", "git.stashDropEditor", "git.stashView", "git.timeline.openDiff", "git.timeline.viewCommit", "git.timeline.copyCommitId", "git.timeline.copyCommitMessage", "git.timeline.selectForCompare", "git.timeline.compareWithSelected", "git.rebaseAbort", "git.closeAllDiffEditors", "git.closeAllUnmodifiedEditors", "git.openRepositoriesInParentFolders", "git.manageUnsafeRepositories", "git.viewChanges", "git.viewStagedChanges", "git.viewUntrackedChanges", "git.copyCommitId", "git.copyCommitMessage", "git.viewCommit", "git.copyContentToClipboard", "git.blame.toggleEditorDecoration", "git.blame.toggleStatusBarItem", "git.api.getRepositories", "git.api.getRepositoryState", "git.api.getRemoteSources", "github.publish", "github.copyVscodeDevLink", "github.copyVscodeDevLinkFile", "github.copyVscodeDevLinkWithoutRange", "github.openOnGitHub", "github.graph.openOnGitHub", "github.timeline.openOnGitHub", "github.openOnVscodeDev", "workbench.action.output.show.vscode.git.Git", "workbench.action.output.show.vscode.github.GitHub", "codegeex.login", "codegeex.inlineChat.input", "codegeex.askcodegeex", "codegeex.askcodegeex.chat", "codegeex.askcodegeex.explain", "codegeex.askcodegeex.tests", "codegeex.problem.fix", "codegeex.problem.fix.chat", "codegeex.askcodegeex.comment.chat", "codegeex.agent.newChat", "codegeex.agent.stop", "codegeex.agent.debugChat", "codegeex.codelens.undo", "codegeex.codelens.done", "codegeex.codelens.list", "codegeex.codelens.addDocString", "codegeex.codelens.addExceptionHandling", "codegeex.codelens.printLogForDebugging", "codegeex.codelens.renameSymbols", "codegeex.codelens.debugInNewFile", "codegeex.codelens.disable", "codegeex.codelens.addDocStringForClass", "codegeex.codelens.addDocStringForMethods", "codegeex.inlineChat.generate", "codegeex.inlineChat.reask", "codegeex.inlineChat.terminate", "codegeex.codelens.enable", "codegeex.review", "codegeex.commit.message", "codegeex.toolbar.add", "codegeex.toolbar.delete", "codegeex.toolbar.history", "codegeex.toolbar.more.settings", "codegeex.toolbar.more.newTab", "codegeex.toolbar.more.toolbox", "codegeex.toolbar.more.help", "codegeex.toolbar.more.feedback", "codegeex.toolbar.more.localMode", "codegeex.toolbar.more.onlineMode", "codegeex.toolbar.split", "codegeex.annotation.keybindings.disable", "codegeex.askcodegeex.scanCode", "codegeex.WelcomeGuide.show", "codegeex.WelcomeGuide.showFunction", "codegeex.mermaid.preview", "codegeex.askcodegeex.comment", "codegeex.terminal.ask", "codegeex.explorer.projectMap", "codegeex.projectMap.removeHistory", "codegeex.editor.fileUML", "codegeex.ghostComment.accept", "codegeex.ghostComment.ask", "codegeex.ghostComment.detail", "codegeex.ghostComment.example", "codegeex.ghostComment.auto", "codegeex.ghostComment.inAutoMode", "codegeex.ghostComment.resetlastused", "codegeex.ghostComment", "codegeex.ghostComment.hide", "codegeex.translate-mode", "codegeex.disable-enable", "codegeex.chat.focus", "codegeex.chat.toggle", "extension.node-debug.toggleAutoAttach", "merge-conflict.accept.current", "merge-conflict.accept.incoming", "merge-conflict.accept.selection", "merge-conflict.accept.both", "merge-conflict.accept.all-current", "merge-conflict.accept.all-incoming", "merge-conflict.accept.all-both", "merge-conflict.next", "merge-conflict.previous", "merge-conflict.compare", "codegeex.trigger-completions", "codegeex.new-completions", "codegeex.verifyInsertion", "npm.runScript", "npm.debugScript", "npm.openScript", "npm.runInstall", "npm.runScriptFromHover", "npm.debugScriptFromHover", "npm.runSelectedScript", "workbench.explorer.openEditorsView.toggleVisibility", "workbench.explorer.openEditorsView.removeView", "workbench.explorer.fileView.toggleVisibility", "workbench.explorer.fileView.removeView", "outline.toggleVisibility", "outline.remove<PERSON>iew", "timeline.toggleVisibility", "timeline.<PERSON><PERSON>iew", "npm.toggleVisibility", "npm.removeView", "npm.runScriptFromFolder", "npm.refresh", "npm.script<PERSON><PERSON>ner", "npm.packageManager"], "id": 3}