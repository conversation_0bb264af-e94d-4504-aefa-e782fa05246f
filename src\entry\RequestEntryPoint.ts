import * as vscode from 'vscode';

import type { Orchestrator } from '../verb/Orchestrator.js';

/**
 * @description 用户请求入口层，监听用户操作并触发主流程。
 */
export class RequestEntryPoint {
  private _orchestrator: Orchestrator | null = null;

  public set_orchestrator(orchestrator: Orchestrator): void {
    this._orchestrator = orchestrator;
  }

  /**
   * 监听并处理用户请求（如按钮、输入等）
   * @param user_input 用户输入
   * @param model_key The key for the model to be used
   */
  async handle_user_request(user_input: string, model_key: string): Promise<void> {
    if (!this._orchestrator) {
      void vscode.window.showErrorMessage('Orchestrator not initialized in RequestEntryPoint');
      return;
    }
    await this._orchestrator.handle_request(user_input, model_key);
  }
}
