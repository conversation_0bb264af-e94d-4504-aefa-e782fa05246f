# MCP over HTTP/SSE: 疑难杂症诊断与修复指南

## 1. 背景

在将本项目的MCP传输层完全重构为 `Stdio` 之前，我们曾深入探索了基于 `HTTP/1.1` 和 `Server-Sent Events (SSE)` 的实现。尽管 `Stdio` 是本地IPC的最佳选择，但这次探索为我们提供了宝贵的底层网络调试经验，并揭示了 `@modelcontextprotocol/sdk` 在 `v1.12.0` 至 `v1.13.1` 版本中存在的具体实现缺陷。

本文档旨在记录我们遇到的问题、诊断过程和最终采用的“外科手术式”修复方案，为未来可能需要处理类似问题的开发者提供一份详细的实战指南。

## 2. 遇到的核心问题

我们遇到了两个看似无关，但都源于SDK `StreamableHTTPServerTransport` 实现缺陷的核心问题：

### 问题A: Node.js 客户端连接后立即抛出 `SyntaxError`

- **现象**: 使用官方的Node.js客户端连接MCP服务器后，客户端会立即因解析错误而崩溃。
- **根本原因**: MCP over SSE 协议要求服务器在发送HTTP头之后，立即开始发送符合SSE格式的数据流。一个合法的SSE流可以以注释行（以 `:` 开头）开始。然而，我们使用的SDK版本在发送完HTTP `200 OK` 响应头后，并未立即发送任何有效数据。这种“沉默”状态导致Node.js的SSE解析器认为流已损坏，从而抛出语法错误。

### 问题B: 浏览器客户端发起 `resources/list` 请求时返回 `406 Not Acceptable`

- **现象**: 在浏览器中加载的客户端可以成功建立SSE连接，但当它通过 `POST` 请求调用 `resources/list` 方法时，服务器返回HTTP `406` 错误。
- **根本原因**: HTTP `406` 错误意味着服务器无法提供客户端在 `Accept` 请求头中声明的任何内容类型。经过深度日志分析，我们发现SDK的 `StreamableHTTPServerTransport` 在处理 `POST` 请求时，其内部的内容协商逻辑存在缺陷。它未能正确匹配客户端 `Accept: application/json` 的要求和服务器端生成 `application/json` 响应的能力，错误地拒绝了本应合法的请求。

## 3. 诊断过程：深入底层

解决这些问题的关键在于深入协议和代码的底层：

1.  **开启深度日志**: 我们修改了 `StreamableHTTPServerTransport.js` 的源码，在处理请求和响应的关键路径上添加了大量的 `console.log`，打印出完整的HTTP请求头、请求体、响应头和响应数据。
2.  **网络抓包分析**: 使用 `curl -v` 命令手动向服务器发送请求，观察最原始的HTTP响应头和数据流。这帮助我们确认了问题A中服务器在握手后“沉默”的事实。
3.  **代码走读**: 我们仔细阅读了SDK中处理GET（SSE连接）和POST（RPC调用）请求的逻辑分支，最终定位到了问题B中内容协商失败的具体代码位置。

## 4. “外科手术式”修复方案

在不修改SDK源码的前提下，我们设计了一套“补丁”方案，直接在我们的 `server.ts` 中对有问题的行为进行拦截和修正。

### 修复方案A：为SSE连接注入“心跳”

为了解决Node.js客户端的 `SyntaxError`，我们需要在HTTP头发送后，立即向SSE流写入一个合法的“开场白”。一个SSE注释行是完美的解决方案。

**实现 (`server.ts` 历史代码):**
```typescript
// 在创建http服务器的部分
const server = http.createServer((req, res) => {
    // ... 其他逻辑

    // 检查是否是建立SSE连接的GET请求
    if (req.method === 'GET') {
        // 在将请求交给MCP transport处理之前，我们手动写入第一个SSE注释
        // 这可以看作是一个“心跳”，确保客户端立即收到有效数据
        res.write(': hello\n\n'); 
    }

    // 将请求和响应交给SDK的transport处理
    transport.handle(req, res);
});
```
通过在 `transport.handle` 之前手动写入 `: hello\n\n`，我们确保了SSE流的合法性，成功解决了Node.js客户端的解析问题。

### 修复方案B：绕过SDK，手动处理POST请求

对于 `406` 错误，我们采取了更激进的策略：完全绕过SDK有问题的POST请求处理逻辑，自己手动实现。

**实现 (`server.ts` 历史代码):**
```typescript
// 在创建http服务器的部分
const server = http.createServer(async (req, res) => {
    // ...

    // 拦截所有POST请求
    if (req.method === 'POST' && req.url === '/') {
        let body = '';
        req.on('data', chunk => { body += chunk.toString(); });
        req.on('end', async () => {
            const jsonRpcRequest = JSON.parse(body);

            // 我们只关心 resources/list
            if (jsonRpcRequest.method === 'resources/list') {
                // 手动调用我们自己的资源获取逻辑
                const resources = mcpServer.listResources(); // mcpServer是我们的Server实例
                
                // 手动构建一个合法的JSON-RPC响应
                const jsonRpcResponse = {
                    jsonrpc: '2.0',
                    id: jsonRpcRequest.id,
                    result: resources.map(r => ({ uri: r.uri, readable: r.readable }))
                };

                // 手动发送响应头和响应体
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(jsonRpcResponse));
                return; // 终止执行，不让SDK处理
            }
        });
    } else {
        // 对于GET请求和其他请求，还是交给SDK处理
        transport.handle(req, res);
    }
});
```
这个方案虽然代码较多，但它精准地绕过了SDK的缺陷，让我们完全控制了 `resources/list` 的响应流程，从而解决了 `406` 错误。

## 5. 结论

这次经历深刻地教育了我们：

- **不要盲目信任第三方库**: 即使是官方SDK，也可能在特定场景下存在Bug。要有能力、有勇气去质疑和深入探究。
- **底层知识是最终的救生筏**: 当高层抽象失效时，对HTTP、SSE、JSON-RPC等底层协议的理解，是定位和解决问题的关键。
- **选择合适的工具**: 最重要的教训是，我们最终通过切换到 `Stdio` 传输层，从根本上消除了所有这些问题。这证明了根据具体场景选择最简单、最直接的技术方案，远比在一个不合适的方案上投入大量精力进行修补要明智得多。
