{"name": "SiliconFlow", "api_key": "sk-jexocxnjnjwlchqwxwshlcnjbawcvrvldcjulxepaonyerju", "base_url": "https://api.siliconflow.cn/v1", "models": ["Qwen/QwQ-32B", "Qwen/QwQ-7B", "<PERSON><PERSON>/QwQ-Max"], "default_model": "Qwen/QwQ-32B", "max_tokens": 4096, "temperature": 0.7, "top_p": 0.7, "top_k": 50, "frequency_penalty": 0.5, "min_p": 0.05, "streaming": true, "enabled": true, "apis": {"chat": {"endpoint": "/v1/chat/completions", "method": "POST", "content_type": "application/json", "example": "const options = {\n  method: 'POST',\n  headers: {Authorization: 'Bearer <token>', 'Content-Type': 'application/json'},\n  body: '{\"model\":\"Qwen/QwQ-32B\",\"messages\":[{\"role\":\"user\",\"content\":\"What opportunities and challenges will the Chinese large model industry face in 2025?\"}],\"stream\":false,\"max_tokens\":512,\"enable_thinking\":false,\"thinking_budget\":4096,\"min_p\":0.05,\"stop\":null,\"temperature\":0.7,\"top_p\":0.7,\"top_k\":50,\"frequency_penalty\":0.5,\"n\":1,\"response_format\":{\"type\":\"text\"},\"tools\":[{\"type\":\"function\",\"function\":{\"description\":\"<string>\",\"name\":\"<string>\",\"parameters\":{},\"strict\":false}}]}'\n};\n\nfetch('https://api.siliconflow.cn/v1/chat/completions', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));", "response_format": {"id": "<string>", "choices": [{"message": {"role": "assistant", "content": "<string>", "reasoning_content": "<string>", "tool_calls": [{"id": "<string>", "type": "function", "function": {"name": "<string>", "arguments": "<string>"}}]}, "finish_reason": "stop"}], "usage": {"prompt_tokens": 123, "completion_tokens": 123, "total_tokens": 123}, "created": 123, "model": "<string>", "object": "chat.completion"}}, "files": {"upload": {"endpoint": "/v1/files", "method": "POST", "content_type": "multipart/form-data", "example": "const form = new FormData();\nform.append(\"purpose\", \"batch\");\n\nconst options = {\n  method: 'POST',\n  headers: {Authorization: 'Bearer <token>', 'Content-Type': 'multipart/form-data'}\n};\n\noptions.body = form;\n\nfetch('https://api.siliconflow.cn/v1/files', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));", "response_format": {"code": 20000, "message": "Ok", "status": true, "data": {"id": "file-jkvytbjtow", "object": "file", "bytes": 8509, "createdAt": 1741685396, "filename": "requests.jsonl", "purpose": "batch"}}}, "list": {"endpoint": "/v1/files", "method": "GET", "example": "const options = {method: 'GET', headers: {Authorization: 'Bearer <token>'}};\n\nfetch('https://api.siliconflow.cn/v1/files', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));", "response_format": {"code": 20000, "message": "Ok", "status": true, "data": {"data": [{"id": "file-kkhtqklcnm", "object": "file", "bytes": 806, "created_at": 1741777570, "filename": "requests-2.jsonl", "purpose": "batch", "line_count": 2}], "object": "file"}}}}, "batches": {"create": {"endpoint": "/v1/batches", "method": "POST", "content_type": "application/json", "example": "const options = {\n  method: 'POST',\n  headers: {Authorization: 'Bearer <token>', 'Content-Type': 'application/json'},\n  body: '{\"input_file_id\":\"file-jkvytbjtow\",\"endpoint\":\"/v1/chat/completions\",\"completion_window\":\"24h\",\"metadata\":{\"description\":\"nightly eval job\"},\"replace\":{\"model\":\"deepseek-ai/DeepSeek-V3\"}}'\n};\n\nfetch('https://api.siliconflow.cn/v1/batches', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));", "response_format": {"id": "batch_rdyqgrcgjg", "object": "batch", "endpoint": "/v1/chat/completions", "errors": null, "input_file_id": "file-jkvytbjtow", "completion_window": "24h", "status": "in_queue", "output_file_id": null, "error_file_id": null, "created_at": 1741685413, "in_progress_at": null, "expires_at": 1741771813, "finalizing_at": null, "completed_at": null, "failed_at": null, "expired_at": null, "cancelling_at": null, "cancelled_at": null, "request_counts": null, "metadata": {"description": "nightly eval job"}}}, "get": {"endpoint": "/v1/batches/{batch_id}", "method": "GET", "example": "const options = {method: 'GET', headers: {Authorization: 'Bearer <token>'}};\n\nfetch('https://api.siliconflow.cn/v1/batches/{batch_id}', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));", "response_format": {"id": "batch_rdyqgrcgjg", "object": "batch", "endpoint": "/v1/chat/completions", "errors": [], "input_file_id": "file-jkvytbjtow", "completion_window": "24h", "status": "in_queue", "output_file_id": null, "error_file_id": null, "created_at": 1741685413, "in_progress_at": null, "expires_at": 1741771813, "finalizing_at": null, "completed_at": null, "failed_at": null, "expired_at": null, "cancelling_at": null, "cancelled_at": null, "request_counts": {}, "metadata": {"description": "nightly eval job"}}}, "list": {"endpoint": "/v1/batches", "method": "GET", "example": "const options = {method: 'GET', headers: {Authorization: 'Bearer <token>'}};\n\nfetch('https://api.siliconflow.cn/v1/batches', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));", "response_format": {"id": "batch_rdyqgrcgjg", "object": "batch", "endpoint": "/v1/chat/completions", "errors": [], "input_file_id": "file-jkvytbjtow", "completion_window": "24h", "status": "in_queue", "output_file_id": null, "error_file_id": null, "created_at": 1741685413, "in_progress_at": null, "expires_at": 1741771813, "finalizing_at": null, "completed_at": null, "failed_at": null, "expired_at": null, "cancelling_at": null, "cancelled_at": null, "request_counts": {}, "metadata": {"description": "nightly eval job"}}}}}, "status_codes": {"200": "成功响应", "400": "错误请求", "401": "未授权", "404": "资源未找到", "429": "请求超限", "503": "服务不可用", "504": "网关超时"}}