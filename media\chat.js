// chat.js
console.log('chat.js loaded');
const vscode = acquireVsCodeApi();
const chatContainer = document.getElementById('chat-container');

// Function to create a new chat window for a model
function createChatWindow(modelKey, modelName) {
  const chatWindow = document.createElement('div');
  chatWindow.className = 'chat-window';
  chatWindow.id = `chatWindow_${modelKey}`;

  chatWindow.innerHTML = `
        <h2>${modelName}</h2>
        <div class="chat-history" id="chatHistory_${modelKey}"></div>
        <div class="chat-input">
            <input type="text" id="userInput_${modelKey}" placeholder="Type a message...">
            <button id="sendButton_${modelKey}">Send</button>
        </div>
    `;

  chatContainer.appendChild(chatWindow);

  // Add event listeners for the new elements
  document.getElementById(`sendButton_${modelKey}`).addEventListener('click', () => sendMessage(modelKey));
  document.getElementById(`userInput_${modelKey}`).addEventListener('keydown', e => {
    if (e.key === 'Enter') {
      sendMessage(modelKey);
    }
  });
}

// Function to add a message to a specific chat window
function addMessage(modelKey, text, className) {
  const chatHistory = document.getElementById(`chatHistory_${modelKey}`);
  if (!chatHistory) return;
  const messageDiv = document.createElement('div');
  messageDiv.className = 'message ' + className;
  messageDiv.textContent = text; // Use textContent for security
  chatHistory.appendChild(messageDiv);
  chatHistory.scrollTop = chatHistory.scrollHeight;
}

// Function to send a message to the extension backend
function sendMessage(modelKey) {
  const userInput = document.getElementById(`userInput_${modelKey}`);
  if (userInput && userInput.value) {
    const text = userInput.value;
    addMessage(modelKey, `You: ${text}`, 'user-message');
    vscode.postMessage({
      type: 'userInput',
      model: modelKey,
      input: text,
    });
    userInput.value = '';
  }
}

// Listen for messages from the extension
window.addEventListener('message', event => {
  const message = event.data; // The message from the extension
  console.log('[chat.js] received message:', message);
  switch (message.type) {
    case 'initialize':
      console.log('[chat.js] received initialize:', message);
      // Clear existing windows and create new ones based on the models provided
      chatContainer.innerHTML = '';
      if (message.models) {
        for (const modelKey in message.models) {
          createChatWindow(modelKey, message.models[modelKey].name);
        }
      }
      break;
    case 'update':
      // Add a bot message to the correct window
      if (message.model && message.data) {
        addMessage(message.model, `Bot: ${message.data}`, 'bot-message');
      }
      break;
    case 'addResponse':
      // Add a bot response to all chat windows (since we don't know which model it's for)
      if (message.content) {
        // Get all model keys from existing chat windows
        const chatWindows = document.querySelectorAll('.chat-window');
        chatWindows.forEach(window => {
          const modelKey = window.id.replace('chatWindow_', '');
          addMessage(modelKey, `Bot: ${message.content}`, 'bot-message');
        });
      }
      break;
    case 'showError':
      // Show error message in all chat windows
      if (message.error) {
        const chatWindows = document.querySelectorAll('.chat-window');
        chatWindows.forEach(window => {
          const modelKey = window.id.replace('chatWindow_', '');
          addMessage(modelKey, `Error: ${message.error}`, 'error-message');
        });
      }
      break;
    // Add other cases as needed, e.g., for history
  }
});

// Tell the extension that the webview is ready to be initialized
vscode.postMessage({ type: 'webviewReady' });
