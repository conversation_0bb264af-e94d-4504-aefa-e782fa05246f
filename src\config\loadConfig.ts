import * as path from 'path';
import { TextDecoder, TextEncoder } from 'util';
import * as vscode from 'vscode';
import { z } from 'zod';

// Zod schema for WorkerInfo
const worker_info_schema = z.object({
  worker_id: z.string(),
  role: z.string(),
  prompt: z.string(),
  promote_level: z.string(),
});

export type WorkerInfo = z.infer<typeof worker_info_schema>;

const worker_info_array_schema = z.array(worker_info_schema);

// Define schemas for different LLM types
const mistral_llm_schema = z.object({
  id: z.string(),
  name: z.string(),
  api_key: z.string(),
  model: z.string(),
  api_type: z.literal('mistral'),
  base_url: z.string(),
});

const spark_llm_schema = z.object({
  id: z.string(),
  name: z.string(),
  api_key: z.string(),
  api_type: z.literal('spark'),
  ws_url: z.string(),
  api_secret: z.string(),
  appid: z.string(),
  domain: z.string(),
});

// Create a discriminated union schema for LlmInfo
const llm_info_schema = z.discriminatedUnion('api_type', [mistral_llm_schema, spark_llm_schema]);

export type LlmInfo = z.infer<typeof llm_info_schema>;

const llm_info_array_schema = z.array(llm_info_schema);

const llm_config_schema = z.object({
  llms: llm_info_array_schema,
});

/**
 * Loads the worker info map from a JSON file using VS Code's file system API.
 */
export async function load_worker_info_map(context: vscode.ExtensionContext): Promise<Map<string, WorkerInfo>> {
  const worker_info_map = new Map<string, WorkerInfo>();
  try {
    const file_uri = vscode.Uri.file(path.join(context.extensionPath, 'resources', 'worker_info.json'));
    const raw_data = await vscode.workspace.fs.readFile(file_uri);
    const json_string = new TextDecoder().decode(raw_data);
    const parsed_workers = worker_info_array_schema.safeParse(JSON.parse(json_string));

    if (parsed_workers.success) {
      for (const info of parsed_workers.data) {
        worker_info_map.set(info.worker_id, info);
      }
    } else {
      void vscode.window.showErrorMessage('Failed to parse worker info file.');
    }
  } catch (error) {
    void vscode.window.showErrorMessage(
      `Failed to load worker info: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
  return worker_info_map;
}

/**
 * Loads the LLM info list from the API config file using VS Code's file system API.
 */
export async function load_llm_info(context: vscode.ExtensionContext): Promise<LlmInfo[]> {
  try {
    const file_uri = vscode.Uri.file(path.join(context.extensionPath, 'resources', 'api_config.json'));
    const raw_data = await vscode.workspace.fs.readFile(file_uri);
    const json_string = new TextDecoder().decode(raw_data);
    const parsed_config = llm_config_schema.safeParse(JSON.parse(json_string));

    if (parsed_config.success) {
      return parsed_config.data.llms;
    } else {
      void vscode.window.showErrorMessage('Failed to parse LLM configuration.');
      return [];
    }
  } catch (error) {
    void vscode.window.showErrorMessage(
      `Failed to load LLM info: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    return [];
  }
}

/**
 * Saves the worker info map to a JSON file using VS Code's file system API.
 */
export async function save_worker_info_map(
  context: vscode.ExtensionContext,
  worker_info_map: Map<string, WorkerInfo>,
): Promise<void> {
  try {
    const file_uri = vscode.Uri.file(path.join(context.extensionPath, 'resources', 'worker_info.json'));
    const worker_array = Array.from(worker_info_map.values());
    const content_as_string = JSON.stringify(worker_array, null, 2);
    const content_as_bytes = new TextEncoder().encode(content_as_string);
    await vscode.workspace.fs.writeFile(file_uri, content_as_bytes);
  } catch (error) {
    void vscode.window.showErrorMessage(
      `Failed to save worker info: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
