module.exports = {
  semi: true,
  trailingComma: 'all',
  singleQuote: true,
  printWidth: 120,
  tabWidth: 2,
  endOfLine: 'lf',       // 显式指定
  arrowParens: 'avoid',  // 箭头函数单参省略括号
  bracketSameLine: true, // JSX标签闭合同行
  bracketSpacing: true,  // 对象花括号空格
  proseWrap: 'always',
  quoteProps: 'as-needed',
  jsxSingleQuote: true,
  htmlWhitespaceSensitivity: 'ignore',
  embeddedLanguageFormatting: 'auto',
};