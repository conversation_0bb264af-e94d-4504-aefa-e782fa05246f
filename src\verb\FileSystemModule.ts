import * as vscode from 'vscode';

/**
 * Represents a single search result.
 */
export interface SearchResult {
  file_path: string;
  line: number;
  preview: string;
}

/**
 * Represents the outline of a file, containing functions, classes, etc.
 */
export interface FileOutline {
  symbols: vscode.DocumentSymbol[];
  file_path: string;
}

/**
 * Provides functionalities to interact with the file system in a structured way.
 */
export class FileSystemModule {
  /**
   * Retrieves the structural outline of a file.
   *
   * @param filePath The absolute path to the file.
   * @returns A `FileOutline` object or an error object.
   */
  public async get_file_outline(file_path: string): Promise<FileOutline | { error: string }> {
    if (!file_path) {
      return { error: 'A file path must be provided.' };
    }

    try {
      const uri = vscode.Uri.file(file_path);
      const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[] | undefined>(
        'vscode.executeDocumentSymbolProvider',
        uri,
      );

      if (!symbols) {
        // This can happen if the file is empty, has no symbols, or the language provider is not available.
        return { file_path, symbols: [] };
      }

      return { file_path, symbols };
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      return { error: `Failed to get file outline for ${file_path}. Reason: ${message}` };
    }
  }

  /**
   * Reads a specific range of lines from a file.
   *
   * @param filePath The absolute path to the file.
   * @param startLine The 1-based starting line number.
   * @param endLine The 1-based ending line number.
   * @returns The content of the specified line range or an error object.
   */
  public async read_file_range(
    file_path: string,
    start_line: number,
    end_line: number,
  ): Promise<{ content: string } | { error: string }> {
    if (start_line <= 0 || end_line < start_line) {
      return { error: 'Invalid line range provided.' };
    }

    try {
      const uri = vscode.Uri.file(file_path);
      const content_bytes = await vscode.workspace.fs.readFile(uri);
      const content = Buffer.from(content_bytes).toString('utf8');
      const lines = content.split(/\r?\n/);

      const clamped_start = Math.max(0, start_line - 1);
      const clamped_end = Math.min(lines.length, end_line);

      const selected_lines = lines.slice(clamped_start, clamped_end);
      return { content: selected_lines.join('\n') };
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      return { error: `Failed to read file range from ${file_path}. Reason: ${message}` };
    }
  }

  /**
   * Searches the entire workspace for a given keyword.
   *
   * @param keyword The keyword to search for.
   * @returns An array of `SearchResult` objects or an error object.
   */
  public async search_in_workspace(keyword: string): Promise<SearchResult[] | { error: string }> {
    if (!keyword) {
      return { error: 'A keyword to search for must be provided.' };
    }

    try {
      const symbols = await vscode.commands.executeCommand<vscode.SymbolInformation[] | undefined>(
        'vscode.executeWorkspaceSymbolProvider',
        keyword,
      );

      if (!symbols) {
        return [];
      }

      const results: SearchResult[] = await Promise.all(
        symbols.map(async symbol => {
          const uri = symbol.location.uri;
          const line = symbol.location.range.start.line;

          try {
            const content_bytes = await vscode.workspace.fs.readFile(uri);
            const content = Buffer.from(content_bytes).toString('utf8');
            const lines = content.split(/\r?\n/);
            const preview_text = lines[line] || '';

            return {
              file_path: uri.fsPath,
              line: line + 1,
              preview: preview_text.trim(),
            };
          } catch {
            return {
              file_path: uri.fsPath,
              line: line + 1,
              preview: `(Could not read file preview for ${symbol.name})`,
            };
          }
        }),
      );

      const MAX_RESULTS = 50;
      return results.slice(0, MAX_RESULTS);
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      return { error: `Failed to search workspace for symbols. Reason: ${message}` };
    }
  }
}
