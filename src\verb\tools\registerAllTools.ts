import * as vscode from 'vscode';
import { z } from 'zod';

import type { ChatPanelProvider } from '../../exit/ChatPanelProvider.js';
import type { CommandStorageModule } from '../../object/CommandStorageModule.js';
import type { EnhancedCommandManager } from '../../object/EnhancedCommandManager.js';
import type { ToolMap } from '../moduleInterfaces.js';
import { PluginCommandCapturerModule } from '../PluginCommandCapturerModule.js';

/**
 * @description Registers all the tools that the LLM can use.
 * @param command_storage - The command storage module.
 * @param chat_panel_provider - The chat panel provider.
 * @returns A map of all the registered tools.
 */
export function register_all_tools(
  command_storage: CommandStorageModule,
  chat_panel_provider: ChatPanelProvider,
  enhanced_command_manager?: EnhancedCommandManager,
): ToolMap {
  const all_tools: ToolMap = new Map();
  const plugin_command_capturer = new PluginCommandCapturerModule();

  // Tool: List all VS Code commands
  all_tools.set('list_all_vscode_commands', {
    description: 'Gets all available VS Code commands including built-in and extension commands.',
    parameters_schema: z.object({}),
    handler: async () => {
      const all_commands = await plugin_command_capturer.capture_all_commands();
      return all_commands;
    },
  });

  // Tool: Search commands
  all_tools.set('search_commands', {
    description: 'Searches for VS Code commands based on a query string.',
    parameters_schema: z.object({
      query: z.string().describe('The search term to find relevant commands (e.g., "format", "git", "save").'),
    }),
    handler: async (parameters: unknown) => {
      const { query } = parameters as { query: string };
      const matching_commands = await plugin_command_capturer.search_commands(query);
      return matching_commands;
    },
  });

  // Tool: Show message to user
  all_tools.set('show_message', {
    description: 'Shows an information message to the user in VS Code.',
    parameters_schema: z.object({
      message: z.string().describe('The message to display to the user.'),
      type: z.enum(['info', 'warning', 'error']).optional().describe('The type of message (default: info).'),
    }),
    handler: async (parameters: unknown) => {
      const { message, type = 'info' } = parameters as { message: string; type?: 'info' | 'warning' | 'error' };

      switch (type) {
        case 'warning':
          void vscode.window.showWarningMessage(message);
          break;
        case 'error':
          void vscode.window.showErrorMessage(message);
          break;
        default:
          void vscode.window.showInformationMessage(message);
      }

      return `Showed ${type} message: "${message}"`;
    },
  });

  // Tool: Capture context information
  all_tools.set('capture_context', {
    description: 'Captures current VS Code context information including active editor, workspace, and selection.',
    parameters_schema: z.object({}),
    handler: async () => {
      const context_info = {
        active_editor: vscode.window.activeTextEditor ? {
          file_path: vscode.window.activeTextEditor.document.fileName,
          language: vscode.window.activeTextEditor.document.languageId,
          line_count: vscode.window.activeTextEditor.document.lineCount,
          selection: vscode.window.activeTextEditor.selection,
          selected_text: vscode.window.activeTextEditor.document.getText(vscode.window.activeTextEditor.selection),
        } : null,
        workspace: vscode.workspace.workspaceFolders ? {
          folders: vscode.workspace.workspaceFolders.map(folder => ({
            name: folder.name,
            path: folder.uri.fsPath,
          })),
        } : null,
        visible_editors: vscode.window.visibleTextEditors.length,
        terminal_count: vscode.window.terminals.length,
      };

      return context_info;
    },
  });

  // Tool: Reply to user
  all_tools.set('reply_to_user', {
    description: 'Replies directly to the user in the chat window.',
    parameters_schema: z.object({
      message: z.string().describe('The message to display to the user.'),
    }),
    handler: (parameters: unknown) => {
      const { message } = parameters as { message: string };
      chat_panel_provider.post_message({ type: 'addResponse', content: message });
      return Promise.resolve(`Replied to user: "${message}"`);
    },
  });

  // Tool: Execute VS Code command
  all_tools.set('execute_vscode_command', {
    description: 'Executes a registered VS Code command by its ID.',
    parameters_schema: z.object({
      command_id: z.string().describe('The ID of the command to execute (e.g., "workbench.action.files.save").'),
    }),
    handler: async (parameters: unknown) => {
      const { command_id } = parameters as { command_id: string };
      await vscode.commands.executeCommand(command_id);
      return `Executed command: ${command_id}`;
    },
  });

  // Tool: Add command to whitelist
  all_tools.set('add_command_to_whitelist', {
    description: 'Adds a command to the whitelist so it can be executed.',
    parameters_schema: z.object({ command_id: z.string() }),
    handler: async (parameters: unknown) => {
      const { command_id } = parameters as { command_id: string };
      const current_whitelist = command_storage.get_whitelist_ids();
      if (!current_whitelist.includes(command_id)) {
        current_whitelist.push(command_id);
        await command_storage.save_whitelist(current_whitelist);
        return `Command '${command_id}' added to whitelist.`;
      }
      return `Command '${command_id}' is already in the whitelist.`;
    },
  });

  // Tool: Remove command from whitelist (with MCP core tool protection)
  all_tools.set('remove_command_from_whitelist', {
    description: 'Removes a command from the whitelist. MCP core tools cannot be removed.',
    parameters_schema: z.object({ command_id: z.string() }),
    handler: async (parameters: unknown) => {
      const { command_id } = parameters as { command_id: string };

      // 检查是否为MCP核心工具
      if (command_storage.is_mcp_core_tool(command_id)) {
        return `❌ Cannot remove MCP core tool '${command_id}' from whitelist. This tool is required for system functionality.`;
      }

      const success = await command_storage.safe_remove_from_whitelist(command_id);
      if (success) {
        return `✅ Command '${command_id}' removed from whitelist.`;
      } else {
        return `ℹ️ Command '${command_id}' was not in the whitelist.`;
      }
    },
  });

  // Tool: Get whitelisted commands
  all_tools.set('get_whitelisted_commands', {
    description: 'Gets the list of all commands currently in the whitelist.',
    parameters_schema: z.object({}),
    handler: () => {
      const all_commands = command_storage.get_all_commands();
      const whitelist_ids = new Set(command_storage.get_whitelist_ids());
      const whitelisted_commands = all_commands.filter(cmd => whitelist_ids.has(cmd.id));
      return Promise.resolve(whitelisted_commands);
    },
  });

  // Tool: Get all available commands (excluding whitelisted)
  all_tools.set('get_available_commands', {
    description: 'Gets all available VS Code commands that are not currently in the whitelist.',
    parameters_schema: z.object({}),
    handler: () => {
      const all_commands = command_storage.get_all_commands();
      const whitelist_ids = new Set(command_storage.get_whitelist_ids());
      const available_commands = all_commands.filter(cmd => !whitelist_ids.has(cmd.id));
      return Promise.resolve(available_commands);
    },
  });

  // Tool: Get MCP core tools information
  all_tools.set('get_mcp_core_tools', {
    description: 'Gets the list of MCP core tools that cannot be removed from the whitelist.',
    parameters_schema: z.object({}),
    handler: () => {
      const mcp_core_tools = command_storage.get_mcp_core_tools();
      return Promise.resolve({
        core_tools: mcp_core_tools,
        count: mcp_core_tools.length,
        description: 'These tools are essential for MCP functionality and cannot be removed from the whitelist.',
      });
    },
  });

  // ==================== 增强命令管理工具 ====================
  if (enhanced_command_manager) {
    // Action 1: 列出白名单
    all_tools.set('list_whitelist_commands', {
      description: 'Lists all commands currently in the whitelist that can be executed.',
      parameters_schema: z.object({}),
      handler: async () => {
        const whitelist = enhanced_command_manager.get_whitelist();
        return {
          count: whitelist.length,
          commands: whitelist,
          description: 'Commands in whitelist can be executed by the AI.',
        };
      },
    });

    // Action 2: 列出黑名单
    all_tools.set('list_blacklist_commands', {
      description: 'Lists all commands currently in the blacklist that cannot be executed.',
      parameters_schema: z.object({}),
      handler: async () => {
        const blacklist = enhanced_command_manager.get_blacklist();
        return {
          count: blacklist.length,
          commands: blacklist,
          description: 'Commands in blacklist cannot be executed and must be moved to whitelist first.',
        };
      },
    });

    // Action 3: 捕获命令并自动添加到黑名单
    all_tools.set('capture_and_classify_commands', {
      description: 'Captures all VS Code commands and automatically adds new ones to blacklist. Shows newly discovered and removed commands.',
      parameters_schema: z.object({}),
      handler: async () => {
        const result = await enhanced_command_manager.capture_and_update_commands();
        return {
          ...result,
          message: `Captured ${result.total_commands} total commands. ${result.newly_captured.length} new commands added to blacklist, ${result.removed_commands.length} invalid commands removed.`,
        };
      },
    });

    // Action 4: 刷新命令机制
    all_tools.set('refresh_command_system', {
      description: 'Refreshes the entire command system by re-capturing all commands and updating classifications.',
      parameters_schema: z.object({}),
      handler: async () => {
        await enhanced_command_manager.refresh_commands();
        const classification = enhanced_command_manager.get_command_classification();
        return {
          whitelist_count: classification.whitelist.length,
          blacklist_count: classification.blacklist.length,
          plugin_registered_count: classification.plugin_registered.length,
          mcp_core_count: classification.mcp_core.length,
          unclassified_count: classification.unclassified.length,
          message: 'Command system refreshed successfully.',
        };
      },
    });

    // 移动命令到白名单
    all_tools.set('move_commands_to_whitelist', {
      description: 'Moves commands from blacklist to whitelist so they can be executed. Commands must exist in blacklist first.',
      parameters_schema: z.object({
        command_ids: z.array(z.string()).describe('Array of command IDs to move to whitelist'),
      }),
      handler: async (parameters: unknown) => {
        const { command_ids } = parameters as { command_ids: string[] };
        const result = await enhanced_command_manager.move_to_whitelist(command_ids);
        return {
          ...result,
          message: `Successfully moved ${result.success.length} commands to whitelist. ${result.failed.length} commands not found in blacklist. ${result.already_in_whitelist.length} commands already in whitelist.`,
        };
      },
    });

    // 从白名单移除命令
    all_tools.set('remove_commands_from_whitelist', {
      description: 'Removes commands from whitelist and moves them to blacklist. MCP core tools are protected and cannot be removed.',
      parameters_schema: z.object({
        command_ids: z.array(z.string()).describe('Array of command IDs to remove from whitelist'),
      }),
      handler: async (parameters: unknown) => {
        const { command_ids } = parameters as { command_ids: string[] };
        const result = await enhanced_command_manager.remove_from_whitelist(command_ids);
        return {
          ...result,
          message: `Successfully removed ${result.success.length} commands from whitelist. ${result.protected_commands.length} MCP core tools are protected. ${result.not_found.length} commands not found in whitelist.`,
        };
      },
    });

    // 获取命令分类信息
    all_tools.set('get_command_classification', {
      description: 'Gets detailed classification of all commands including whitelist, blacklist, plugin registered, MCP core, and unclassified commands.',
      parameters_schema: z.object({}),
      handler: async () => {
        const classification = enhanced_command_manager.get_command_classification();
        return {
          whitelist: {
            count: classification.whitelist.length,
            commands: classification.whitelist,
          },
          blacklist: {
            count: classification.blacklist.length,
            commands: classification.blacklist,
          },
          plugin_registered: {
            count: classification.plugin_registered.length,
            commands: classification.plugin_registered,
          },
          mcp_core: {
            count: classification.mcp_core.length,
            commands: classification.mcp_core,
          },
          unclassified: {
            count: classification.unclassified.length,
            commands: classification.unclassified,
          },
          summary: `Total: ${classification.whitelist.length + classification.blacklist.length + classification.plugin_registered.length + classification.unclassified.length} commands classified`,
        };
      },
    });

    // 检查命令执行权限
    all_tools.set('check_command_execution_permission', {
      description: 'Checks if specific commands can be executed (are in whitelist).',
      parameters_schema: z.object({
        command_ids: z.array(z.string()).describe('Array of command IDs to check'),
      }),
      handler: async (parameters: unknown) => {
        const { command_ids } = parameters as { command_ids: string[] };
        const results = command_ids.map(id => ({
          command_id: id,
          can_execute: enhanced_command_manager.can_execute_command(id),
          is_mcp_core: enhanced_command_manager.is_mcp_core_tool(id),
          is_plugin_registered: enhanced_command_manager.is_plugin_registered_command(id),
        }));

        const executable_count = results.filter(r => r.can_execute).length;

        return {
          results,
          summary: `${executable_count}/${command_ids.length} commands can be executed`,
        };
      },
    });
  }

  return all_tools;
}
