// ... 其他导入保持不变 ...

export function register_all_tools(
  command_storage: CommandStorageModule,
  chat_panel_provider: ChatPanelProvider,
  enhanced_command_manager?: EnhancedCommandManager,
): ToolMap {
  const all_tools: ToolMap = new Map();
  const plugin_command_capturer = new PluginCommandCapturerModule();

  // 保留的工具：显示消息
  all_tools.set('show_message', {
    description: 'Shows an information message to the user in VS Code.',
    parameters_schema: z.object({
      message: z.string().describe('The message to display to the user.'),
      type: z.enum(['info', 'warning', 'error']).optional().describe('The type of message (default: info).'),
    }),
    handler: async (parameters: unknown) => {
      const { message, type = 'info' } = parameters as { message: string; type?: 'info' | 'warning' | 'error' };

      switch (type) {
        case 'warning':
          void vscode.window.showWarningMessage(message);
          break;
        case 'error':
          void vscode.window.showErrorMessage(message);
          break;
        default:
          void vscode.window.showInformationMessage(message);
      }

      return `Showed ${type} message: "${message}"`;
    },
  });

  // 保留的工具：捕获上下文信息
  all_tools.set('capture_context', {
    description: 'Captures current VS Code context information including active editor, workspace, and selection.',
    parameters_schema: z.object({}),
    handler: async () => {
      const context_info = {
        active_editor: vscode.window.activeTextEditor ? {
          file_path: vscode.window.activeTextEditor.document.fileName,
          language: vscode.window.activeTextEditor.document.languageId,
          line_count: vscode.window.activeTextEditor.document.lineCount,
          selection: vscode.window.activeTextEditor.selection,
          selected_text: vscode.window.activeTextEditor.document.getText(vscode.window.activeTextEditor.selection),
        } : null,
        workspace: vscode.workspace.workspaceFolders ? {
          folders: vscode.workspace.workspaceFolders.map(folder => ({
            name: folder.name,
            path: folder.uri.fsPath,
          })),
        } : null,
        visible_editors: vscode.window.visibleTextEditors.length,
        terminal_count: vscode.window.terminals.length,
      };

      return context_info;
    },
  });

  // 保留的工具：回复用户
  all_tools.set('reply_to_user', {
    description: 'Replies directly to the user in the chat window.',
    parameters_schema: z.object({
      message: z.string().describe('The message to display to the user.'),
    }),
    handler: (parameters: unknown) => {
      const { message } = parameters as { message: string };
      chat_panel_provider.post_message({ type: 'addResponse', content: message });
      return Promise.resolve(`Replied to user: "${message}"`);
    },
  });

  // 统一命令管理工具
  if (enhanced_command_manager) {
    all_tools.set('commands_manager', {
      description: 'A unified command management system for managing and executing VS Code commands. ' +
        'This tool provides a comprehensive set of actions to list, search, and execute commands, ' +
        'as well as manage the whitelist and blacklist of commands that can be executed by the AI.',
      parameters_schema: z.object({
        action: z.enum([
          'list_commands',
          'search_commands',
          'execute_command',
          'add_to_whitelist',
          'remove_from_whitelist',
          'get_command_info',
          'capture_refresh'
        ]).describe(`
          The action to perform. Valid actions are:
          - list_commands: List all commands (optionally filtered by type)
          - search_commands: Search for commands by query
          - execute_command: Execute a command by ID
          - add_to_whitelist: Add commands to the whitelist
          - remove_from_whitelist: Remove commands from the whitelist
          - get_command_info: Get detailed information about specific commands
          - capture_refresh: Refresh the list of available commands
        `),
        type: z.enum(['all', 'whitelist', 'blacklist', 'mcp_core', 'plugin_registered', 'unclassified'])
          .optional()
          .describe('Filter commands by type (used with list_commands action)'),
        query: z.string().optional()
          .describe('Search query (used with search_commands action)'),
        command_ids: z.array(z.string()).optional()
          .describe('Array of command IDs (used with add_to_whitelist, remove_from_whitelist, get_command_info actions)'),
        command_id: z.string().optional()
          .describe('Single command ID (alternative to command_ids for single command operations)'),
        args: z.array(z.any()).optional()
          .describe('Arguments to pass to the command (used with execute_command action)'),
        include_details: z.boolean().optional().default(false)
          .describe('Whether to include detailed information about each command (may be slower)'),
      }),
      handler: async (parameters: unknown) => {
        const {
          action,
          type = 'all',
          query = '',
          command_ids = [],
          command_id,
          args = [],
          include_details = false
        } = parameters as {
          action: string;
          type?: 'all' | 'whitelist' | 'blacklist' | 'mcp_core' | 'plugin_registered' | 'unclassified';
          query?: string;
          command_ids?: string[];
          command_id?: string;
          args?: any[];
          include_details?: boolean;
        };

        // Helper function to get command info
        const get_command_info = (id: string) => {
          const is_whitelisted = enhanced_command_manager.can_execute_command(id);
          const is_mcp_core = enhanced_command_manager.is_mcp_core_tool(id);
          const is_plugin_registered = enhanced_command_manager.is_plugin_registered_command(id);

          let command_type = 'unclassified';
          if (is_mcp_core) command_type = 'mcp_core';
          else if (is_plugin_registered) command_type = 'plugin_registered';
          else if (is_whitelisted) command_type = 'whitelist';
          else command_type = 'blacklist';

          return {
            command_id: id,
            is_whitelisted,
            is_mcp_core,
            is_plugin_registered,
            type: command_type,
          };
        };

        switch (action) {
          case 'list_commands': {
            // Get all commands
            const all_commands = await plugin_command_capturer.capture_all_commands();

            // Filter by type if specified
            let filtered_commands = all_commands;
            if (type !== 'all') {
              filtered_commands = all_commands.filter(cmd => {
                const info = get_command_info(cmd.id);
                return info.type === type;
              });
            }

            // Add detailed info if requested
            let commands_with_details = filtered_commands;
            if (include_details) {
              commands_with_details = filtered_commands.map(cmd => ({
                ...cmd,
                ...get_command_info(cmd.id)
              }));
            }

            return {
              action: 'list_commands',
              type,
              count: commands_with_details.length,
              commands: commands_with_details,
              message: `Found ${commands_with_details.length} commands${type !== 'all' ? ` in ${type}` : ''}`,
            };
          }

          case 'search_commands': {
            // Search for commands
            if (!query) {
              throw new Error('Query parameter is required for search_commands action');
            }

            const matching_commands = await plugin_command_capturer.search_commands(query);

            // Add detailed info if requested
            let commands_with_details = matching_commands;
            if (include_details) {
              commands_with_details = matching_commands.map(cmd => ({
                ...cmd,
                ...get_command_info(cmd.id)
              }));
            }

            return {
              action: 'search_commands',
              query,
              count: commands_with_details.length,
              commands: commands_with_details,
              message: `Found ${commands_with_details.length} commands matching "${query}"`,
            };
          }

          case 'execute_command': {
            // Execute a command
            const target_command_id = command_id || (command_ids && command_ids[0]);
            if (!target_command_id) {
              throw new Error('command_id or command_ids is required for execute_command action');
            }

            if (!enhanced_command_manager.can_execute_command(target_command_id)) {
              return {
                action: 'execute_command',
                command_id: target_command_id,
                success: false,
                message: `Command "${target_command_id}" is not in the whitelist. Use add_to_whitelist action first.`,
              };
            }

            try {
              const result = await vscode.commands.executeCommand(target_command_id, ...args);
              return {
                action: 'execute_command',
                command_id: target_command_id,
                success: true,
                result,
                message: `Command "${target_command_id}" executed successfully`,
              };
            } catch (error) {
              return {
                action: 'execute_command',
                command_id: target_command_id,
                success: false,
                error: error instanceof Error ? error.message : String(error),
                message: `Failed to execute command "${target_command_id}"`,
              };
            }
          }

          case 'add_to_whitelist': {
            // Add commands to whitelist
            const ids_to_add = command_id ? [command_id] : command_ids;
            if (!ids_to_add || ids_to_add.length === 0) {
              throw new Error('command_id or command_ids is required for add_to_whitelist action');
            }

            const results = await Promise.all(ids_to_add.map(async (id) => {
              try {
                const current_whitelist = command_storage.get_whitelist_ids();
                if (!current_whitelist.includes(id)) {
                  current_whitelist.push(id);
                  await command_storage.save_whitelist(current_whitelist);
                  return { id, success: true, message: `Added to whitelist` };
                } else {
                  return { id, success: true, message: `Already in whitelist` };
                }
              } catch (error) {
                return {
                  id,
                  success: false,
                  message: `Failed to add to whitelist: ${error instanceof Error ? error.message : String(error)}`
                };
              }
            }));

            return {
              action: 'add_to_whitelist',
              results,
              success_count: results.filter(r => r.success).length,
              total_count: results.length,
              message: `Added ${results.filter(r => r.success).length} of ${results.length} commands to whitelist`,
            };
          }

          case 'remove_from_whitelist': {
            // Remove commands from whitelist
            const ids_to_remove = command_id ? [command_id] : command_ids;
            if (!ids_to_remove || ids_to_remove.length === 0) {
              throw new Error('command_id or command_ids is required for remove_from_whitelist action');
            }

            const results = await Promise.all(ids_to_remove.map(async (id) => {
              try {
                // Check if it's an MCP core tool
                if (command_storage.is_mcp_core_tool(id)) {
                  return {
                    id,
                    success: false,
                    message: 'Cannot remove MCP core tool from whitelist'
                  };
                }

                const success = await command_storage.safe_remove_from_whitelist(id);
                return {
                  id,
                  success,
                  message: success ? 'Removed from whitelist' : 'Not in whitelist'
                };
              } catch (error) {
                return {
                  id,
                  success: false,
                  message: `Failed to remove from whitelist: ${error instanceof Error ? error.message : String(error)}`
                };
              }
            }));

            return {
              action: 'remove_from_whitelist',
              results,
              success_count: results.filter(r => r.success).length,
              total_count: results.length,
              message: `Removed ${results.filter(r => r.success).length} of ${results.length} commands from whitelist`,
            };
          }

          case 'get_command_info': {
            // Get detailed information about specific commands
            const ids_to_check = command_id ? [command_id] : command_ids;
            if (!ids_to_check || ids_to_check.length === 0) {
              throw new Error('command_id or command_ids is required for get_command_info action');
            }

            const results = ids_to_check.map(id => get_command_info(id));

            return {
              action: 'get_command_info',
              results,
              count: results.length,
              message: `Retrieved information for ${results.length} commands`,
            };
          }

          case 'capture_refresh': {
            // Refresh the list of available commands
            const capture_result = await enhanced_command_manager.capture_and_update_commands();
            const classification = enhanced_command_manager.get_command_classification();

            return {
              action: 'capture_refresh',
              newly_captured: capture_result.newly_captured,
              removed_commands: capture_result.removed_commands,
              total_commands: capture_result.total_commands,
              classification: {
                whitelist_count: classification.whitelist.length,
                blacklist_count: classification.blacklist.length,
                plugin_registered_count: classification.plugin_registered.length,
                mcp_core_count: classification.mcp_core.length,
                unclassified_count: classification.unclassified.length,
              },
              message: `Refreshed command list. Found ${capture_result.total_commands} commands.`,
            };
          }

          default:
            throw new Error(`Unknown action: ${action}. Valid actions: list_commands, search_commands, execute_command, add_to_whitelist, remove_from_whitelist, get_command_info, capture_refresh`);
        }
      },
    });
  }

  return all_tools;
}