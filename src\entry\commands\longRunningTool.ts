import * as vscode from 'vscode';

import type { Tool } from '../../verb/moduleInterfaces.js';

/**
 * A utility function to safely check if a value is a record (plain object).
 * @param value The value to check.
 * @returns True if the value is a record, false otherwise.
 */
function is_record(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Registers the 'long_running_tool' command.
 * This command executes a tool handler that may perform a long-running task.
 * @param context The VS Code extension context.
 * @param all_tools A record of all available tools.
 */
export function register_long_running_tool_command(
  context: vscode.ExtensionContext,
  all_tools: Record<string, Tool>,
): void {
  context.subscriptions.push(
    vscode.commands.registerCommand('long_running_tool', async (params: unknown): Promise<unknown> => {
      const tool = all_tools['long_running_tool'];
      if (!tool) {
        void vscode.window.showErrorMessage('long_running_tool is not registered.');
        return;
      }

      try {
        // The `handler` from the Tool interface returns Promise<any>, which is unsafe.
        // We handle this by immediately casting the result to `unknown` to maintain type safety.
        const handler_params = is_record(params) ? params : {};
        const result: unknown = await tool.handler(handler_params);
        return result;
      } catch (e: unknown) {
        const error_message = e instanceof Error ? e.message : String(e);
        void vscode.window.showErrorMessage(`Error executing long_running_tool: ${error_message}`);
        // Re-throwing the error allows the caller of the command to handle it.
        throw e;
      }
    }),
  );
}
