import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import fs from 'fs';
import os from 'os';
import path from 'path';

// Configuration
const PORT_FILE = path.join(os.tmpdir(), '.mcp_port');

function getServerUrl() {
  if (!fs.existsSync(PORT_FILE)) {
    console.error(`MCP port file not found at ${PORT_FILE}. Is the server running?`);
    process.exit(1);
  }
  const port = fs.readFileSync(PORT_FILE, 'utf8');
  if (!port) {
    console.error(`Port file is empty. Is the server running correctly?`);
    process.exit(1);
  }
  return `http://localhost:${port}`;
}

const SERVER_URL = getServerUrl();
const CLIENT_NAME = `test-client-${Date.now()}`;

async function testMcpConnection() {
  console.log('Starting MCP connection test...');

  // Create a new client
  const client = new Client(
    {
      name: CLIENT_NAME,
      version: '1.0.0',
    },
    {
      capabilities: {},
    },
  );

  try {
    console.log(`Connecting to MCP server at ${SERVER_URL}...`);
    const transport = new SSEClientTransport({
      url: `${SERVER_URL}/mcp`,
    });

    await client.connect(transport);
    console.log('Successfully connected to MCP server!');

    // 1. List available resources
    console.log('\n--- Listing available resources ---');
    const resources = await client.request({ method: 'resources/list' }, 'ListResourcesResultSchema');
    console.log('Available resources:', JSON.stringify(resources, null, 2));

    // 2. Test showMessage tool if available
    if (resources.resources && resources.resources.some(r => r.uri.startsWith('tool:///showMessage'))) {
      console.log('\n--- Testing showMessage tool ---');
      const message = 'Hello from the test script!';
      const result = await client.request(
        {
          method: 'resources/read',
          params: {
            uri: `tool:///showMessage?message=${encodeURIComponent(message)}`,
          },
        },
        'ReadResourceResultSchema',
      );
      console.log('showMessage result:', result);
    } else {
      console.log('\nshowMessage tool not found in available resources');
    }
  } catch (error) {
    console.error('Error during MCP connection test:', error);
  } finally {
    client.close();
    console.log('\nTest completed. Connection closed.');
  }
}

testMcpConnection().catch(console.error);
