火山方舟
APIkey:964e2c05-4c96-40c9-87a3-75d98408c6ec
All interfaces under 云服务器 are provided below. It is recommended that you debug and call the API through API Explorer.
云助手
API name
Descriptio
Operations
Create
 CreateCommand
调用CreateCommand接口，新建一条自定义命令。

Documentation
Debug
Modify
 ModifyCommand
调用ModifyCommand接口，修改指定自定义命令内容。

Documentation
Debug
Delete
 DeleteCommand
调用DeleteCommand接口，删除一条自定义命令。

Documentation
Debug
Query
 DescribeCommands
调用DescribeCommands接口，查询命令列表。

Documentation
Debug
Modify
 InvokeCommand
调用InvokeCommand接口，使用已有自定义命令或公共命令创建任务。

Documentation
Debug
Delete
 DeleteInvocation
DeleteInvocation

Documentation
Debug
Query
 DescribeInvocations
调用DescribeInvocations接口，查询任务的执行列表和状态。

Documentation
Debug
Query
 DescribeInvocationInstances
调用DescribeInvocationInstances接口，查询任务执行对象列表。

Documentation
Debug
Query
 DescribeInvocationResults
调用DescribeInvocationResults接口，查看一条或多条任务的执行结果。

Documentation
Debug
Query
 DescribeCloudAssistantStatus
调用DescribeCloudAssistantStatus接口，查询实例云助手客户端的安装状态。

Documentation
Debug
Create
 RunCommand
调用RunCommand接口，使用手工输入命令的方式创建任务。

Documentation
Debug
Modify
 StopInvocation
调用StopInvocation接口，停止一条正在运行中（Running）的作业。

Documentation
Debug
Create
 InstallCloudAssistant
调用InstallCloudAssistant接口，为指定实例安装云助手客户端。

Documentation
Debug
Modify
 UninstallCloudAssistants
调用UninstallCloudAssistants接口，为云服务器实例卸载已安装的云助手客户端。

Documentation
Debug
Modify
 UpgradeCloudAssistants
调用UpgradeCloudAssistants接口，为云服务器实例升级已安装的云助手客户端至最新版本。

Documentation
Debug
高性能计算集群
API name
Description
Operations
Query
 DescribeHpcClusters
调用 DescribeHpcClusters 接口查询符合条件的高性能计算集群详情列表。

Documentation
Debug
Create
 CreateHpcCluster
调用 CreateHpcCluster 接口创建一个高性能计算集群。高性能计算集群用于实现高性能计算型实例的逻辑隔离，了解更多。

Documentation
Debug
Delete
 DeleteHpcCluster
调用 DeleteHpcCluster 接口删除一个高性能计算集群。已部署云服务器的高性能计算集群不允许删除。

Documentation
Debug
实例
API name
Description
Operations
Create
 StartInstance
调用 StartInstance 接口启动一台实例。

Documentation
Debug
Create
 StopInstance
调用 StopInstance 接口停止一台实例。

Documentation
Debug
Create
 RebootInstance
调用 RebootInstance 接口重启一台实例。

Documentation
Debug
Delete
 DeleteInstance
调用 DeleteInstance 接口删除一台实例。

Documentation
Debug
Query
 DescribeInstances
调用 DescribeInstances 接口获取实例信息。

Documentation
Debug
Create
 ModifyInstanceSpec
调用 ModifyInstanceSpec 修改指定实例的规格。

Documentation
Debug
Create
 ModifyInstanceChargeType
调用 ModifyInstanceChargeType 接口转换实例的计费方式，支持在按量计费和包年包月之间相互转换。

Documentation
Debug
Query
 DescribeAvailableResource
调用 DescribeAvailableResource 接口查询可用区中计算资源的库存信息，包括所属地域、可用区、计算资源库存状态等。

Documentation
Debug
Create
 RenewInstance
调用 RenewInstance 续费一台包年包月实例，优先使用抵扣代金券方式续费，且包年包月的云盘和镜像会一起续费。

Documentation
Debug
Query
 DescribeUserData
调用 DescribeUserData 接口查询一台云服务器实例的自定义数据。

Documentation
Debug
Query
 DescribeInstanceECSTerminalUrl
调用 DescribeInstanceECSTerminalUrl 查询一台云服务器的 ECS Terminal 管理终端地址。

Documentation
Debug
Query
 DescribeInstanceVncUrl
调用 DescribeInstanceVncUrl 查询一台云服务器的VNC管理终端地址。

Documentation
Debug
Query
 DescribeInstancesIamRoles
调用 DescribeInstancesIamRoles 接口查询一台或多台ECS实例上已绑定的IAM角色。

Documentation
Debug
Create
 StartInstances
调用 StartInstances 接口启动一台或多台实例。

Documentation
Debug
Create
 StopInstances
调用 StopInstances 接口停止一台或多台实例。

Documentation
Debug
Create
 RebootInstances
调用 RebootInstances 接口重启一台或多台实例。

Documentation
Debug
Delete
 DeleteInstances
调用 DeleteInstances 接口删除一台或多台实例。

Documentation
Debug
Create
 AssociateInstancesIamRole
调用 AssociateInstancesIamRole 接口为一台或多台实例绑定IAM角色。

Documentation
Debug
Create
 DisassociateInstancesIamRole
调用 DisassociateInstancesIamRole 接口为一台或多台实例解绑IAM角色，用户或服务只能解绑各自绑定的角色。

Documentation
Debug
Query
 DescribeInstanceTypeFamilies
调用 DescribeInstanceTypeFamilies 查询云服务器提供的实例规格族信息。

Documentation
Debug
Modify
 ModifyInstanceVpcAttribute
调用 ModifyInstanceVpcAttribute 接口修改一台实例的VPC、子网或安全组。

Documentation
Debug
Modify
 ModifyInstancesSpec
调用 ModifyInstancesSpec 批量修改指定实例的规格。

Documentation
Debug
Query
 DescribeSpotAdvice
调用 DescribeSpotAdvice 接口查询抢占式实例近30天的实例平均释放率、平均折扣率等信息。

Documentation
Debug
Create
 PurchaseReservedInstances
调用 PurchaseReservedInstances 接口购买抵扣型资源（预留实例券或预留存储容量包）。

Documentation
Debug
Query
 DescribeReservedInstances
调用 DescribeReservedInstances 接口获取预留实例券信息。

Documentation
Debug
Create
 ModifyInstanceAttribute
调用 ModifyInstanceAttribute 接口修改一台ECS实例的信息，包括名称、描述、密码、主机名以及自定义数据等。

Documentation
Debug
Query
 DescribeInstanceTypes
调用 DescribeInstanceTypes 接口查询云服务器提供的所有实例规格的信息，也可以查询指定实例规格的信息。

Documentation
Debug
Create
 RunInstances
调用 RunInstances 接口创建一台或多台云服务器实例。

Documentation
Debug
Modify
 ModifyReservedInstances
调用 ModifyReservedInstances 接口更改预留实例券，包括更改预留实例券属性、拆分/合并预留实例券等。

Documentation
Debug
Query
 DescribeSpotPriceHistory
调用 DescribeSpotPriceHistory 接口查询抢占式实例近30天内的历史价格。

Documentation
Debug
Query
 DescribeScheduledInstanceStock
调用 DescribeScheduledInstanceStock 接口查询弹性预约实例可预约时间段。

Documentation
Debug
Create
 CreateScheduledInstances
调用 CreateScheduledInstances 接口创建弹性预约实例。

Documentation
Debug
Query
 DescribeScheduledInstances
调用 DescribeScheduledInstances 接口查询弹性预约单信息。

Documentation
Debug
Delete
 DeleteScheduledInstance
调用 DeleteScheduledInstance 接口取消预约。

Documentation
Debug
Query
 GetScheduledInstanceLatestReleaseAt
调用 GetScheduledInstanceLatestReleaseAt 接口查询弹性预约实例-时段型的最晚释放时间。

Documentation
Debug
地域
API name
Description
Operations
Query
 DescribeZones
调用 DescribeZones 查询一个地域下的可用区信息。

Documentation
Debug
Query
 DescribeRegions
调用 DescribeRegions 接口查询地域信息。

Documentation
Debug
镜像
API name
Description
Operations
Query
 DescribeImages
调用 DescribeImages 接口，查询镜像信息。

Documentation
Debug
Create
 ModifyImageSharePermission
调用 ModifyImageSharePermission 接口，管理镜像共享权限。

Documentation
Debug
Query
 DescribeImageSharePermission
调用 DescribeImageSharePermission 接口，查询自定义镜像已经共享的所有用户。

Documentation
Debug
Create
 ExportImage
调用 ExportImage 接口，导出自定义镜像。

Documentation
Debug
Query
 DescribeTasks
调用 DescribeTasks 接口，查询一个或多个异步请求的进度。

Documentation
Debug
Create
 ModifyImageAttribute
调用 ModifyImageAttribute 接口，修改自定义镜像的名称、描述、启动模式等信息。

Documentation
Debug
Create
 CopyImage
调用 CopyImage 接口，跨地域复制镜像。

Documentation
Debug
Create
 ReplaceSystemVolume
调用 ReplaceSystemVolume 接口，为一台实例更换系统。

Documentation
Debug
Create
 ImportImage
调用 ImportImage 接口，将本地镜像文件导入火山引擎，该镜像将作为自定义镜像展示在相应地域中。

Documentation
Debug
Create
 CreateImage
调用 CreateImage 接口，通过实例创建自定义镜像。

Documentation
Debug
Delete
 DeleteImages
调用 DeleteImages 接口，删除自定义镜像。

Documentation
Debug
Create
 DetectImage
调用 DetectImage 接口，发起镜像检测流程。

Documentation
Debug
密钥对
API name
Description
Operations
Create
 ImportKeyPair
调用 ImportKeyPair 导入由其他工具产生的密钥对的公钥。您在同一地域内最多可创建500个密钥对。

Documentation
Debug
Delete
 DeleteKeyPairs
调用 DeleteKeyPairs 删除一个或多个密钥对。

Documentation
Debug
Create
 AttachKeyPair
调用 AttachKeyPair 绑定一个密钥对到一台或多台Linux实例。

Documentation
Debug
Create
 ModifyKeyPairAttribute
调用 ModifyKeyPairAttribute 修改密钥对描述信息。

Documentation
Debug
Create
 DetachKeyPair
调用 DetachKeyPair 为一台或多台Linux实例解绑密钥对。

Documentation
Debug
Create
 CreateKeyPair
调用 CreateKeyPair 接口创建一个密钥对。

Documentation
Debug
Query
 DescribeKeyPairs
调用 DescribeKeyPairs 接口查询密钥对列表信息。

Documentation
Debug
标签
API name
Description
Operations
Modify
 CreateTags
调用 CreateTags 接口为一个或多个资源添加用户标签。

Documentation
Debug
Modify
 DeleteTags
调用 DeleteTags 接口为一个或多个资源删除用户标签。

Documentation
Debug
Query
 DescribeTags
调用 DescribeTags 接口查询已绑定标签的资源。

Documentation
Debug
Modify
 UntagResources
调用 UntagResources 接口为一个或多个资源删除用户标签。

Documentation
Debug
Modify
 TagResources
调用 TagResources 接口为一个或多个资源添加用户标签。

Documentation
Debug
Query
 ListTagsForResources
调用 ListTagsForResources 接口查询已绑定标签的资源。

Documentation
Debug
监控
API name
Description
Operations
Query
 DescribeSystemEvents
调用 DescribeSystemEvents 接口，查询实例的系统事件信息。

Documentation
Debug
Create
 UpdateSystemEvents
调用 UpdateSystemEvents 接口，修改系统事件状态。

Documentation
Debug
Create
 CreateSubscription
调用 CreateSubscription 接口，订阅系统事件通知。

Documentation
Debug
Create
 ModifySubscriptionEventTypes
调用 ModifySubscriptionEventTypes 接口，修改订阅事件类型。

Documentation
Debug
Query
 DescribeSubscriptions
调用 DescribeSubscriptions 接口，查询事件通知订阅列表。

Documentation
Debug
Query
 DescribeEventTypes
调用 DescribeEventTypes 接口，查询事件类型列表。

Documentation
Debug
运维
API name
Description
Operations
Query
 GetConsoleOutput
调用 GetConsoleOutput 获取实例最近启动、重启或者关机时的系统命令行输出。

Documentation
Debug
Query
 GetConsoleScreenshot
调用 GetConsoleScreenshot 接口，获取正在运行实例的JPG格式屏幕截图。

Documentation
Debug
部署集
API name
Description
Operations
Create
 CreateDeploymentSet
调用 CreateDeploymentSet 接口创建一个部署集。

Documentation
Debug
Query
 DescribeDeploymentSets
调用 DescribeDeploymentSets 接口查询部署集的属性。

Documentation
Debug
Create
 ModifyDeploymentSetAttribute
调用 ModifyDeploymentSetAttribute 接口修改一个部署集的名称和描述信息。

Documentation
Debug
Create
 ModifyInstanceDeployment
调用 ModifyInstanceDeployment 接口调整实例所属部署集，您可以向指定部署集内添加一台实例，或者将一台实例从部署集中移除。

Documentation
Debug
Delete
 DeleteDeploymentSet
调用 DeleteDeploymentSet 接口删除一个部署集。

Documentation
Debug
Query
 DescribeDeploymentSetSupportedInstanceTypeFamily
调用 DescribeDeploymentSetSupportedInstanceTypeFamily 接口查询支持部署集的实例规格族。

Documentation
Debug
专有宿主机
API name
Description
Operations
Query
 DescribeDedicatedHosts
调用 DescribeDedicatedHosts 接口获取专有宿主机的详细信息，包括宿主机状态、到期时间和已创建的实例列表等。

Documentation
Debug
Create
 AllocateDedicatedHosts
调用 AllocateDedicatedHosts 接口申请一台或多台包年包月的专有宿主机。

Documentation
Debug
Modify
 ModifyDedicatedHostAttribute
调用 ModifyDedicatedHostAttribute 接口修改专有宿主机属性，包括名称、描述、CPU超分比、是否自动部署以及自动迁移等。

Documentation
Debug
Query
 DescribeDedicatedHostTypes
调用 DescribeDedicatedHostTypes 接口查询专有宿主机规格信息。

Documentation
Debug
Modify
 ModifyInstancePlacement
调用 ModifyInstancePlacement 接口为专有宿主机上的实例调整部署设置。

Documentation
Debug
Modify
 RenewDedicatedHost
调用 RenewDedicatedHost 接口对专有宿主机进行续费，优先使用抵扣代金券方式续费。

Documentation
Debug
Create
 RedeployDedicatedHost
调用 RedeployDedicatedHost 接口对专有宿主机进行整体迁移。

Documentation
Debug
Query
 DescribeDedicatedHostClusters
调用 DescribeDedicatedHostClusters 接口查询专有宿主机集群的详细信息。

Documentation
Debug
Modify
 ModifyDedicatedHostClusterAttribute
调用 ModifyDedicatedHostClusterAttribute 接口修改专有宿主机集群的信息，包括名称、描述等。

Documentation
Debug
Delete
 DeleteDedicatedHostCluster
调用 DeleteDedicatedHostCluster 接口删除专有宿主机集群。

Documentation
Debug
Create
 CreateDedicatedHostCluster
调用 CreateDedicatedHostCluster 接口创建一个专有宿主机集群。

Documentation
Debug