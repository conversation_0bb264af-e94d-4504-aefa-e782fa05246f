# MCP 开发核心思想：从“协议即代码”到“契约式编程”

**日期:** 2025-06-25

---

## 核心哲学：MCP SDK 不是一个“库”，而是一个“编译器”

我们经历的所有挫折，都源于一个根本性的误解：我们最初将 `@modelcontextprotocol/sdk` 当作一个功能齐全的“库” (Library)，期望它能提供像 `fs.readFile` 一样开箱即用的函数。这是一个致命的错误。

**正确的认知是：MCP SDK 是一个“协议编译器” (Protocol Compiler) 和“运行时” (Runtime)。**

它的核心价值在于，它提供了一套工具，让我们能够**定义一种语言（即我们的协议）**，并保证所有参与方（客户端、服务端）都严格遵守这门语言的语法和语义。它负责“编译”我们的协议定义（通过 Zod Schema），并在运行时强制执行它。

**开发者不是“库的调用者”，而是“语言的设计者”。** 我们的首要任务，是设计这门用于我们自己应用通信的、精确无歧义的语言。

---

## MCP 开发的“黄金之路”：四步法开发范式

为确保每一个功能都健壮、安全、可维护，请**严格按照以下顺序**执行这四个步骤。**绝不跳步，绝不颠倒顺序。** 这是一条被实践证明的、通往高质量代码的“黄金之路”。

### 黄金之路清单 (Golden Path Checklist)

在开始编码前，请先回答以下问题：

1.  **[ ] 契约 (Contract):** 我是否已经用 Zod 清晰地定义了请求的 `Schema` 和成功的响应 `Schema`？
2.  **[ ] 安全 (Security):** 我的实现代码是否对所有外部输入（如文件路径、命令参数）进行了严格的安全检查？
3.  **[ ] 错误 (Errors):** 我的实现代码是否用 `try/catch` 覆盖了所有可能失败的逻辑，并为每一种失败场景准备了明确的 `JsonRpcError`？
4.  **[ ] 测试 (Testing):** 我的核心逻辑是否封装在独立的函数中，以便进行单元测试？

**只有当以上所有问题的答案都为“是”时，才认为该功能的后端部分开发完成。**

---

### 第一步：定义“契约” (The Contract) - *用 Zod 思考*

这是所有工作的起点，也是最重要的一步。在写任何一行功能代码之前，先用 `Zod` 将通信的“契约”完整、严格地定义出来。

-   **产出物:** 一个 `...RequestSchema.ts` 和一个 `...ResponseSchema.ts` 文件（或在同一文件中定义）。
-   **核心要点:** 只定义数据结构和类型，不包含任何逻辑。

**示例：**
```typescript
// fileProtocol.ts
export const ReadFileRequestSchema = z.object({
  method: z.literal('file/read'),
  params: z.object({ path: z.string() }),
});

export const ReadFileResponseSchema = z.object({
  content: z.string(),
});
```

### 第二步：实现“契约” (The Implementation) - *编写防御性业务逻辑*

现在，针对上一步定义的每一份契约，编写一个处理函数。**一个生产级的处理函数必须同时考虑“理想路径”与“非理想路径”**。

-   **产出物:** 一个包含核心处理逻辑的 `...Handler.ts` 函数。
-   **核心要点:** 安全第一，错误处理优先，逻辑内聚。

**升级版示例：**
```typescript
// fileHandler.ts
import { promises as fs } from 'fs';
import path from 'path';
import { JsonRpcError } from '@modelcontextprotocol/sdk';

const SAFE_ROOT = '/path/to/safe/workspace'; // 安全的根目录

export async function handleReadFile(params: { path: string }): Promise<{ content: string }> {
  // 1. 安全加固
  const resolvedPath = path.resolve(SAFE_ROOT, params.path);
  if (!resolvedPath.startsWith(SAFE_ROOT)) {
    throw new JsonRpcError(-32602, 'Invalid path: Access denied');
  }

  // 2. 业务逻辑与错误处理
  try {
    const fileContent = await fs.readFile(resolvedPath, 'utf-8');
    return { content: fileContent };
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      throw new JsonRpcError(1001, 'File not found', { path: params.path });
    } else {
      throw new JsonRpcError(-32000, 'Server error', { details: error.message });
    }
  }
}
```

### 第三步：连接“契约”与“实现” (The Wiring) - *在主服务中注册处理器*

此步骤是“胶水代码”，将外部请求路由到我们健壮的业务逻辑单元。

-   **产出物:** `server.ts` 中增加的 `setRequestHandler` 调用。
-   **核心要点:** 这是一个声明式的路由配置，应保持简单清晰。

```typescript
// server.ts
import { server } from './server';
import { ReadFileRequestSchema } from './protocols/fileProtocol';
import { handleReadFile } from './handlers/fileHandler';

server.setRequestHandler(
  ReadFileRequestSchema, 
  (request) => handleReadFile(request.params)
);
```

### 第四步：选择“载体” (The Transport) - *启动服务器*

此步骤完全不变，它为整个应用提供了动力。

-   **产出物:** `server.ts` 中启动服务器的最后几行代码。
-   **核心要点:** 业务逻辑应与通信方式完全解耦。

---

## 高级概念：超越简单的请求/响应

#### 通知 (Notifications)

当服务端需要主动向客户端发送信息，而不需要客户端响应时（例如，一个长时任务的进度更新），应使用“通知”。

-   **定义契约:** 同样使用 Zod 定义通知的 Schema。
-   **发送通知:** 在服务端调用 `server.notify(NotificationSchema, params)`。

#### 资源 (Resources)

对于有状态的、长生命周期的对象（如一个持续运行的分析任务），应将其建模为“资源”。MCP 提供了标准的 `resources/list`, `resources/read` 等方法来管理这些资源，客户端可以通过 URI 来与之交互。

---

## 可测试性：契约的馈赠

“契约式编程”极大地简化了测试。由于我们的核心业务逻辑（`handleReadFile`）被封装在独立的、可导出的函数中，我们可以：

-   **轻松进行单元测试:** 无需启动整个 MCP 服务器，可以直接导入 `handleReadFile` 函数，传入各种“理想”和“非理想”的 `params`，并断言其返回值或抛出的 `JsonRpcError` 是否符合预期。

---

## 我们踩过的“坑”：必须避开的反模式

_(此部分内容保持不变，仍然至关重要)_ 

1.  **反模式：在 SDK 里“寻宝”**
2.  **反模式：路径“魔法”**
3.  **反模式：传输层与协议层混淆**

---

## 结论：MCP 编程的本质

**MCP 编程，本质上是一种高度形式化的“应用层通信协议”设计过程。** 它迫使我们以“架构师”而非“程序员”的视角，首先思考系统的边界、能力、安全性和错误处理的契约，然后才是具体的实现。这种模式虽然初期有学习曲线，但其带来的长期回报是巨大的：一个逻辑清晰、类型安全、高度解耦、易于测试和扩展的**健壮系统**。
