import * as vscode from 'vscode';

import type { RequestEntryPoint } from '../entry/RequestEntryPoint.js';
import type { ModelConfig } from '../subject/modelConfigs.js';
import type { MessageFromWebview, MessageToWebview } from './webviewMessageProtocol.js';
import { message_from_webview_schema } from './webviewMessageProtocol.js';

/**
 * A type guard to check if a value is an instance of Map.
 * @param value The value to check.
 * @returns True if the value is a Map, otherwise false.
 */
function is_map(value: unknown): value is Map<unknown, unknown> {
  return value instanceof Map;
}

/**
 * @description Provides the webview panel for the chat interface.
 * This class is responsible for creating the webview, loading the static HTML,
 * and acting as a message broker between the webview and the extension's backend.
 */
export class ChatPanelProvider implements vscode.WebviewViewProvider {
  public static readonly view_type = 'llm-bridge.chat';
  private _view?: vscode.WebviewView;
  private readonly _context: vscode.ExtensionContext;
  private readonly _request_entry_point: RequestEntryPoint;
  private readonly _model_configs: Map<string, ModelConfig>;
  private _current_model_key: string | null = null; // 当前webview对应的模型
  private _chat_history: Array<{type: 'user' | 'bot' | 'error', content: string, timestamp: number}> = [];
  private _current_input: string = '';

  // 静态映射：存储每个模型对应的Provider实例
  private static _provider_instances: Map<string, ChatPanelProvider> = new Map();

  constructor(
    context: vscode.ExtensionContext,
    request_entry_point: RequestEntryPoint,
    model_configs: Map<string, ModelConfig>,
  ) {
    this._context = context;
    this._request_entry_point = request_entry_point;
    // The linter flags an unsafe assignment, likely due to type resolution issues
    // originating from other files. We disable the rule here because the property's
    // usage is protected by a type guard to ensure runtime safety.
    this._model_configs = model_configs;
  }

  // 静态方法：根据模型键获取对应的Provider实例
  public static get_provider_for_model(model_key: string): ChatPanelProvider | undefined {
    return ChatPanelProvider._provider_instances.get(model_key);
  }

  // This method must be camelCase to correctly implement the vscode.WebviewViewProvider interface.
  // eslint-disable-next-line @typescript-eslint/naming-convention
  public async resolveWebviewView(
    webview_view: vscode.WebviewView,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _context: vscode.WebviewViewResolveContext,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _token: vscode.CancellationToken,
  ): Promise<void> {
    this._view = webview_view;

    // 根据viewType确定当前webview对应的模型
    const view_type = webview_view.viewType;
    let current_model_key: string | null = null;

    // 动态建立viewType与配置ID的映射关系
    if (view_type.startsWith('chatPanel-')) {
      const model_id = view_type.replace('chatPanel-', '');
      if (is_map(this._model_configs) && this._model_configs.has(model_id)) {
        current_model_key = model_id;
        this._current_model_key = model_id;
        // 注册当前Provider实例到静态映射中
        ChatPanelProvider._provider_instances.set(model_id, this);
      }
    }

    webview_view.webview.options = {
      enableScripts: true,
      localResourceRoots: [vscode.Uri.joinPath(this._context.extensionUri, 'media')],
    };

    // 生成独立的HTML内容，传递当前模型信息
    webview_view.webview.html = this._generate_independent_html(webview_view.webview, current_model_key);

    webview_view.webview.onDidReceiveMessage(async (message: unknown) => {
      const parsed_message = message_from_webview_schema.safeParse(message);

      if (!parsed_message.success) {
        console.error('Received invalid message from webview:', parsed_message.error);
        return;
      }

      const safe_message: MessageFromWebview = parsed_message.data;

      try {
        switch (safe_message.type) {
          case 'userInput':
            await this._request_entry_point.handle_user_request(safe_message.input, safe_message.model);
            break;
          case 'webviewReady': {
            if (is_map(this._model_configs) && current_model_key && this._model_configs.has(current_model_key)) {
              const model_config = this._model_configs.get(current_model_key)!;
              const models = { [current_model_key]: { name: model_config.name } };
              console.log(`[ChatPanelProvider] webviewReady for ${view_type}, model:`, JSON.stringify(models));
              this.post_message({
                type: 'initialize',
                models,
              });
              this.post_message({
                type: 'updateModelList',
                models: [current_model_key],
              });
            } else {
              console.log(`[ChatPanelProvider] No model found for viewType: ${view_type}`);
              this.post_message({ type: 'updateModelList', models: [] });
            }
            break;
          }
          case 'addConfig':
            void vscode.window.showInformationMessage('添加配置功能将在后续版本中实现');
            break;
          case 'openSettings':
            void vscode.window.showInformationMessage('设置功能将在后续版本中实现');
            break;
          case 'showMenu':
            void vscode.window.showInformationMessage('菜单功能将在后续版本中实现');
            break;
          case 'revealConfigFile':
            if (safe_message.model && typeof safe_message.model === 'string') {
              await this._reveal_config_file(safe_message.model);
            }
            break;
          case 'openInEditor':
            if (safe_message.model && safe_message.chatHistory) {
              await this._open_in_editor(safe_message.model, safe_message.chatHistory);
            }
            break;
        }
      } catch (error: unknown) {
        const error_message = error instanceof Error ? error.message : String(error);
        void vscode.window.showErrorMessage(`Error handling webview message: ${error_message}`);
      }
    });
  }

  private _update_resource_uris(html_content: string, webview: vscode.Webview): string {
    return html_content
      .replace(/<link rel="stylesheet" href="(.*?)">/g, (_match: string, href: string) => {
        const resource_uri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', href));
        return `<link rel="stylesheet" href="${resource_uri.toString(true)}">`;
      })
      .replace(/<script src="(.*?)"><\/script>/g, (_match: string, src: string) => {
        const resource_uri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', src));
        return `<script src="${resource_uri.toString(true)}"></script>`;
      });
  }

  public post_message(message: MessageToWebview): void {
    console.log('[ChatPanelProvider] post_message:', JSON.stringify(message));
    if (this._view) {
      void this._view.webview.postMessage(message);
    }
  }

  // 静态方法：向特定模型的webview发送消息
  public static post_message_to_model(model_key: string, message: MessageToWebview): void {
    const provider = ChatPanelProvider._provider_instances.get(model_key);
    if (provider) {
      provider.post_message(message);
    } else {
      console.warn(`[ChatPanelProvider] No provider found for model: ${model_key}`);
    }
  }

  private _generate_independent_html(webview: vscode.Webview, model_key: string | null): string {
    const model_config = model_key ? this._model_configs.get(model_key) : null;
    const model_name = model_config?.name || 'Unknown Model';
    const theme_color = model_config?.ui_config?.theme_color || '#007ACC';
    const welcome_message = model_config?.ui_config?.welcome_message || '欢迎使用AI助手！';

    const css_uri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', 'chat.css'));
    const nonce = this._get_nonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${model_name}</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
    <link rel="stylesheet" href="${css_uri}">
    <style>
        :root {
            --model-theme-color: ${theme_color};
        }
        .model-header {
            background-color: var(--model-theme-color);
            color: white;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
        }
        .model-title {
            font-size: 1.1em;
        }
        .header-controls {
            display: flex;
            gap: 8px;
        }
        .control-button {
            padding: 4px 8px;
            font-size: 0.85em;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            cursor: pointer;
        }
        .control-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .reveal-button {
            padding: 4px 6px;
            font-size: 0.9em;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            cursor: pointer;
            margin-left: 8px;
        }
        .reveal-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="model-header">
        <div class="model-title">${model_name}</div>
        <div class="header-controls">
            <button class="reveal-button" id="open-in-editor-button" title="在编辑器中打开">→</button>
        </div>
    </div>
    <div id="chat-history" class="chat-history"></div>
    <div class="chat-input">
        <div class="input-wrapper">
            <textarea id="user-input" placeholder="输入消息..." rows="1"></textarea>
        </div>
        <button id="send-button">发送</button>
    </div>
    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        const MODEL_KEY = '${model_key}';

        // 状态持久化
        let chatHistory = vscode.getState()?.chatHistory || [];
        let currentInput = vscode.getState()?.currentInput || '';

        // 恢复状态
        const userInput = document.getElementById('user-input');
        userInput.value = currentInput;
        renderChatHistory();

        // 自动调整textarea高度
        function autoResizeTextarea() {
            userInput.style.height = 'auto';
            userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
        }

        // 监听输入变化
        userInput.addEventListener('input', function() {
            autoResizeTextarea();
            saveState();
        });

        // 初始化高度
        autoResizeTextarea();

        // 保存状态
        function saveState() {
            vscode.setState({
                chatHistory: chatHistory,
                currentInput: document.getElementById('user-input').value
            });
        }

        // 渲染聊天历史
        function renderChatHistory() {
            const historyDiv = document.getElementById('chat-history');
            historyDiv.innerHTML = '';
            chatHistory.forEach(msg => {
                addMessageToDOM(msg.type, msg.content, msg.timestamp);
            });
        }

        // 添加消息到DOM
        function addMessageToDOM(type, content, timestamp) {
            const historyDiv = document.getElementById('chat-history');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type + '-message';
            messageDiv.innerHTML = '<div class="message-content">' + content + '</div><div class="message-time">' + new Date(timestamp).toLocaleTimeString() + '</div>';
            historyDiv.appendChild(messageDiv);
            historyDiv.scrollTop = historyDiv.scrollHeight;
        }

        // 添加消息到历史
        function addMessage(type, content) {
            const timestamp = Date.now();
            chatHistory.push({type, content, timestamp});
            addMessageToDOM(type, content, timestamp);
            saveState();
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('user-input');
            const text = input.value.trim();
            if (text) {
                addMessage('user', text);
                vscode.postMessage({
                    type: 'userInput',
                    model: MODEL_KEY,
                    input: text
                });
                input.value = '';
                saveState();
            }
        }

        // 右箭头按钮事件 - 在编辑器中打开
        document.getElementById('open-in-editor-button').addEventListener('click', function() {
            vscode.postMessage({
                type: 'openInEditor',
                model: MODEL_KEY,
                chatHistory: chatHistory
            });
        });

        // 事件监听
        document.getElementById('send-button').addEventListener('click', sendMessage);
        document.getElementById('user-input').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
            // 实时保存输入状态
            setTimeout(saveState, 100);
        });

        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'addResponse':
                    if (message.content) {
                        addMessage('bot', message.content);
                    }
                    break;
                case 'showError':
                    if (message.error) {
                        addMessage('error', message.error);
                    }
                    break;
            }
        });

        // 通知扩展webview已准备就绪
        vscode.postMessage({ type: 'webviewReady' });
    </script>
</body>
</html>`;
  }

  private async _open_in_editor(model_key: string, chatHistory: Array<{type: 'user' | 'bot' | 'error', content: string, timestamp: number}>): Promise<void> {
    try {
      // 获取模型配置信息
      const model_config = this._model_configs.get(model_key);
      const model_name = model_config?.name || model_key;

      // 构建聊天内容的Markdown格式
      let content = `# ${model_name} 聊天记录\n\n`;

      if (chatHistory.length === 0) {
        content += '暂无聊天记录\n';
      } else {
        chatHistory.forEach((msg, index) => {
          const time = new Date(msg.timestamp).toLocaleString();
          const roleLabel = msg.type === 'user' ? '👤 用户' : msg.type === 'bot' ? '🤖 助手' : '❌ 错误';

          content += `## ${index + 1}. ${roleLabel} (${time})\n\n`;
          content += `${msg.content}\n\n`;
          content += '---\n\n';
        });
      }

      // 创建新的编辑器文档
      const document = await vscode.workspace.openTextDocument({
        content: content,
        language: 'markdown',
      });

      // 在新的编辑器列中显示文档
      await vscode.window.showTextDocument(document, vscode.ViewColumn.Beside);

      console.log(`[ChatPanelProvider] 在编辑器中打开聊天记录: ${model_name}`);
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      void vscode.window.showErrorMessage(`无法在编辑器中打开聊天记录: ${error_message}`);
      console.error(`[ChatPanelProvider] 在编辑器中打开失败:`, error);
    }
  }

  private async _reveal_config_file(model_key: string): Promise<void> {
    try {
      // 构建配置文件路径
      const config_file_name = `${model_key}_config.json`;
      const config_file_path = vscode.Uri.joinPath(this._context.extensionUri, 'config', config_file_name);

      // 检查文件是否存在
      try {
        await vscode.workspace.fs.stat(config_file_path);
        // 文件存在，在文件资源管理器中显示
        await vscode.commands.executeCommand('revealInExplorer', config_file_path);
        console.log(`[ChatPanelProvider] 在文件资源管理器中显示配置文件: ${config_file_path.fsPath}`);
      } catch (error) {
        // 文件不存在
        void vscode.window.showWarningMessage(`配置文件 ${config_file_name} 不存在`);
        console.warn(`[ChatPanelProvider] 配置文件不存在: ${config_file_path.fsPath}`);
      }
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      void vscode.window.showErrorMessage(`无法显示配置文件: ${error_message}`);
      console.error(`[ChatPanelProvider] 显示配置文件失败:`, error);
    }
  }

  private _get_nonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}
