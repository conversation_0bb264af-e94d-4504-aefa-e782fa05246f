/**
 * 配置系统测试脚本
 * 用于验证统一配置管理系统的功能
 */

const fs = require('fs');
const path = require('path');

// 测试配置文件路径
const CONFIG_DIR = path.join(__dirname, '../config');
const TEST_CONFIG_PATH = path.join(CONFIG_DIR, 'test_config.json');

// 测试配置模板
const TEST_CONFIG = {
  "id": "test-model",
  "name": "Test Model",
  "description": "测试用配置",
  "version": "1.0.0",
  "api_type": "mistral",
  "capabilities": {
    "max_tokens": 4096,
    "supports_streaming": true,
    "supports_function_calling": true,
    "supports_vision": false,
    "supports_code_execution": true
  },
  "available_tools": [
    {
      "name": "reply_to_user",
      "description": "回复用户消息",
      "parameters": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "要发送给用户的消息"
          }
        },
        "required": ["message"]
      }
    },
    {
      "name": "execute_command",
      "description": "执行VS Code命令",
      "parameters": {
        "type": "object",
        "properties": {
          "command": {
            "type": "string",
            "description": "要执行的命令"
          },
          "args": {
            "type": "array",
            "description": "命令参数"
          }
        },
        "required": ["command"]
      }
    }
  ],
  "prompt_templates": {
    "system_prompt": "你是一个智能的VS Code助手，可以帮助用户进行代码开发和项目管理。",
    "user_prompt_template": "用户请求：{{user_request}}\n\n工作区根目录：{{workspace_root}}\n当前文件：{{active_file_path}}\n选中文本：{{selected_text}}",
    "tool_instruction_template": "你可以使用以下工具来完成任务：\n{{available_tools}}\n\n请根据用户需求选择合适的工具。",
    "context_template": "当前上下文：\n- 工作区：{{workspace_root}}\n- 活动文件：{{active_file_path}}\n- 选中文本：{{selected_text}}\n- 打开的文件：{{open_files}}"
  },
  "ui_config": {
    "panel_title": "Test Model",
    "panel_icon": "robot",
    "theme_color": "#007ACC",
    "welcome_message": "你好！我是测试模型，可以帮助你进行开发工作。"
  },
  "api_config": {
    "timeout": 30000,
    "api_key": "YOUR_MISTRAL_API_KEY",
    "base_url": "https://api.mistral.ai/v1/chat/completions",
    "model": "mistral-large-latest"
  }
};

/**
 * 测试配置文件创建
 */
function test_config_creation() {
  console.log('🧪 测试配置文件创建...');
  
  try {
    // 确保配置目录存在
    if (!fs.existsSync(CONFIG_DIR)) {
      fs.mkdirSync(CONFIG_DIR, { recursive: true });
    }
    
    // 写入测试配置
    fs.writeFileSync(TEST_CONFIG_PATH, JSON.stringify(TEST_CONFIG, null, 2));
    console.log('✅ 测试配置文件创建成功');
    
    // 验证文件存在
    if (fs.existsSync(TEST_CONFIG_PATH)) {
      console.log('✅ 配置文件存在验证通过');
    } else {
      console.log('❌ 配置文件存在验证失败');
    }
    
    return true;
  } catch (error) {
    console.log('❌ 配置文件创建失败:', error.message);
    return false;
  }
}

/**
 * 测试配置文件读取
 */
function test_config_reading() {
  console.log('🧪 测试配置文件读取...');
  
  try {
    const config_content = fs.readFileSync(TEST_CONFIG_PATH, 'utf8');
    const config = JSON.parse(config_content);
    
    // 验证基本字段
    const required_fields = ['id', 'name', 'version', 'api_type', 'capabilities', 'available_tools', 'prompt_templates', 'ui_config', 'api_config'];
    
    for (const field of required_fields) {
      if (!(field in config)) {
        console.log(`❌ 缺少必需字段: ${field}`);
        return false;
      }
    }
    
    console.log('✅ 配置文件读取成功');
    console.log(`✅ 配置ID: ${config.id}`);
    console.log(`✅ 配置名称: ${config.name}`);
    console.log(`✅ API类型: ${config.api_type}`);
    console.log(`✅ 可用工具数量: ${config.available_tools.length}`);
    
    return true;
  } catch (error) {
    console.log('❌ 配置文件读取失败:', error.message);
    return false;
  }
}

/**
 * 测试配置验证
 */
function test_config_validation() {
  console.log('🧪 测试配置验证...');
  
  try {
    const config_content = fs.readFileSync(TEST_CONFIG_PATH, 'utf8');
    const config = JSON.parse(config_content);
    
    // 基本验证
    const validations = [
      {
        name: 'ID格式',
        test: () => typeof config.id === 'string' && config.id.length > 0
      },
      {
        name: '版本格式',
        test: () => /^\d+\.\d+\.\d+$/.test(config.version)
      },
      {
        name: 'API类型',
        test: () => ['mistral', 'spark', 'openai'].includes(config.api_type)
      },
      {
        name: '能力配置',
        test: () => typeof config.capabilities === 'object' && 
                   typeof config.capabilities.supports_streaming === 'boolean'
      },
      {
        name: '工具配置',
        test: () => Array.isArray(config.available_tools) && 
                   config.available_tools.length > 0
      },
      {
        name: '提示词模板',
        test: () => typeof config.prompt_templates === 'object' &&
                   typeof config.prompt_templates.system_prompt === 'string'
      },
      {
        name: 'UI配置',
        test: () => typeof config.ui_config === 'object' &&
                   typeof config.ui_config.panel_title === 'string'
      },
      {
        name: 'API配置',
        test: () => typeof config.api_config === 'object' &&
                   typeof config.api_config.api_key === 'string'
      }
    ];
    
    let passed = 0;
    for (const validation of validations) {
      if (validation.test()) {
        console.log(`✅ ${validation.name} 验证通过`);
        passed++;
      } else {
        console.log(`❌ ${validation.name} 验证失败`);
      }
    }
    
    console.log(`📊 验证结果: ${passed}/${validations.length} 通过`);
    return passed === validations.length;
    
  } catch (error) {
    console.log('❌ 配置验证失败:', error.message);
    return false;
  }
}

/**
 * 测试配置扫描
 */
function test_config_scanning() {
  console.log('🧪 测试配置扫描...');
  
  try {
    const files = fs.readdirSync(CONFIG_DIR);
    const config_files = files.filter(file => file.endsWith('_config.json'));
    
    console.log(`📁 配置目录: ${CONFIG_DIR}`);
    console.log(`📄 找到配置文件: ${config_files.length} 个`);
    
    for (const file of config_files) {
      console.log(`  - ${file}`);
    }
    
    // 验证测试配置文件在列表中
    if (config_files.includes('test_config.json')) {
      console.log('✅ 测试配置文件扫描成功');
      return true;
    } else {
      console.log('❌ 测试配置文件未被扫描到');
      return false;
    }
    
  } catch (error) {
    console.log('❌ 配置扫描失败:', error.message);
    return false;
  }
}

/**
 * 清理测试文件
 */
function cleanup_test_files() {
  console.log('🧹 清理测试文件...');
  
  try {
    if (fs.existsSync(TEST_CONFIG_PATH)) {
      fs.unlinkSync(TEST_CONFIG_PATH);
      console.log('✅ 测试配置文件已删除');
    }
  } catch (error) {
    console.log('❌ 清理失败:', error.message);
  }
}

/**
 * 运行所有测试
 */
function run_all_tests() {
  console.log('🚀 开始配置系统测试\n');
  
  const tests = [
    { name: '配置文件创建', fn: test_config_creation },
    { name: '配置文件读取', fn: test_config_reading },
    { name: '配置验证', fn: test_config_validation },
    { name: '配置扫描', fn: test_config_scanning }
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    console.log(`\n--- ${test.name} ---`);
    if (test.fn()) {
      passed++;
    }
  }
  
  console.log('\n📊 测试总结');
  console.log(`通过: ${passed}/${total}`);
  console.log(`成功率: ${Math.round(passed / total * 100)}%`);
  
  if (passed === total) {
    console.log('🎉 所有测试通过！配置系统工作正常。');
  } else {
    console.log('⚠️ 部分测试失败，请检查配置系统。');
  }
  
  // 清理测试文件
  cleanup_test_files();
  
  return passed === total;
}

// 运行测试
if (require.main === module) {
  run_all_tests();
}

module.exports = {
  run_all_tests,
  test_config_creation,
  test_config_reading,
  test_config_validation,
  test_config_scanning
};
