import * as vscode from 'vscode';

import type { IComposableModule } from '../verb/moduleInterfaces.js';
import type { ContextPackage, LlmTool } from './dataModels.js';

/**
 * @description Captures the user's environment context to be sent to the LLM.
 */
export class ContextCapturerModule implements IComposableModule {
  /**
   * @description Captures the current context, including active file, selection, and initial tools.
   * @param user_request The user's text request from the chat.
   * @returns A ContextPackage object.
   */
  public execute(context: ContextPackage): Promise<ContextPackage> {
    const editor = vscode.window.activeTextEditor;
    const workspace_folders = vscode.workspace.workspaceFolders;

    context.active_file_path = editor ? editor.document.uri.fsPath : null;
    context.selected_text = editor ? editor.document.getText(editor.selection) : '';
    context.full_text = editor ? editor.document.getText() : '';
    context.workspace_root = workspace_folders && workspace_folders.length > 0 ? workspace_folders[0].uri.fsPath : null;

    // The initial set of tools is now static and very small.
    const initial_tools: LlmTool[] = [
      {
        name: 'reply_to_user',
        description:
          'Replies directly to the user in the chat window. Use this to ask for clarification or to provide the final answer.',
        parameters: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'The message to display to the user.',
            },
          },
          required: ['message'],
        },
      },
      {
        name: 'search_commands',
        description:
          'Searches for available VS Code commands based on a query. Use this to find relevant commands to execute.',
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'The search term to find relevant commands (e.g., "format", "git", "save").',
            },
          },
          required: ['query'],
        },
      },
      {
        name: 'get_file_outline',
        description:
          'Gets the structural outline of a file (classes, functions, etc.). Use this to understand the structure of a file before reading it.',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: 'The absolute path to the file to outline. You can get this from the active file path.',
            },
          },
          required: ['file_path'],
        },
      },
      {
        name: 'read_file_range',
        description:
          'Reads a specific range of lines from a file. Use this after getting the outline to read specific functions or classes.',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: 'The absolute path to the file to read.',
            },
            start_line: {
              type: 'number',
              description: 'The 1-based starting line number to read from.',
            },
            end_line: {
              type: 'number',
              description: 'The 1-based ending line number to read to.',
            },
          },
          required: ['file_path', 'start_line', 'end_line'],
        },
      },
      {
        name: 'search_in_workspace',
        description:
          'Searches for a keyword across all files in the current workspace. Returns a list of matching lines.',
        parameters: {
          type: 'object',
          properties: {
            keyword: {
              type: 'string',
              description: 'The keyword or text to search for.',
            },
          },
          required: ['keyword'],
        },
      },
    ];

    context.available_tools = initial_tools;
    context.tool_executions = []; // Initially empty

    return Promise.resolve(context);
  }
}
