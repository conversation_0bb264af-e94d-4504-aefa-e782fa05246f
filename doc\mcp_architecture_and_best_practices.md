# MCP 架构选择与最佳实践

本文档旨在沉淀我们在 `llm-bridge` 项目中，围绕模型上下文协议（MCP）开发过程中获得的深刻见解与最佳实践，为未来的开发和维护提供坚实的理论基础。

## 核心结论：传输方式是关键的架构决策

MCP 的实践核心在于选择正确的 **传输层（Transport Layer）**。这是一个关键的架构决策，直接决定了项目的稳定性、性能和适用范围。主要有两种选择：`Stdio` 和 `HTTP`。

### 1. `Stdio` (标准输入/输出): 本地进程通信的“高速公路”

- **工作原理**: 主程序（如 VS Code 插件）直接启动一个子进程（MCP 服务器）。两者通过操作系统底层的“管道”（Standard Input/Output）进行通信，不涉及任何网络协议。
- **优点**:
    - **极其稳定**: 从根本上避免了端口冲突、CORS、防火墙、HTTP 协议及 SSE 握手等所有网络相关的复杂性和 Bug。
    - **性能极高**: 纯内存数据交换，无网络延迟。
    - **简单安全**: 无需监听网络端口，实现简单，安全性高。
- **适用场景**: **客户端和服务器保证运行在同一台机器上。** 这是绝大多数桌面应用插件（如 VS Code, IntelliJ）和后台服务的首选和最佳实践。
- **参考实现**: 社区中的 `mcp-server-client` 示例项目即采用此模式。

### 2. `HTTP` + `SSE`: 网络/浏览器通信的“必经之路”

- **工作原理**: 服务器监听一个网络端口，通过标准的 HTTP 协议对外提供服务，并利用 Server-Sent Events (SSE) 实现服务器到客户端的推送。
- **优点**:
    - **通用访问**: 允许任何能够访问网络的客户端连接，**特别是浏览器环境的客户端**。
- **缺点**:
    - **复杂度剧增**: 必须处理端口管理、CORS 跨域、HTTP 协议本身的复杂性。
    - **稳定性风险**: 任何网络波动、防火墙配置或 SDK 在 HTTP 层的实现缺陷（**我们已亲身经历**）都可能导致严重问题。

## 复盘我们的选择：为何选择更难的 `HTTP` 道路？

我们的 `llm-bridge` 项目选择了 `HTTP` 传输层。这是一个**清醒且有远见的决策**，而非无意的失误。

- **根本原因**: 我们从项目初期就预见了未来需要支持 **Web 视图（WebView）** 或其他浏览器客户端直接与 MCP 服务器通信的场景。
- **付出的代价**: 这个选择让我们成为了 `@modelcontextprotocol/sdk` 中 `StreamableHTTPServerTransport` 模块稳定性的“早期测试者”，并迫使我们通过“外科手术式”的补丁（如 SSE 心跳）来修复其实现缺陷。

**结论**: 我们的选择是正确的，我们的修复是必要的。我们为未来的扩展性付出了前期的研发成本，并成功克服了挑战。

## 行动纲领与未来展望

1.  **保留并维护补丁**: 我们为 `HTTP` 传输层添加的“心跳补丁”是项目的宝贵资产，在官方 SDK 修复此问题前，必须予以保留和维护。
2.  **坚持文档化**: 持续将架构决策、遇到的问题和解决方案记录到文档中。
3.  **技术选型**: 未来在新的项目中或为本项目添加新的通信模块时，应首先进行如本文档一般的分析，根据是否需要跨网络/浏览器访问来审慎选择 `Stdio` 或 `HTTP`。
