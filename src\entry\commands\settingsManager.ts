import * as vscode from 'vscode';

import { configuration_manager } from '../../config/ConfigurationManager.js';
import { ConfigurationValidator } from '../../config/ConfigurationValidator.js';

/**
 * 设置管理器 - 管理插件的全局设置和配置
 */
export function register_settings_manager(context: vscode.ExtensionContext): void {
  context.subscriptions.push(
    vscode.commands.registerCommand('llm-bridge.openSettings', async () => {
      try {
        await show_settings_panel();
      } catch (error) {
        const error_message = error instanceof Error ? error.message : String(error);
        void vscode.window.showErrorMessage(`打开设置失败: ${error_message}`);
      }
    }),
  );
}

/**
 * 显示设置面板
 */
async function show_settings_panel(): Promise<void> {
  const stats = configuration_manager.get_stats();
  const all_configs = configuration_manager.get_all_configs();

  // 创建设置选项
  const settings_options = [
    {
      label: '$(gear) 配置管理',
      description: `管理 ${stats.total} 个模型配置`,
      detail: `API类型分布: ${Object.entries(stats.by_api_type).map(([type, count]) => `${type}(${count})`).join(', ')}`,
      action: 'manage_configs'
    },
    {
      label: '$(tools) 工具设置',
      description: `管理 ${stats.enabled_tools} 个启用的工具`,
      detail: '配置命令白名单、工具权限等',
      action: 'manage_tools'
    },
    {
      label: '$(comment-discussion) 提示词模板',
      description: '管理系统提示词和用户提示词模板',
      detail: '自定义AI助手的行为和响应风格',
      action: 'manage_prompts'
    },
    {
      label: '$(export) 导入/导出',
      description: '导入或导出配置文件',
      detail: '备份和分享配置设置',
      action: 'import_export'
    },
    {
      label: '$(info) 系统信息',
      description: '查看插件状态和诊断信息',
      detail: '检查配置完整性和连接状态',
      action: 'system_info'
    },
    {
      label: '$(check-all) 配置验证',
      description: '验证所有配置的完整性和正确性',
      detail: '检查API密钥、工具定义、提示词模板等',
      action: 'validate_configs'
    }
  ];

  const selected = await vscode.window.showQuickPick(settings_options, {
    placeHolder: '选择要管理的设置类型',
    title: 'LLM Bridge 设置管理'
  });

  if (!selected) {
    return;
  }

  switch (selected.action) {
    case 'manage_configs':
      await manage_configurations();
      break;
    case 'manage_tools':
      await manage_tools();
      break;
    case 'manage_prompts':
      await manage_prompts();
      break;
    case 'import_export':
      await import_export_settings();
      break;
    case 'system_info':
      await show_system_info();
      break;
    case 'validate_configs':
      await validate_all_configurations();
      break;
  }
}

/**
 * 管理配置
 */
async function manage_configurations(): Promise<void> {
  const all_configs = configuration_manager.get_all_configs();
  const config_options = Array.from(all_configs.entries()).map(([id, config]) => ({
    label: `$(${config.ui_config.panel_icon || 'gear'}) ${config.name}`,
    description: `${config.api_type.toUpperCase()} API`,
    detail: config.description || `模型ID: ${id}`,
    config_id: id
  }));

  config_options.push({
    label: '$(add) 添加新配置',
    description: '创建新的模型配置',
    detail: '支持 Mistral、Spark、OpenAI 等API',
    config_id: '__add_new__'
  });

  const selected = await vscode.window.showQuickPick(config_options, {
    placeHolder: '选择要管理的配置',
    title: '配置管理'
  });

  if (!selected) {
    return;
  }

  if (selected.config_id === '__add_new__') {
    await vscode.commands.executeCommand('llm-bridge.addConfig');
  } else {
    await edit_configuration(selected.config_id);
  }
}

/**
 * 编辑配置
 */
async function edit_configuration(config_id: string): Promise<void> {
  const config = configuration_manager.get_config(config_id);
  if (!config) {
    void vscode.window.showErrorMessage(`配置 ${config_id} 不存在`);
    return;
  }

  const actions = [
    {
      label: '$(edit) 编辑配置文件',
      description: '直接编辑JSON配置文件',
      action: 'edit_file'
    },
    {
      label: '$(tools) 管理工具',
      description: `管理 ${config.available_tools.length} 个可用工具`,
      action: 'manage_tools'
    },
    {
      label: '$(comment) 编辑提示词',
      description: '自定义系统提示词和模板',
      action: 'edit_prompts'
    },
    {
      label: '$(trash) 删除配置',
      description: '永久删除此配置',
      action: 'delete_config'
    }
  ];

  const selected = await vscode.window.showQuickPick(actions, {
    placeHolder: `管理配置: ${config.name}`,
    title: '配置操作'
  });

  if (!selected) {
    return;
  }

  switch (selected.action) {
    case 'edit_file':
      const config_file = vscode.Uri.file(
        require('path').join(__dirname, '../../../config', `${config_id}_config.json`)
      );
      await vscode.window.showTextDocument(config_file);
      break;
      
    case 'delete_config':
      const confirm = await vscode.window.showWarningMessage(
        `确定要删除配置 "${config.name}" 吗？此操作不可撤销。`,
        '删除',
        '取消'
      );
      if (confirm === '删除') {
        await configuration_manager.remove_config(config_id);
      }
      break;
  }
}

/**
 * 管理工具
 */
async function manage_tools(): Promise<void> {
  void vscode.window.showInformationMessage('工具管理功能开发中...');
}

/**
 * 管理提示词
 */
async function manage_prompts(): Promise<void> {
  void vscode.window.showInformationMessage('提示词管理功能开发中...');
}

/**
 * 导入导出设置
 */
async function import_export_settings(): Promise<void> {
  const actions = [
    {
      label: '$(export) 导出所有配置',
      description: '将所有配置导出为备份文件',
      action: 'export_all'
    },
    {
      label: '$(import) 导入配置',
      description: '从备份文件导入配置',
      action: 'import_configs'
    }
  ];

  const selected = await vscode.window.showQuickPick(actions, {
    placeHolder: '选择导入导出操作',
    title: '配置备份'
  });

  if (!selected) {
    return;
  }

  switch (selected.action) {
    case 'export_all':
      void vscode.window.showInformationMessage('导出功能开发中...');
      break;
    case 'import_configs':
      void vscode.window.showInformationMessage('导入功能开发中...');
      break;
  }
}

/**
 * 显示系统信息
 */
async function show_system_info(): Promise<void> {
  const stats = configuration_manager.get_stats();
  const all_configs = configuration_manager.get_all_configs();
  
  let info = `# LLM Bridge 系统信息\n\n`;
  info += `## 配置统计\n`;
  info += `- 总配置数: ${stats.total}\n`;
  info += `- 启用工具数: ${stats.enabled_tools}\n`;
  info += `- API类型分布:\n`;
  
  for (const [type, count] of Object.entries(stats.by_api_type)) {
    info += `  - ${type.toUpperCase()}: ${count} 个\n`;
  }
  
  info += `\n## 配置详情\n`;
  for (const [id, config] of all_configs.entries()) {
    info += `### ${config.name} (${id})\n`;
    info += `- API类型: ${config.api_type}\n`;
    info += `- 版本: ${config.version}\n`;
    info += `- 可用工具: ${config.available_tools.length} 个\n`;
    info += `- 支持功能调用: ${config.capabilities.supports_function_calling ? '是' : '否'}\n`;
    info += `- 支持流式输出: ${config.capabilities.supports_streaming ? '是' : '否'}\n\n`;
  }

  // 创建临时文档显示信息
  const doc = await vscode.workspace.openTextDocument({
    content: info,
    language: 'markdown'
  });
  await vscode.window.showTextDocument(doc);
}

/**
 * 验证所有配置
 */
async function validate_all_configurations(): Promise<void> {
  const all_configs = configuration_manager.get_all_configs();

  if (all_configs.size === 0) {
    void vscode.window.showWarningMessage('没有找到任何配置文件');
    return;
  }

  // 执行验证
  const validation_results = ConfigurationValidator.validate_all_configs(all_configs);

  // 生成报告
  const report = ConfigurationValidator.generate_validation_report(validation_results);

  // 统计结果
  const total_configs = validation_results.size;
  const valid_configs = Array.from(validation_results.values()).filter(r => r.is_valid).length;
  const invalid_configs = total_configs - valid_configs;
  const total_errors = Array.from(validation_results.values()).reduce((sum, r) => sum + r.errors.length, 0);
  const total_warnings = Array.from(validation_results.values()).reduce((sum, r) => sum + r.warnings.length, 0);

  // 显示结果摘要
  if (invalid_configs === 0 && total_warnings === 0) {
    void vscode.window.showInformationMessage(`✅ 所有 ${total_configs} 个配置都通过验证！`);
  } else if (invalid_configs === 0) {
    const action = await vscode.window.showWarningMessage(
      `⚠️ 配置验证完成：${valid_configs} 个有效，${total_warnings} 个警告`,
      '查看详细报告'
    );
    if (action === '查看详细报告') {
      await show_validation_report(report);
    }
  } else {
    const action = await vscode.window.showErrorMessage(
      `❌ 配置验证失败：${invalid_configs} 个无效，${total_errors} 个错误，${total_warnings} 个警告`,
      '查看详细报告',
      '修复配置'
    );

    if (action === '查看详细报告') {
      await show_validation_report(report);
    } else if (action === '修复配置') {
      await fix_configuration_issues(validation_results);
    }
  }
}

/**
 * 显示验证报告
 */
async function show_validation_report(report: string): Promise<void> {
  const doc = await vscode.workspace.openTextDocument({
    content: report,
    language: 'markdown'
  });
  await vscode.window.showTextDocument(doc);
}

/**
 * 修复配置问题
 */
async function fix_configuration_issues(validation_results: Map<string, any>): Promise<void> {
  const invalid_configs = Array.from(validation_results.entries())
    .filter(([_, result]) => !result.is_valid)
    .map(([config_id, result]) => ({
      label: `$(error) ${config_id}`,
      description: `${result.errors.length} 个错误`,
      detail: result.errors.slice(0, 2).join('; ') + (result.errors.length > 2 ? '...' : ''),
      config_id
    }));

  if (invalid_configs.length === 0) {
    void vscode.window.showInformationMessage('没有需要修复的配置');
    return;
  }

  const selected = await vscode.window.showQuickPick(invalid_configs, {
    placeHolder: '选择要修复的配置',
    title: '修复配置问题'
  });

  if (selected) {
    await edit_configuration(selected.config_id);
  }
}
