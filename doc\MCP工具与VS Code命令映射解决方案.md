# MCP工具与VS Code命令映射解决方案

## 🎯 问题描述

用户发现了一个**关键的架构问题**：

> "在MCP中注册的命令 大模型调用后传递给服务端 因此服务端也应该向vscode注册一模一样的命令 否则服务端就不能调用 就是在MCP注册的工具 必须同样在vscode里注册 而且MCP里注册的命令 在vscode里注册 必须在白名单 不可移动 为默认命令"

### 核心问题：
1. **MCP工具 → VS Code命令映射缺失**：MCP注册了工具，但VS Code中没有对应的命令注册
2. **服务端执行失败**：服务端收到调用请求后，找不到对应的VS Code命令来执行
3. **MCP工具必须在白名单且不可移动**：MCP工具是系统核心功能，必须始终可用

## 🔧 解决方案实施

### 1. **为每个MCP工具注册对应的VS Code命令**

**文件：`src/extension.ts`**
```typescript
/**
 * 注册MCP工具对应的VS Code命令
 * 确保每个MCP工具都有对应的VS Code命令，解决服务端调用问题
 */
function register_mcp_tool_commands(context: vscode.ExtensionContext, all_tools: Map<string, any>): void {
  // 为每个MCP工具注册对应的VS Code命令
  for (const [tool_name, tool] of all_tools.entries()) {
    context.subscriptions.push(
      vscode.commands.registerCommand(tool_name, async (params: unknown): Promise<unknown> => {
        try {
          console.log(`🔧 执行MCP工具命令: ${tool_name}`, params);
          
          // 验证参数
          const validated_params = tool.parameters_schema ? tool.parameters_schema.parse(params || {}) : (params || {});
          
          // 执行工具处理器
          const result = await tool.handler(validated_params);
          
          console.log(`✅ MCP工具命令执行成功: ${tool_name}`, result);
          return result;
        } catch (error) {
          const error_message = error instanceof Error ? error.message : String(error);
          console.error(`❌ MCP工具命令执行失败: ${tool_name}`, error_message);
          void vscode.window.showErrorMessage(`MCP工具执行出错 [${tool_name}]: ${error_message}`);
          throw error;
        }
      })
    );
  }
  
  console.log(`📝 已注册 ${all_tools.size} 个MCP工具对应的VS Code命令`);
}
```

### 2. **MCP核心工具白名单保护机制**

**文件：`src/object/CommandStorageModule.ts`**
```typescript
// MCP核心工具命令 - 不可移动的默认命令
const MCP_CORE_TOOLS = [
  'list_all_vscode_commands',
  'execute_vscode_command', 
  'search_commands',
  'show_message',
  'capture_context',
  'add_command_to_whitelist',
  'remove_command_from_whitelist',
  'get_whitelisted_commands',
  'get_available_commands',
];

const DEFAULT_WHITELIST = [
  'workbench.action.quickOpen',
  'workbench.action.showCommands',
  'workbench.action.tasks.runTask',
  'workbench.action.debug.start',
  'workbench.action.files.saveAll',
  // MCP核心工具 - 系统必需，不可移动
  ...MCP_CORE_TOOLS,
];
```

### 3. **安全的白名单管理**

**新增方法：**
- `is_mcp_core_tool(command_id: string): boolean` - 检查是否为MCP核心工具
- `get_mcp_core_tools(): string[]` - 获取所有MCP核心工具列表
- `safe_remove_from_whitelist(command_id: string): Promise<boolean>` - 安全移除（保护MCP核心工具）

### 4. **MCP工具中的保护逻辑**

**文件：`src/verb/tools/registerAllTools.ts`**
```typescript
// Tool: Remove command from whitelist (with MCP core tool protection)
all_tools.set('remove_command_from_whitelist', {
  description: 'Removes a command from the whitelist. MCP core tools cannot be removed.',
  parameters_schema: z.object({ command_id: z.string() }),
  handler: async (parameters: unknown) => {
    const { command_id } = parameters as { command_id: string };
    
    // 检查是否为MCP核心工具
    if (command_storage.is_mcp_core_tool(command_id)) {
      return `❌ Cannot remove MCP core tool '${command_id}' from whitelist. This tool is required for system functionality.`;
    }
    
    const success = await command_storage.safe_remove_from_whitelist(command_id);
    if (success) {
      return `✅ Command '${command_id}' removed from whitelist.`;
    } else {
      return `ℹ️ Command '${command_id}' was not in the whitelist.`;
    }
  },
});
```

### 5. **UI界面增强**

**CommandManagerPanel UI更新：**
- 显示MCP核心工具的特殊标识（🔒 MCP核心）
- MCP核心工具不显示移除按钮
- 特殊的CSS样式突出显示保护状态

## 📊 解决的问题

### ✅ **已修复的映射关系**

| MCP工具 | VS Code命令 | 状态 |
|---------|-------------|------|
| `list_all_vscode_commands` | ✅ 已注册 | 🔒 白名单保护 |
| `execute_vscode_command` | ✅ 已注册 | 🔒 白名单保护 |
| `search_commands` | ✅ 已注册 | 🔒 白名单保护 |
| `show_message` | ✅ 已注册 | 🔒 白名单保护 |
| `capture_context` | ✅ 已注册 | 🔒 白名单保护 |
| `add_command_to_whitelist` | ✅ 已注册 | 🔒 白名单保护 |
| `remove_command_from_whitelist` | ✅ 已注册 | 🔒 白名单保护 |
| `get_whitelisted_commands` | ✅ 已注册 | 🔒 白名单保护 |
| `get_available_commands` | ✅ 已注册 | 🔒 白名单保护 |

### ✅ **补充实现的VS Code命令**

| 命令 | 实现状态 | 功能 |
|------|----------|------|
| `command_manager` | ✅ 已实现 | 打开命令管理面板 |
| `prompt_manager` | ✅ 已存在 | 多功能提示词管理 |
| `llm-bridge.manageWhitelist` | ✅ 已存在 | 白名单管理界面 |

## 🛡️ 安全保障机制

1. **不可移动保护**：MCP核心工具无法从白名单中移除
2. **UI视觉提示**：特殊标识和样式显示保护状态
3. **API级别保护**：`safe_remove_from_whitelist`方法拒绝移除核心工具
4. **错误处理**：完整的错误捕获和用户反馈机制
5. **日志记录**：详细的执行日志便于调试和监控

## 🎉 实施结果

✅ **MCP工具与VS Code命令完全映射**  
✅ **MCP核心工具白名单保护**  
✅ **服务端调用问题解决**  
✅ **UI界面增强显示保护状态**  
✅ **完整的安全保障机制**  

现在大模型可以安全地调用所有MCP工具，服务端能够找到对应的VS Code命令执行，同时MCP核心工具受到完整保护，确保系统稳定运行。
