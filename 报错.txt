128             console.log(`🧹 [ExternalLlmClientModule] 移除 ```json  
标记后:\n${cleaned_content}`);
    
     ~

src/subject/ExternalLlmClientModule.ts:129:53 - error TS1005: ',' expected.

129           } else if (cleaned_content.startsWith('```')) {
                                                        ~~~~~

src/subject/ExternalLlmClientModule.ts:129:58 - error TS1002: Unterminated string literal.

129           } else if (cleaned_content.startsWith('```')) {
    

src/subject/ExternalLlmClientModule.ts:130:13 - error TS1005: ',' expected.

130             cleaned_content = cleaned_content.replace(/^```\s*/, '').replace(/\s*```$/, '');
                ~~~~~~~~~~~~~~~

src/subject/ExternalLlmClientModule.ts:130:92 - error TS1005: ')' expected.

130             cleaned_content = cleaned_content.replace(/^```\s*/, '').replace(/\s*```$/, '');
    
                        ~

src/subject/ExternalLlmClientModule.ts:131:62 - error TS1005: ',' expected.

131             console.log(`🧹 [ExternalLlmClientModule] 移除 ``` 标记 
后:\n${cleaned_content}`);
                                                                 ~~~   

src/subject/ExternalLlmClientModule.ts:131:65 - error TS1005: ',' expected.

131             console.log(`🧹 [ExternalLlmClientModule] 移除 ``` 标记 
后:\n${cleaned_content}`);
                                                                    ~  

src/subject/ExternalLlmClientModule.ts:131:66 - error TS1127: Invalid character.

131             console.log(`🧹 [ExternalLlmClientModule] 移除 ``` 标记 
后:\n${cleaned_content}`);
                                                                       

src/subject/ExternalLlmClientModule.ts:131:69 - error TS1005: ',' expected.

131             console.log(`🧹 [ExternalLlmClientModule] 移除 ``` 标记 
后:\n${cleaned_content}`);
    
 ~

src/subject/ExternalLlmClientModule.ts:134:24 - error TS1127: Invalid character.

134           console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
                           ~~

src/subject/ExternalLlmClientModule.ts:134:53 - error TS1005: ',' expected.

134           console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
                                                        ~~~~

src/subject/ExternalLlmClientModule.ts:134:58 - error TS1005: ',' expected.

134           console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
                                                             ~~~~      

src/subject/ExternalLlmClientModule.ts:134:62 - error TS1005: ',' expected.

134           console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
                                                                 ~     

src/subject/ExternalLlmClientModule.ts:134:63 - error TS1127: Invalid character.

134           console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
                                                                       

src/subject/ExternalLlmClientModule.ts:134:66 - error TS1005: ',' expected.

134           console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
                                                                     ~ 

src/subject/ExternalLlmClientModule.ts:136:24 - error TS1127: Invalid character.

136           console.log(`✅ [ExternalLlmClientModule] JSON 解析成功:`,
 JSON.stringify(command_json, null, 2));
                           ~

src/subject/ExternalLlmClientModule.ts:136:52 - error TS1005: ',' expected.

136           console.log(`✅ [ExternalLlmClientModule] JSON 解析成功:`,
 JSON.stringify(command_json, null, 2));
                                                       ~~~~

src/subject/ExternalLlmClientModule.ts:136:57 - error TS1005: ',' expected.

136           console.log(`✅ [ExternalLlmClientModule] JSON 解析成功:`,
 JSON.stringify(command_json, null, 2));
                                                            ~~~~       

src/subject/ExternalLlmClientModule.ts:136:61 - error TS1005: ',' expected.

136           console.log(`✅ [ExternalLlmClientModule] JSON 解析成功:`,
 JSON.stringify(command_json, null, 2));
                                                                ~      

src/subject/ExternalLlmClientModule.ts:140:26 - error TS1127: Invalid character.

140             console.log(`✅ [ExternalLlmClientModule] 命令验证成功:`
, JSON.stringify(parsed_command.data, null, 2));
                             ~

src/subject/ExternalLlmClientModule.ts:140:54 - error TS1005: ',' expected.

140             console.log(`✅ [ExternalLlmClientModule] 命令验证成功:`
, JSON.stringify(parsed_command.data, null, 2));
                                                         ~~~~~~        

src/subject/ExternalLlmClientModule.ts:140:60 - error TS1005: ',' expected.

140             console.log(`✅ [ExternalLlmClientModule] 命令验证成功:`
, JSON.stringify(parsed_command.data, null, 2));
                                                               ~       

src/subject/ExternalLlmClientModule.ts:147:26 - error TS1127: Invalid character.

147             console.log(`📋 [ExternalLlmClientModule] 添加到执行队 
列，当前队列长度: ${context.tool_executions.length}`);
                             ~~

src/subject/ExternalLlmClientModule.ts:147:55 - error TS1005: ',' expected.

147             console.log(`📋 [ExternalLlmClientModule] 添加到执行队 
列，当前队列长度: ${context.tool_executions.length}`);
                                                          ~~~~~~~      

src/subject/ExternalLlmClientModule.ts:147:62 - error TS1127: Invalid character.

147             console.log(`📋 [ExternalLlmClientModule] 添加到执行队 
列，当前队列长度: ${context.tool_executions.length}`);
                                                                 ~     

src/subject/ExternalLlmClientModule.ts:147:69 - error TS1005: ',' expected.

147             console.log(`📋 [ExternalLlmClientModule] 添加到执行队 
列，当前队列长度: ${context.tool_executions.length}`);
    
 ~

src/subject/ExternalLlmClientModule.ts:147:72 - error TS1005: ',' expected.

147             console.log(`📋 [ExternalLlmClientModule] 添加到执行队 
列，当前队列长度: ${context.tool_executions.length}`);
    
    ~

src/subject/ExternalLlmClientModule.ts:147:80 - error TS1005: ',' expected.

147             console.log(`📋 [ExternalLlmClientModule] 添加到执行队 
列，当前队列长度: ${context.tool_executions.length}`);
    
            ~

src/subject/ExternalLlmClientModule.ts:149:28 - error TS1127: Invalid character.

149             console.error(`❌ [ExternalLlmClientModule] 命令格式验证
失败:`, parsed_command.error.message);
                               ~

src/subject/ExternalLlmClientModule.ts:149:56 - error TS1005: ',' expected.

149             console.error(`❌ [ExternalLlmClientModule] 命令格式验证
失败:`, parsed_command.error.message);
                                                           ~~~~~~~~    

src/subject/ExternalLlmClientModule.ts:149:64 - error TS1005: ',' expected.

149             console.error(`❌ [ExternalLlmClientModule] 命令格式验证
失败:`, parsed_command.error.message);
                                                                   ~   

src/subject/ExternalLlmClientModule.ts:151:16 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                   ~~~~~~~~

src/subject/ExternalLlmClientModule.ts:151:25 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                            ~~

src/subject/ExternalLlmClientModule.ts:151:28 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                               ~~~~~~~

src/subject/ExternalLlmClientModule.ts:151:36 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                       ~~~~~~~

src/subject/ExternalLlmClientModule.ts:151:44 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                               ~~~~~~

src/subject/ExternalLlmClientModule.ts:151:51 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                                      ~~~~

src/subject/ExternalLlmClientModule.ts:151:56 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                                           ~~~

src/subject/ExternalLlmClientModule.ts:151:60 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                                               ~~~     

src/subject/ExternalLlmClientModule.ts:151:63 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                                                  ~    

src/subject/ExternalLlmClientModule.ts:151:66 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
                                                                     ~ 

src/subject/ExternalLlmClientModule.ts:151:81 - error TS1005: ',' expected.

151               `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
    
             ~

src/subject/ExternalLlmClientModule.ts:156:26 - error TS1127: Invalid character.

156           console.error(`❌ [ExternalLlmClientModule] JSON 解析失败:
 ${error_message}`);
                             ~

src/subject/ExternalLlmClientModule.ts:156:54 - error TS1005: ',' expected.

156           console.error(`❌ [ExternalLlmClientModule] JSON 解析失败:
 ${error_message}`);
                                                         ~~~~

src/subject/ExternalLlmClientModule.ts:156:59 - error TS1005: ',' expected.

156           console.error(`❌ [ExternalLlmClientModule] JSON 解析失败:
 ${error_message}`);
                                                              ~~~~     

src/subject/ExternalLlmClientModule.ts:156:63 - error TS1005: ',' expected.

156           console.error(`❌ [ExternalLlmClientModule] JSON 解析失败:
 ${error_message}`);
                                                                  ~    

src/subject/ExternalLlmClientModule.ts:156:66 - error TS1005: ',' expected.

156           console.error(`❌ [ExternalLlmClientModule] JSON 解析失败:
 ${error_message}`);
                                                                     ~ 

src/subject/ExternalLlmClientModule.ts:157:26 - error TS1127: Invalid character.

157           console.error(`❌ [ExternalLlmClientModule] 响应内容预览: 
${response_content.substring(0, 200)}...`);
                             ~

src/subject/ExternalLlmClientModule.ts:157:54 - error TS1005: ',' expected.

157           console.error(`❌ [ExternalLlmClientModule] 响应内容预览: 
${response_content.substring(0, 200)}...`);
                                                         ~~~~~~        

src/subject/ExternalLlmClientModule.ts:157:60 - error TS1005: ',' expected.

157           console.error(`❌ [ExternalLlmClientModule] 响应内容预览: 
${response_content.substring(0, 200)}...`);
                                                               ~       

src/subject/ExternalLlmClientModule.ts:157:63 - error TS1005: ',' expected.

157           console.error(`❌ [ExternalLlmClientModule] 响应内容预览: 
${response_content.substring(0, 200)}...`);
                                                                  ~    

src/subject/ExternalLlmClientModule.ts:157:80 - error TS1005: ',' expected.

157           console.error(`❌ [ExternalLlmClientModule] 响应内容预览: 
${response_content.substring(0, 200)}...`);
    
            ~

src/subject/ExternalLlmClientModule.ts:157:99 - error TS1005: ',' expected.

157           console.error(`❌ [ExternalLlmClientModule] 响应内容预览: 
${response_content.substring(0, 200)}...`);
    
                               ~~~

src/subject/ExternalLlmClientModule.ts:158:48 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
                                                   ~~~~~~

src/subject/ExternalLlmClientModule.ts:158:55 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
                                                          ~~

src/subject/ExternalLlmClientModule.ts:158:58 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
                                                             ~~~~~     

src/subject/ExternalLlmClientModule.ts:158:64 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
                                                                   ~~~ 

src/subject/ExternalLlmClientModule.ts:158:68 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
~~~~~~~~

src/subject/ExternalLlmClientModule.ts:158:77 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
         ~~~~

src/subject/ExternalLlmClientModule.ts:158:84 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
                ~

src/subject/ExternalLlmClientModule.ts:158:99 - error TS1127: Invalid character.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    


src/subject/ExternalLlmClientModule.ts:158:110 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
                                          ~~~~~~~

src/subject/ExternalLlmClientModule.ts:158:117 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
                                                 ~

src/subject/ExternalLlmClientModule.ts:158:120 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
                                                    ~

src/subject/ExternalLlmClientModule.ts:158:137 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    
                                                                     ~ 

src/subject/ExternalLlmClientModule.ts:158:156 - error TS1005: ',' expected.

158           void vscode.window.showErrorMessage(`Failed to parse LLM 
response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
    

                 ~~~

src/subject/ExternalLlmClientModule.ts:161:24 - error TS1127: Invalid character.

161         console.error(`❌ [ExternalLlmClientModule] 收到空的或无效的
 LLM 响应`);
                           ~

src/subject/ExternalLlmClientModule.ts:161:52 - error TS1005: ',' expected.

161         console.error(`❌ [ExternalLlmClientModule] 收到空的或无效的
 LLM 响应`);
                                                       ~~~~~~~~        

src/subject/ExternalLlmClientModule.ts:161:61 - error TS1005: ',' expected.

161         console.error(`❌ [ExternalLlmClientModule] 收到空的或无效的
 LLM 响应`);
                                                                ~~~    

src/subject/ExternalLlmClientModule.ts:161:65 - error TS1005: ',' expected.

161         console.error(`❌ [ExternalLlmClientModule] 收到空的或无效的
 LLM 响应`);
                                                                    ~~ 

src/subject/ExternalLlmClientModule.ts:173:44 - error TS1005: ',' expected.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
                                               ~~~~~~

src/subject/ExternalLlmClientModule.ts:173:51 - error TS1005: ',' expected.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
                                                      ~~

src/subject/ExternalLlmClientModule.ts:173:54 - error TS1005: ',' expected.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
                                                         ~~~~~~~~~~~   

src/subject/ExternalLlmClientModule.ts:173:66 - error TS1005: ',' expected.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
                                                                     ~~~~

src/subject/ExternalLlmClientModule.ts:173:71 - error TS1005: '(' expected.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
    
   ~~~

src/subject/ExternalLlmClientModule.ts:173:74 - error TS1005: ')' expected.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
    
      ~

src/subject/ExternalLlmClientModule.ts:173:76 - error TS1434: Unexpected keyword or identifier.

173       void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
    
        ~

src/subject/ExternalLlmClientModule.ts:187:20 - error TS1127: Invalid character.

187       console.log(`🔐 [Spark API] 认证URL: ${auth_url}`);
                       ~~

src/subject/ExternalLlmClientModule.ts:187:30 - error TS1005: ',' expected.

187       console.log(`🔐 [Spark API] 认证URL: ${auth_url}`);
                                 ~~~

src/subject/ExternalLlmClientModule.ts:187:35 - error TS1005: ';' expected.

187       console.log(`🔐 [Spark API] 认证URL: ${auth_url}`);
                                      ~~~~~

src/subject/ExternalLlmClientModule.ts:187:42 - error TS1434: Unexpected keyword or identifier.

187       console.log(`🔐 [Spark API] 认证URL: ${auth_url}`);
                                             ~

src/subject/ExternalLlmClientModule.ts:194:22 - error TS1127: Invalid character.

194         console.log(`🔗 [Spark API] WebSocket 连接已建立`);        
                         ~~

src/subject/ExternalLlmClientModule.ts:194:32 - error TS1005: ',' expected.

194         console.log(`🔗 [Spark API] WebSocket 连接已建立`);        
                                   ~~~

src/subject/ExternalLlmClientModule.ts:194:37 - error TS1005: ';' expected.

194         console.log(`🔗 [Spark API] WebSocket 连接已建立`);        
                                        ~~~~~~~~~

src/subject/ExternalLlmClientModule.ts:194:52 - error TS1443: Module declaration names may only use ' or " quoted strings.

194         console.log(`🔗 [Spark API] WebSocket 连接已建立`);        
                                                       ~~~
195
   
...
220
   
221         console.log(`📤 [Spark API] 发送请求数据:`, JSON.stringify(request_data, null, 2));
    ~~~~~~~~~~~~~~~~~~~~~

src/subject/ExternalLlmClientModule.ts:221:22 - error TS1127: Invalid character.

221         console.log(`📤 [Spark API] 发送请求数据:`, JSON.stringify(request_data, null, 2));
                         ~~

src/subject/ExternalLlmClientModule.ts:221:22 - error TS1128: Declaration or statement expected.

221         console.log(`📤 [Spark API] 发送请求数据:`, JSON.stringify(request_data, null, 2));
                         ~~

src/subject/ExternalLlmClientModule.ts:221:32 - error TS1005: ',' expected.

221         console.log(`📤 [Spark API] 发送请求数据:`, JSON.stringify(request_data, null, 2));
                                   ~~~

src/subject/ExternalLlmClientModule.ts:221:37 - error TS1005: ';' expected.

221         console.log(`📤 [Spark API] 发送请求数据:`, JSON.stringify(request_data, null, 2));
                                        ~~~~~~

src/subject/ExternalLlmClientModule.ts:228:24 - error TS1127: Invalid character.

228           console.log(`📥 [Spark API] 收到响应:`, JSON.stringify(response, null, 2));
                           ~~

src/subject/ExternalLlmClientModule.ts:228:34 - error TS1005: ',' expected.

228           console.log(`📥 [Spark API] 收到响应:`, JSON.stringify(response, null, 2));
                                     ~~~

src/subject/ExternalLlmClientModule.ts:228:39 - error TS1005: ';' expected.

228           console.log(`📥 [Spark API] 收到响应:`, JSON.stringify(response, null, 2));
                                          ~~~~

src/subject/ExternalLlmClientModule.ts:231:28 - error TS1127: Invalid character.

231             console.error(`❌ [Spark API] 错误响应:`, response.heade
r);
                               ~

src/subject/ExternalLlmClientModule.ts:231:37 - error TS1005: ',' expected.

231             console.error(`❌ [Spark API] 错误响应:`, response.heade
r);
                                        ~~~

src/subject/ExternalLlmClientModule.ts:231:42 - error TS1005: ';' expected.

231             console.error(`❌ [Spark API] 错误响应:`, response.heade
r);
                                             ~~~~

src/subject/ExternalLlmClientModule.ts:232:31 - error TS1005: ';' expected.

232             reject(new Error(`Spark API Error: ${response.header?.message || 'Unknown error'}`));
                                  ~~~~~

src/subject/ExternalLlmClientModule.ts:232:37 - error TS1434: Unexpected keyword or identifier.

232             reject(new Error(`Spark API Error: ${response.header?.message || 'Unknown error'}`));
                                        ~~~

src/subject/ExternalLlmClientModule.ts:232:48 - error TS1434: Unexpected keyword or identifier.

232             reject(new Error(`Spark API Error: ${response.header?.message || 'Unknown error'}`));
                                                   ~

src/subject/ExternalLlmClientModule.ts:245:26 - error TS1127: Invalid character.

245             console.log(`✅ [Spark API] 响应完成，最终文本:\n${respo
nse_text}`);
                             ~

src/subject/ExternalLlmClientModule.ts:245:35 - error TS1005: ',' expected.

245             console.log(`✅ [Spark API] 响应完成，最终文本:\n${respo
nse_text}`);
                                      ~~~

src/subject/ExternalLlmClientModule.ts:245:40 - error TS1005: ';' expected.

245             console.log(`✅ [Spark API] 响应完成，最终文本:\n${respo
nse_text}`);
                                           ~~~~

src/subject/ExternalLlmClientModule.ts:245:44 - error TS1127: Invalid character.

245             console.log(`✅ [Spark API] 响应完成，最终文本:\n${respo
nse_text}`);
                                               ~

src/subject/ExternalLlmClientModule.ts:245:50 - error TS1127: Invalid character.

245             console.log(`✅ [Spark API] 响应完成，最终文本:\n${respo
nse_text}`);
    

src/subject/ExternalLlmClientModule.ts:245:51 - error TS1434: Unexpected keyword or identifier.

245             console.log(`✅ [Spark API] 响应完成，最终文本:\n${respo
nse_text}`);
                                                      ~~

src/subject/ExternalLlmClientModule.ts:250:26 - error TS1127: Invalid character.

250           console.error(`❌ [Spark API] 解析响应失败:`, error);     
                             ~

src/subject/ExternalLlmClientModule.ts:250:35 - error TS1005: ',' expected.

250           console.error(`❌ [Spark API] 解析响应失败:`, error);     
                                      ~~~

src/subject/ExternalLlmClientModule.ts:250:40 - error TS1005: ';' expected.

250           console.error(`❌ [Spark API] 解析响应失败:`, error);     
                                           ~~~~~~

src/subject/ExternalLlmClientModule.ts:256:24 - error TS1127: Invalid character.

256         console.error(`❌ [Spark API] WebSocket 错误:`, error);     
                           ~

src/subject/ExternalLlmClientModule.ts:256:33 - error TS1005: ',' expected.

256         console.error(`❌ [Spark API] WebSocket 错误:`, error);     
                                    ~~~

src/subject/ExternalLlmClientModule.ts:256:38 - error TS1005: ';' expected.

256         console.error(`❌ [Spark API] WebSocket 错误:`, error);     
                                         ~~~~~~~~~

src/subject/ExternalLlmClientModule.ts:261:22 - error TS1127: Invalid character.

261         console.log(`🔌 [Spark API] WebSocket 连接已关闭`);        
                         ~~

src/subject/ExternalLlmClientModule.ts:261:32 - error TS1005: ',' expected.

261         console.log(`🔌 [Spark API] WebSocket 连接已关闭`);        
                                   ~~~

src/subject/ExternalLlmClientModule.ts:261:37 - error TS1005: ';' expected.

261         console.log(`🔌 [Spark API] WebSocket 连接已关闭`);        
                                        ~~~~~~~~~

src/subject/ExternalLlmClientModule.ts:261:52 - error TS1443: Module declaration names may only use ' or " quoted strings.

261         console.log(`🔌 [Spark API] WebSocket 连接已关闭`);        
                                                       ~~~
262         if (response_text) {
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
...
288     // 构建签名字符串
    ~~~~~~~~~~~~~~
289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/subject/ExternalLlmClientModule.ts:289:37 - error TS1434: Unexpected keyword or identifier.

289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
                                        ~

src/subject/ExternalLlmClientModule.ts:289:44 - error TS1127: Invalid character.

289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
    

src/subject/ExternalLlmClientModule.ts:289:52 - error TS1434: Unexpected keyword or identifier.

289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
                                                       ~

src/subject/ExternalLlmClientModule.ts:289:59 - error TS1127: Invalid character.

289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
    

src/subject/ExternalLlmClientModule.ts:289:60 - error TS1435: Unknown keyword or identifier. Did you mean 'get'?

289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
                                                               ~~~~    

src/subject/ExternalLlmClientModule.ts:289:65 - error TS1434: Unexpected keyword or identifier.

289     const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
                                                                    ~  

src/subject/ExternalLlmClientModule.ts:291:35 - error TS1005: ';' expected.

291     const authorization_origin = `api_key="${api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature_sha}"`;
                                      ~~~~~~~

src/subject/ExternalLlmClientModule.ts:295:23 - error TS1005: ';' expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
                          ~

src/subject/ExternalLlmClientModule.ts:295:32 - error TS1128: Declaration or statement expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
                                   ~

src/subject/ExternalLlmClientModule.ts:295:48 - error TS1005: ';' expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
                                                   ~

src/subject/ExternalLlmClientModule.ts:295:83 - error TS1109: Expression expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
    
               ~

src/subject/ExternalLlmClientModule.ts:295:88 - error TS1005: ';' expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
    
                    ~

src/subject/ExternalLlmClientModule.ts:295:89 - error TS1434: Unexpected keyword or identifier.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
    
                     ~

src/subject/ExternalLlmClientModule.ts:295:116 - error TS1109: Expression expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
    
                                                ~

src/subject/ExternalLlmClientModule.ts:295:121 - error TS1005: ';' expected.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
    
                                                     ~

src/subject/ExternalLlmClientModule.ts:295:122 - error TS1434: Unexpected keyword or identifier.

295     const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;
    
                                                      ~

ed template literal.

300
   


Found 135 errors in the same file, starting at: src/subject/ExternalLlmClientModule.ts:128

PS C:\vscodeextensiontool>     