/**
 * @file ModuleUniverse.ts
 * @description Defines the core composition logic for the "Natural Language Programming Paradigm".
 * This file provides the tools to chain modules together in a declarative, sentence-like manner,
 * adhering to the principles outlined in the project's architectural rules.
 */

/**
 * The context that flows through the sequence, allowing modules to access shared state
 * or services without being tightly coupled.
 */
export interface ISequenceContext {
  [key: string]: unknown;
}

/**
 * Defines the standard interface for a composable module.
 * Each module is a step in a larger process, taking an input and producing an output.
 * @template TIn The type of the input parameter.
 * @template TOut The type of the output promise.
 */
export interface IComposableModule<TIn, TOut> {
  execute(input: TIn, context?: ISequenceContext): Promise<TOut>;
}

/**
 * Chains a sequence of composable modules together.
 * It executes them in order, passing the output of one module as the input to the next.
 * This function is the foundation of the declarative `sequence(...)` pattern.
 *
 * @param modules An array of modules to execute in sequence.
 * @returns A new composable module that represents the entire sequence.
 */
export function sequence<TStart, TEnd>(
  ...modules: IComposableModule<unknown, unknown>[]
): IComposableModule<TStart, TEnd> {
  return {
    async execute(initial_input: TStart, context: ISequenceContext = {}): Promise<TEnd> {
      let current_result: unknown = initial_input;

      for (const module of modules) {
        // If the previous module returned null or undefined, short-circuit the sequence.
        if (current_result === null || current_result === undefined) {
          // Short-circuiting is an expected behavior, not an error. No message needed.
          return null as TEnd;
        }
        // The type safety of the chain is ensured by the order of modules provided by the developer.
        // At this generic level, we must trust the sequence is correctly constructed.
        current_result = await module.execute(current_result, context);
      }

      // The final result is cast to the expected end type of the sequence.
      return current_result as TEnd;
    },
  };
}
