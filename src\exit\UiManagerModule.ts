// UiManagerModule.ts
// 职责：负责整体Chat面板UI的结构组织与子模块协调

import * as vscode from 'vscode';

import type { IUiManager } from '../common/moduleInterfaces.js';

/**
 * @description Header模块：顶部导航栏
 */
class HeaderView {
  render(): string {
    return `<div class="header">CHAT | + | 历史 | 设置 | 关闭</div>`;
  }
}

/**
 * @description ChatInput模块：输入区
 */
class ChatInputView {
  render(): string {
    return `<div class="chat-input">[输入框] [模型选择] [发送按钮]</div>`;
  }
}

/**
 * @description ThreadList模块：历史会话区
 */
class ThreadListView {
  render(): string {
    return `<div class="thread-list">[历史会话列表]</div>`;
  }
}

/**
 * @description 专用于管理聊天面板视图的Provider
 */
class ChatPanelViewProvider implements vscode.WebviewViewProvider {
  public readonly view_type = 'chatPanel';
  private readonly _context: vscode.ExtensionContext;
  private _view_count = 0;

  constructor(context: vscode.ExtensionContext) {
    this._context = context;
  }

  // This method must be camelCase to correctly implement the vscode.WebviewViewProvider interface.
  // eslint-disable-next-line @typescript-eslint/naming-convention
  public resolveWebviewView(
    webview_view: vscode.WebviewView,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _context: vscode.WebviewViewResolveContext, // _context is required by the interface
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _token: vscode.CancellationToken, // _token is required by the interface
  ): void {
    webview_view.webview.options = {
      // The following properties must be camelCase to align with the VS Code API.
      // eslint-disable-next-line @typescript-eslint/naming-convention
      enableScripts: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      localResourceRoots: [vscode.Uri.joinPath(this._context.extensionUri, 'media')],
    };

    webview_view.webview.html = this._get_html_for_webview(webview_view.webview);

    this._view_count++;
  }

  private _get_html_for_webview(webview: vscode.Webview): string {
    const model_display = new HeaderView().render();
    const script_path = vscode.Uri.joinPath(this._context.extensionUri, 'media', 'main.js');
    const script_uri = webview.asWebviewUri(script_path);

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Chat Panel</title>
      </head>
      <body>
        <div class="chat-panel">
          ${model_display}
          <div class="chat-input">
            <input id="userInput" type="text" placeholder="请输入内容..." />
            <button id="sendBtn">发送</button>
          </div>
          <div id="chatHistory"></div>
        </div>
        <script nonce="${this._get_nonce()}" src="${script_uri.toString(true)}"></script>
      </body>
      </html>
    `;
  }

  private _get_nonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}

/**
 * @description 主UI管理器，协调各个UI子模块
 */
export class UiManager implements IUiManager {
  private readonly _header: HeaderView;
  private readonly _chat_input: ChatInputView;
  private readonly _thread_list: ThreadListView;

  constructor() {
    this._header = new HeaderView();
    this._chat_input = new ChatInputView();
    this._thread_list = new ThreadListView();
  }

  public static register_panel(context: vscode.ExtensionContext): void {
    const provider = new ChatPanelViewProvider(context);
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(provider.view_type, provider));
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public show(_message_handler: (message: unknown) => void): void {
    // Placeholder for showing the panel
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public update(_data: unknown): void {
    // Placeholder for updating the panel
  }

  public hide(): void {
    // Placeholder for hiding the panel
  }
}
