import * as vscode from 'vscode';

import type { Tool } from '../../verb/moduleInterfaces.js';

/**
 * 注册“多功能提示词管理”命令
 * @param context VS Code 插件上下文
 * @param all_tools 工具集合
 */
export function register_prompt_manager_command(context: vscode.ExtensionContext, all_tools: Record<string, Tool>): void {
  context.subscriptions.push(
    vscode.commands.registerCommand('prompt_manager', async (params: unknown): Promise<unknown> => {
      const prompt_manager_tool = all_tools['prompt_manager'];

      if (!prompt_manager_tool) {
        void vscode.window.showErrorMessage('prompt_manager 工具未注册');
        return;
      }

      try {
        // The handler expects a record of parameters, ensure params is a valid object.
        const handler_params = typeof params === 'object' && params !== null ? params : {};
        const result: unknown = await prompt_manager_tool.handler(handler_params);
        return result;
      } catch (e: unknown) {
        const error_message = e instanceof Error ? e.message : String(e);
        void vscode.window.showErrorMessage(`prompt_manager 执行出错: ${error_message}`);
        // It's important to re-throw the error to let VS Code's command infrastructure know it failed.
        throw e;
      }
    }),
  );
}
