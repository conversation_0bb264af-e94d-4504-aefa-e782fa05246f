期望的 MCP 协议是 SSE（Server-Sent Events）或 streamableHttp
你的 llm-bridge MCP 服务只实现了普通 HTTP REST API，并没有实现 SSE 协议或 streamableHttp 协议。
Cursor 的 MCP Server 发现机制
Cursor 会尝试用 POST 请求和 SSE 方式与 MCP Server 通信。
你的服务只支持 /tools 和 /mcp 的普通 HTTP POST，不支持 Cursor 期望的 SSE/流式协议。
所以 Cursor 端会报 400（Invalid tool specified）和 404（SSE端点不存在）。
“No server info found”
说明 Cursor 没有拿到符合 MCP 协议的工具列表或服务描述。
解决方案
方案一：实现标准 MCP SSE/streamableHttp 协议
你需要让 llm-bridge 支持 Cursor 期望的 SSE 或 streamableHttp 协议（即实现官方 MCP Server 的通信规范）。
参考 modelcontextprotocol/server-examples 或 @modelcontextprotocol/server-filesystem 的实现。
这通常意味着要实现 /offerings（GET/POST）、SSE 事件推送等接口。
方案二：用官方 MCP Server 模板快速包装你的服务
推荐直接 fork 官方的 server-template 或用 @modelcontextprotocol/server-http-proxy 之类的包，把你的 HTTP 服务包装成标准 MCP Server。
方案三：仅用于自测，不在 Cursor 里用
你现在的实现适合自测和 HTTP API 调用，但无法直接被 Cursor 识别为 MCP Server，除非你补齐协议。
你现在能做什么？
如果只是自测/开发，继续用 curl/Postman 调试即可。
如果要让 Cursor 直接识别和调用你的 MCP Server，必须实现标准 MCP 协议（SSE/streamableHttp/offerings等）。
可以参考官方 MCP Server 的代码，或用 Node.js/Express/WS 等库实现 SSE。
参考资料
MCP Server 官方协议文档
官方 server-examples 仓库
@modelcontextprotocol/server-filesystem 源码
总结
你的 HTTP 服务没问题，但不符合 Cursor 期望的 MCP 协议，所以一直“Loading tools”。
需要实现标准 MCP 协议，Cursor 才能正常识别和交互。
推荐用官方模板或包装器快速适配。
如需如何把现有 Express 服务升级为标准 MCP Server
期望的 MCP Server 必须实现以下关键接口：
/offerings（GET/POST）：返回所有可用工具（tools）及其参数描述。
/invoke（POST）：接收工具调用请求，返回结果（支持流式/非流式）。
SSE/streamableHttp：支持流式响应（如有需要）。
工具描述、参数校验、错误处理等。
2. 最小可用 Node.js/Express MCP Server 示例
下面是一个最小可用的 MCP Server，兼容 Cursor，支持 /offerings 和 /invoke，可直接集成到你的 llm-bridge 项目：
import express from 'express';
import cors from 'cors';

const app = express();
app.use(cors());
app.use(express.json());

// 1. MCP offerings: 返回所有工具描述
app.get('/offerings', (req, res) => {
  res.json([
    {
      id: 'echo',
      description: 'Echo back the input',
      parameters: {
        type: 'object',
        properties: {
          text: { type: 'string', description: 'Text to echo' }
        },
        required: ['text']
      }
    },
    // 你可以在这里添加更多工具
  ]);
});

// 2. MCP invoke: 执行工具
app.post('/invoke', (req, res) => {
  const { tool, parameters } = req.body;
  if (tool === 'echo') {
    res.json({ result: parameters.text });
  } else {
    res.status(400).json({ error: 'Unknown tool' });
  }
});

// 3. 可选：实现 SSE/streamableHttp（进阶，基础功能可先不实现）

const port = 3000;
app.listen(port, () => {
  console.log(`MCP Server running at http://localhost:${port}`);
});
如何集成到你的 llm-bridge
将你的所有工具描述和参数，放到 /offerings 返回的数组中。
将你的所有工具 handler，放到 /invoke 的分支里。
把原有 /tools、/mcp 逻辑迁移到 /offerings 和 /invoke。
端口保持 3000，Cursor 配置 url 为 http://localhost:3000。
4. 进阶：支持 SSE/streamableHttp（可选）
如果你的工具有流式输出需求，可以实现 /invoke/stream 或 SSE 端点，参考 server-examples。
但基础功能只需实现 /offerings 和 /invoke 即可被 Cursor 识别和调用。
5. 参考官方实现
官方 server-examples（TypeScript）
@modelcontextprotocol/server-filesystem 源码
6. 迁移建议
先实现 /offerings 和 /invoke，确保 Cursor 能识别和调用。
再逐步迁移你的所有工具和参数校验逻辑。
如需流式输出，再补充 SSE/streamableHttp。