import websocket
import json
import time
import hashlib
import hmac
import base64
import urllib.parse

# 读取配置
with open("config/xfyun_spark_config.json", "r", encoding="utf-8") as f:
    config = json.load(f)

appid = config["appid"]
api_key = config["api_key"]
api_secret = config["api_secret"]
domain = config["domain"]
ws_url_base = config["ws_url"]

def get_ws_url(appid, api_key, api_secret, ws_url_base):
    host = ws_url_base.split("/")[2]
    path = "/" + "/".join(ws_url_base.split("/")[3:])
    now = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
    signature_origin = f"host: {host}\ndate: {now}\nGET {path} HTTP/1.1"
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'), hashlib.sha256).digest()
    signature = base64.b64encode(signature_sha).decode('utf-8')
    authorization_origin = f'api_key="{api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature}"'
    authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode('utf-8')
    params = {
        "authorization": authorization,
        "date": now,
        "host": host
    }
    ws_url = ws_url_base + '?' + urllib.parse.urlencode(params)
    return ws_url

def on_message(ws, message):
    print("收到消息:", message)

def on_error(ws, error):
    print("发生错误:", error)

def on_close(ws, close_status_code, close_msg):
    print("连接关闭")

def on_open(ws):
    data = {
        "header": {
            "app_id": appid,
            "uid": "user001"
        },
        "parameter": {
            "chat": {
                "domain": domain,
                "temperature": 0.8,
                "max_tokens": 4096,
                "presence_penalty": 1,
                "frequency_penalty": 0.02,
                "top_k": 5,
                "tools": [
                    {
                        "type": "web_search",
                        "web_search": {
                            "enable": True,
                            "search_mode": "normal"
                        }
                    }
                ]
            }
        },
        "payload": {
            "message": {
                "text": [
                    {"role": "user", "content": "你好，讯飞星火X1"}
                ]
            }
        }
    }
    ws.send(json.dumps(data))

if __name__ == "__main__":
    ws_url = get_ws_url(appid, api_key, api_secret, ws_url_base)
    ws = websocket.WebSocketApp(ws_url,
                                on_open=on_open,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close)
    ws.run_forever()