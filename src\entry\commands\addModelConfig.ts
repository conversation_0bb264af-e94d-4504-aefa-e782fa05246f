import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';

/**
 * 添加新的模型配置
 * @param context VS Code 插件上下文
 */
export async function add_new_model_config(context: vscode.ExtensionContext): Promise<void> {
  try {
    // 1. 获取用户输入的模型名称
    const model_name = await vscode.window.showInputBox({
      prompt: '请输入新模型的名称（如：claude、gpt4等）',
      placeHolder: '模型名称',
      validateInput: (value: string) => {
        if (!value || value.trim().length === 0) {
          return '模型名称不能为空';
        }
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value.trim())) {
          return '模型名称只能包含字母、数字和下划线，且必须以字母开头';
        }
        return null;
      },
    });

    if (!model_name) {
      return; // 用户取消了输入
    }

    const clean_model_name = model_name.trim().toLowerCase();

    // 2. 选择API类型
    const api_type = await vscode.window.showQuickPick(
      [
        { label: 'mistral', description: 'Mistral AI API' },
        { label: 'spark', description: '讯飞星火API' },
        { label: 'openai', description: 'OpenAI Compatible API' },
      ],
      {
        placeHolder: '选择API类型',
        canPickMany: false,
      },
    );

    if (!api_type) {
      return; // 用户取消了选择
    }

    // 3. 生成配置文件模板
    const config_template = generate_config_template(clean_model_name, api_type.label);
    const config_file_name = `${clean_model_name}_config.json`;
    const config_file_path = path.join(context.extensionPath, 'config', config_file_name);

    // 4. 检查文件是否已存在
    if (fs.existsSync(config_file_path)) {
      const overwrite = await vscode.window.showWarningMessage(
        `配置文件 ${config_file_name} 已存在，是否覆盖？`,
        '覆盖',
        '取消',
      );
      if (overwrite !== '覆盖') {
        return;
      }
    }

    // 5. 创建配置文件
    fs.writeFileSync(config_file_path, JSON.stringify(config_template, null, 2), 'utf-8');

    // 6. 更新映射关系
    await update_view_type_mapping(context, clean_model_name);

    // 7. 更新package.json
    await update_package_json(context, clean_model_name);

    // 8. 提示用户
    const open_config = await vscode.window.showInformationMessage(
      `模型配置 ${clean_model_name} 已创建！请配置API密钥等信息后重新加载扩展。`,
      '打开配置文件',
      '重新加载扩展',
    );

    if (open_config === '打开配置文件') {
      const config_uri = vscode.Uri.file(config_file_path);
      await vscode.window.showTextDocument(config_uri);
    } else if (open_config === '重新加载扩展') {
      await vscode.commands.executeCommand('workbench.action.reloadWindow');
    }
  } catch (error) {
    const error_message = error instanceof Error ? error.message : String(error);
    void vscode.window.showErrorMessage(`添加配置失败: ${error_message}`);
  }
}

/**
 * 生成配置文件模板
 */
function generate_config_template(model_name: string, api_type: string): object {
  const base_config = {
    id: model_name,
    name: model_name.charAt(0).toUpperCase() + model_name.slice(1),
    api_type: api_type,
  };

  switch (api_type) {
    case 'mistral':
      return {
        ...base_config,
        api_key: 'YOUR_MISTRAL_API_KEY',
        base_url: 'https://api.mistral.ai/v1/chat/completions',
        model: 'mistral-large-latest',
        endpoints: {
          chat: 'https://api.mistral.ai/v1/chat/completions',
          embedding: 'https://api.mistral.ai/v1/embeddings',
          fim: 'https://api.mistral.ai/v1/fim/completions',
          moderation: 'https://api.mistral.ai/v1/moderations',
        },
        models: {
          chat: 'mistral-large-latest',
          embedding: 'mistral-embed',
          fim: 'codestral-latest',
        },
      };

    case 'spark':
      return {
        ...base_config,
        appid: 'YOUR_SPARK_APPID',
        api_key: 'YOUR_SPARK_API_KEY',
        api_secret: 'YOUR_SPARK_API_SECRET',
        domain: 'x1',
        ws_url: 'wss://spark-api.xf-yun.com/v1/x1',
      };

    case 'openai':
      return {
        ...base_config,
        api_key: 'YOUR_OPENAI_API_KEY',
        base_url: 'https://api.openai.com/v1/chat/completions',
        model: 'gpt-4',
        organization: 'YOUR_ORGANIZATION_ID', // 可选
      };

    default:
      return base_config;
  }
}

/**
 * 更新ChatPanelProvider中的viewType映射
 */
async function update_view_type_mapping(context: vscode.ExtensionContext, model_name: string): Promise<void> {
  const provider_file_path = path.join(context.extensionPath, 'src', 'exit', 'ChatPanelProvider.ts');
  
  if (!fs.existsSync(provider_file_path)) {
    throw new Error('ChatPanelProvider.ts 文件不存在');
  }

  let content = fs.readFileSync(provider_file_path, 'utf-8');
  
  // 查找映射对象的位置
  const mapping_regex = /(const view_type_to_model_id_map: Record<string, string> = \{[^}]*)\}/;
  const match = content.match(mapping_regex);
  
  if (!match) {
    throw new Error('未找到 view_type_to_model_id_map 映射对象');
  }

  // 添加新的映射条目
  const new_mapping_entry = `      'chatPanel-${model_name}': '${model_name}',`;
  const updated_mapping = match[1] + '\n' + new_mapping_entry + '\n    }';
  
  content = content.replace(mapping_regex, updated_mapping);
  
  fs.writeFileSync(provider_file_path, content, 'utf-8');
}

/**
 * 更新package.json添加新的webview配置
 */
async function update_package_json(context: vscode.ExtensionContext, model_name: string): Promise<void> {
  const package_json_path = path.join(context.extensionPath, 'package.json');
  
  if (!fs.existsSync(package_json_path)) {
    throw new Error('package.json 文件不存在');
  }

  const package_json = JSON.parse(fs.readFileSync(package_json_path, 'utf-8'));
  
  // 添加新的webview配置
  const new_webview_config = {
    id: `chatPanel-${model_name}`,
    name: model_name.toUpperCase(),
    icon: 'media/icon.svg',
    type: 'webview',
  };

  if (!package_json.contributes) {
    package_json.contributes = {};
  }
  if (!package_json.contributes.views) {
    package_json.contributes.views = {};
  }
  if (!package_json.contributes.views['llm-bridge-container']) {
    package_json.contributes.views['llm-bridge-container'] = [];
  }

  // 检查是否已存在相同配置
  const existing_config = package_json.contributes.views['llm-bridge-container'].find(
    (config: any) => config.id === new_webview_config.id,
  );

  if (!existing_config) {
    package_json.contributes.views['llm-bridge-container'].push(new_webview_config);
  }

  fs.writeFileSync(package_json_path, JSON.stringify(package_json, null, 2), 'utf-8');
}
