# MCP协议深度复盘与未来展望

## 1. MCP 的哲学：万物皆资源 (Everything as a Resource)

Model Context Protocol (MCP) 的核心哲学思想，是将开发工具中的所有可交互组件、数据和服务，统一抽象为符合REST风格的“资源”。

一个资源可以是一个文件、一个函数定义、一个正在运行的调试会话、一个Linter的诊断信息，甚至是一个可以被执行的“动作”（如我们的 `tool:///showMessage`）。

这种抽象带来了巨大的好处：

- **统一的交互模型**: 无论资源的内在形式如何，客户端都通过一套统一的动词（如 `read`, `write`, `list`）与之交互。这极大地降低了客户端的复杂性。
- **解耦与可扩展性**: 服务器（如我们的插件）可以独立地增加、删除或修改其提供的资源，而无需改动客户端代码。这使得工具的功能可以被轻松地扩展。
- **语言无关性**: 因为交互是基于标准协议（JSON-RPC over a transport），客户端和服务器可以用任何语言实现，只要它们都遵循MCP规范。

## 2. 三大支柱：资源、传输与JSON-RPC

### a. 资源 (Resources)
- **URI**: 每个资源都由一个唯一的URI（统一资源标识符）来定位，例如 `file:///path/to/file.ts` 或我们自定义的 `tool:///showMessage`。
- **能力 (Capabilities)**: 资源通过元数据声明自己支持哪些操作（`readable`, `writeable`, `listable` 等）。客户端在交互前可以通过 `resources/list` 发现这些能力。

### b. 传输 (Transports)
- **可插拔的通信层**: MCP 的美妙之处在于其传输层是完全可插拔的。协议本身不关心数据包是“如何”从A点到B点的。
- **常见的传输方式**:
    - **Stdio**: 用于本地父子进程间通信。**这是VS Code插件等本地IPC场景的最佳选择**，稳定、高效、安全。
    - **HTTP/1.1 + SSE**: 用于Web客户端（如浏览器）和服务器之间的通信。SSE (Server-Sent Events) 允许服务器向客户端推送实时更新。然而，正如我们所经历的，其实现可能存在兼容性问题。
    - **WebSockets**: 另一种双向实时通信方案，相比SSE，它提供真正的全双工通信。
    - **IPC Sockets**: 用于同一台机器上不同进程间的通信，比Stdio更通用。

### c. JSON-RPC 2.0
- **通信语言**: MCP选择JSON-RPC作为其请求和响应的格式。这是一个轻量级的远程过程调用（RPC）协议。
- **核心方法**: MCP预定义了几个核心的JSON-RPC方法，如 `resources/list`, `resources/read`, `resources/write`。所有交互都建立在这些方法之上。

## 3. 项目历程复盘：一个宝贵的实战案例

我们的项目经历完整地展示了理论与实践的碰撞，最终回归到“选择最适合场景的工具”这一工程学基本原则上。

1.  **初始阶段 (HTTP/SSE)**: 我们选择了理论上最灵活的HTTP/SSE方案，希望兼顾Node.js和潜在的浏览器客户端。
2.  **遭遇现实 (SDK Bug)**: 我们遇到了官方SDK在 `StreamableHTTPServerTransport` 中存在的底层Bug。这提醒我们，即使是官方库，在某些非核心或较新的模块上也可能存在不稳定性。
3.  **“战术性”修复 (Surgical Fixes)**: 我们通过注入SSE心跳、手动构造JSON-RPC响应等方式，绕过了Bug。这展示了深入理解协议底层（了解SSE协议格式、JSON-RPC结构）对于解决疑难杂症的关键作用。
4.  **“战略性”重构 (Refactoring to Stdio)**: 我们最终意识到，对于VS Code插件的场景，HTTP的复杂性是“杀鸡用牛刀”。我们果断放弃了不适合当前场景的技术，回归到最简单、最稳定的Stdio方案，所有问题迎刃而解。

**核心教训**: **不要为了追求理论上的“完全兼容”或“未来可能”，而选择一个在当前核心场景下过于复杂或不稳定的技术栈。优先保证核心功能的稳定与简洁。**

## 4. 未来展望与开发建议

基于本次项目的经验，我们为未来基于MCP的开发工作提出以下建议：

- **场景优先原则**: 在选择传输层时，首先明确你的核心通信场景。是本地IPC？还是需要跨网络访问？
    - **VS Code 插件**: **始终首选 `StdioServerTransport` 和 `StdioClientTransport`**。
    - **需要Web界面的应用**: 考虑使用HTTP/SSE或WebSockets，但在选择前，要对所用SDK的稳定性进行充分验证，或考虑寻找更成熟的替代方案。
- **深入理解协议**: 不要将SDK视为黑盒。花时间阅读MCP官方规范和JSON-RPC规范。这种知识能在你遇到问题时，给予你绕过问题或从根本上解决问题的能力。
- **拥抱简单**: 在软件工程中，简单几乎总是等同于可靠。我们移除近百行网络处理代码的重构过程，是这一原则的最佳体现。
- **持续学习与跟进**: 关注MCP社区和SDK的更新。我们遇到的Bug可能在未来的版本中被修复。保持对技术生态的关注是专业开发者的必备素养。

这份文档记录了我们从实践到理论再回到实践的完整认知闭环，希望能为团队未来的项目提供坚实的理论基础和宝贵的实战经验。
