import globals from 'globals';
import tseslint from 'typescript-eslint';
import prettier from 'eslint-config-prettier';
import promisePlugin from 'eslint-plugin-promise';
import unicornPlugin from 'eslint-plugin-unicorn';
import simpleImportSort from 'eslint-plugin-simple-import-sort';
import importPlugin from 'eslint-plugin-import';

export default tseslint.config(
  {
    // Global ignores
    ignores: ['node_modules/**', 'dist/**', 'media/**'],
  },
  // Base configurations for all files
  {
    plugins: {
      'simple-import-sort': simpleImportSort,
      import: importPlugin,
      promise: promisePlugin,
      unicorn: unicornPlugin,
    },
    rules: {
      'simple-import-sort/imports': 'error',
      'simple-import-sort/exports': 'error',
      'import/first': 'error',
      'import/newline-after-import': 'error',
    },
  },
  // TypeScript specific configurations
  ...tseslint.configs.recommendedTypeChecked.map(config => ({
    ...config,
    files: ['**/*.ts', '**/*.tsx'],
  })),
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
      },
      globals: {
        ...globals.node,
        ...globals.es2021,
      },
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/consistent-type-imports': 'error',
      '@typescript-eslint/explicit-function-return-type': 'error',
      '@typescript-eslint/no-floating-promises': 'error',
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'default',
          format: ['snake_case'],
          leading_underscore: 'allow',
          trailing_underscore: 'allow',
        },
        {
          selector: 'variable',
          format: ['snake_case', 'UPPER_CASE', 'PascalCase'],
          leading_underscore: 'allow',
          trailing_underscore: 'allow',
        },
        {
          selector: 'variable',
          modifiers: ['const'],
          format: ['snake_case', 'UPPER_CASE', 'PascalCase'],
        },
        {
          selector: 'function',
          format: ['snake_case'],
        },
        {
          selector: 'typeLike',
          format: ['PascalCase'],
        },
        {
          selector: 'enumMember',
          format: ['UPPER_CASE'],
        },
        {
          selector: 'property',
          modifiers: ['requiresQuotes'],
          format: null,
        },
      ],
    },
  },
  // Unicorn and Promise recommended rules
  promisePlugin.configs['recommended'],
  unicornPlugin.configs['recommended'],
  // Prettier must be last
  prettier,
  // Overrides for specific files
  {
    files: ['*.js', '*.cjs'],
    ...tseslint.configs.disableTypeChecked,
    rules: {
      '@typescript-eslint/no-var-requires': 'off',
    },
  },
  {
    files: ['**/*.test.ts', '**/*.spec.ts'],
    rules: {
      'no-console': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
    },
  },
);
