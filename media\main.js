// This script will run in the webview

// It is a best practice to leave the VS Code API acquisition in a try-catch block,
// as the acquireVsCodeApi function will not be defined when running the webview in a browser.
(function () {
  // The acquireVsCodeApi function is exposed by VS Code in the webview context.
  // It can be used to communicate with the extension.
  const vscode = acquireVsCodeApi();

  const old_state = vscode.getState() || { count: 0 };

  let counter = old_state.count;

  // Log to the console to confirm the script is running.
  console.log('main.js script loaded successfully!');

  // Send a message to the extension to confirm that the script has loaded.
  vscode.postMessage({
    command: 'webview-ready',
    text: 'The webview is ready and the script has loaded.',
  });

  // Example of handling messages from the extension
  window.addEventListener('message', event => {
    const message = event.data; // The JSON data that the extension sent
    switch (message.command) {
      case 'update-counter':
        counter = message.value;
        // Update the state
        vscode.setState({ count: counter });
        break;
    }
  });
})();
