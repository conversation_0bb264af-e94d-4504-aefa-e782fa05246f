const vscode = require('vscode');

async function testCommandCapture() {
  console.log('=== VS Code 命令捕获测试 ===');

  try {
    // 测试1: 获取所有命令（包括插件命令）
    console.log('\n1. 获取所有可发现的命令...');
    const allCommands = await vscode.commands.getCommands(true);
    console.log(`总命令数量: ${allCommands.length}`);

    // 分析命令来源
    const builtinCommands = allCommands.filter(
      cmd =>
        cmd.startsWith('workbench.') ||
        cmd.startsWith('editor.') ||
        cmd.startsWith('debug.') ||
        cmd.startsWith('git.') ||
        cmd.startsWith('extension.'),
    );

    const extensionCommands = allCommands.filter(
      cmd =>
        cmd.includes('.') &&
        !cmd.startsWith('workbench.') &&
        !cmd.startsWith('editor.') &&
        !cmd.startsWith('debug.') &&
        !cmd.startsWith('git.'),
    );

    console.log(`内置命令数量: ${builtinCommands.length}`);
    console.log(`插件命令数量: ${extensionCommands.length}`);

    // 显示一些插件命令示例
    console.log('\n2. 插件命令示例:');
    extensionCommands.slice(0, 20).forEach(cmd => {
      console.log(`  - ${cmd}`);
    });

    if (extensionCommands.length > 20) {
      console.log(`  ... 还有 ${extensionCommands.length - 20} 个插件命令`);
    }

    // 测试3: 按插件分组
    console.log('\n3. 按插件分组的命令统计:');
    const pluginGroups = new Map();

    extensionCommands.forEach(cmd => {
      const parts = cmd.split('.');
      if (parts.length >= 2) {
        const pluginName = parts[0];
        if (!pluginGroups.has(pluginName)) {
          pluginGroups.set(pluginName, []);
        }
        pluginGroups.get(pluginName).push(cmd);
      }
    });

    // 显示插件统计
    const sortedPlugins = Array.from(pluginGroups.entries()).sort((a, b) => b[1].length - a[1].length);

    sortedPlugins.slice(0, 10).forEach(([plugin, commands]) => {
      console.log(`  ${plugin}: ${commands.length} 个命令`);
    });

    // 测试4: 检查特定插件的命令
    console.log('\n4. 检查常见插件的命令:');
    const commonPlugins = ['eslint', 'prettier', 'git', 'typescript', 'python', 'java', 'cpp'];

    commonPlugins.forEach(plugin => {
      const pluginCommands = allCommands.filter(cmd => cmd.startsWith(`${plugin}.`));
      if (pluginCommands.length > 0) {
        console.log(`  ${plugin}: ${pluginCommands.length} 个命令`);
        pluginCommands.slice(0, 3).forEach(cmd => {
          console.log(`    - ${cmd}`);
        });
      }
    });

    // 测试5: 验证命令是否可执行
    console.log('\n5. 验证命令可执行性测试:');
    const testCommands = [
      'workbench.action.showCommands',
      'editor.action.formatDocument',
      'workbench.action.files.save',
    ];

    for (const cmd of testCommands) {
      try {
        // 注意：这里只是检查命令是否存在，不实际执行
        const exists = allCommands.includes(cmd);
        console.log(`  ${cmd}: ${exists ? '✓ 存在' : '✗ 不存在'}`);
      } catch (error) {
        console.log(`  ${cmd}: ✗ 错误 - ${error.message}`);
      }
    }
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('注意: 此脚本需要在VS Code扩展环境中运行');
  console.log('请在VS Code中打开开发者工具，然后运行此脚本');
}

module.exports = { testCommandCapture };
