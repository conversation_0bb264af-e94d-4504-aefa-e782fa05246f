# 📚 配置系统使用指南

## 🎯 快速开始

### 1. 添加新的模型配置

#### 方法一：使用命令面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `LLM Bridge: Add Configuration`
3. 选择API类型（Mistral/Spark/OpenAI）
4. 输入模型ID（如：`my-gpt4`）
5. 系统自动生成配置模板
6. 选择是否立即编辑配置文件

#### 方法二：手动创建配置文件
1. 在 `config/` 目录下创建 `{model_id}_config.json` 文件
2. 复制以下模板并修改：

```json
{
  "id": "my-model",
  "name": "My Custom Model",
  "description": "我的自定义模型配置",
  "version": "1.0.0",
  "api_type": "mistral",
  "capabilities": {
    "max_tokens": 4096,
    "supports_streaming": true,
    "supports_function_calling": true,
    "supports_vision": false,
    "supports_code_execution": true
  },
  "available_tools": [
    {
      "name": "reply_to_user",
      "description": "回复用户消息",
      "parameters": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "要发送给用户的消息"
          }
        },
        "required": ["message"]
      }
    }
  ],
  "prompt_templates": {
    "system_prompt": "你是一个智能的VS Code助手。",
    "user_prompt_template": "用户请求：{{user_request}}",
    "tool_instruction_template": "可用工具：{{available_tools}}",
    "context_template": "当前上下文：{{workspace_root}}"
  },
  "ui_config": {
    "panel_title": "My Model",
    "panel_icon": "robot",
    "theme_color": "#007ACC",
    "welcome_message": "你好！我是你的AI助手。"
  },
  "api_config": {
    "timeout": 30000,
    "api_key": "YOUR_API_KEY",
    "base_url": "https://api.example.com/v1/chat/completions",
    "model": "model-name"
  }
}
```

### 2. 管理现有配置

#### 打开设置管理器
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `LLM Bridge: Open Settings`
3. 选择管理类型：
   - **配置管理**：查看、编辑、删除模型配置
   - **工具设置**：管理命令白名单和工具权限
   - **提示词模板**：自定义AI助手行为
   - **导入/导出**：备份和分享配置
   - **系统信息**：查看插件状态和诊断信息
   - **配置验证**：检查配置完整性和正确性

## 🔧 配置详解

### API配置类型

#### Mistral API
```json
"api_config": {
  "timeout": 30000,
  "api_key": "YOUR_MISTRAL_API_KEY",
  "base_url": "https://api.mistral.ai/v1/chat/completions",
  "model": "mistral-large-latest"
}
```

#### Spark API (讯飞星火)
```json
"api_config": {
  "timeout": 30000,
  "api_key": "YOUR_SPARK_API_KEY",
  "appid": "YOUR_SPARK_APPID",
  "api_secret": "YOUR_SPARK_API_SECRET",
  "domain": "generalv3.5",
  "ws_url": "wss://spark-api.xf-yun.com/v3.5/chat"
}
```

#### OpenAI API
```json
"api_config": {
  "timeout": 30000,
  "api_key": "YOUR_OPENAI_API_KEY",
  "base_url": "https://api.openai.com/v1/chat/completions",
  "model": "gpt-4",
  "organization": "YOUR_ORG_ID"
}
```

### 能力配置说明

```json
"capabilities": {
  "max_tokens": 4096,              // 最大token数
  "supports_streaming": true,       // 是否支持流式输出
  "supports_function_calling": true, // 是否支持函数调用
  "supports_vision": false,         // 是否支持视觉理解
  "supports_code_execution": true   // 是否支持代码执行
}
```

### 工具配置格式

```json
{
  "name": "tool_name",
  "description": "工具描述",
  "parameters": {
    "type": "object",
    "properties": {
      "param1": {
        "type": "string",
        "description": "参数描述"
      }
    },
    "required": ["param1"]
  }
}
```

### 提示词模板变量

可在提示词模板中使用以下变量：

- `{{user_request}}` - 用户请求内容
- `{{workspace_root}}` - 工作区根目录
- `{{active_file_path}}` - 当前活动文件路径
- `{{selected_text}}` - 选中的文本内容
- `{{open_files}}` - 打开的文件列表
- `{{available_tools}}` - 可用工具列表

## 🔍 配置验证

### 自动验证
- 配置文件保存时自动验证
- 扩展启动时验证所有配置
- 配置错误时显示详细错误信息

### 手动验证
1. 打开设置管理器
2. 选择"配置验证"
3. 查看详细验证报告
4. 根据建议修复问题

### 常见验证错误

#### API密钥未配置
```
❌ Mistral API密钥未配置
💡 请在api_config.api_key中设置有效的API密钥
```

#### URL格式错误
```
❌ API地址无效
💡 请检查base_url格式是否正确
```

#### 工具定义错误
```
❌ 工具名称重复: reply_to_user
💡 请确保每个工具名称唯一
```

## 🎨 UI自定义

### 面板图标
支持的图标类型：
- VS Code内置图标：`robot`, `gear`, `comment`, `tools`等
- 自定义图标：使用相对路径指向图标文件

### 主题颜色
使用十六进制颜色代码：
- `#007ACC` - VS Code蓝
- `#FF6B6B` - 红色
- `#4ECDC4` - 青色
- `#45B7D1` - 天蓝色

## 🔄 配置热重载

配置文件修改后会自动重载，无需重启VS Code：

1. 修改配置文件
2. 保存文件
3. 系统自动检测变更
4. 重新加载配置
5. UI自动更新

## 🚨 故障排除

### 配置不生效
1. 检查配置文件语法是否正确
2. 确认文件名格式为 `{id}_config.json`
3. 查看VS Code开发者控制台的错误信息
4. 运行配置验证检查问题

### API调用失败
1. 验证API密钥是否正确
2. 检查网络连接
3. 确认API地址是否可访问
4. 查看API配置是否匹配服务商要求

### 工具无法使用
1. 检查工具定义格式
2. 确认工具参数配置正确
3. 验证命令白名单设置
4. 查看工具权限配置

## 📞 获取帮助

如果遇到问题：

1. 查看配置验证报告
2. 检查VS Code开发者控制台
3. 参考示例配置文件
4. 使用系统信息功能诊断问题

## 🎉 最佳实践

1. **配置命名**：使用有意义的ID和名称
2. **版本管理**：使用语义化版本号
3. **安全性**：不要在配置中硬编码敏感信息
4. **备份**：定期导出配置进行备份
5. **测试**：添加新配置后进行验证测试
