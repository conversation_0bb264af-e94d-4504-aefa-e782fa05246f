import * as path from 'path';
import * as vscode from 'vscode';
import { z } from 'zod';

import type { Command, CommandStorageModule } from '../../object/CommandStorageModule.js';

// Define a Zod schema for the command contribution object from package.json.
const package_command_schema = z.object({
  command: z.string(),
});

// Define a Zod schema for the relevant parts of package.json.
const package_json_schema = z.object({
  contributes: z
    .object({
      commands: z.array(package_command_schema).optional(),
    })
    .optional(),
});

/**
 * 自动同步 VS Code 命令，将新发现的命令添加到存储中，同时过滤掉黑名单和自身命令。
 * @param context VS Code 插件上下文
 * @param command_storage 命令存储模块
 * @param core_commands 核心命令，确保它们始终存在于白名单中
 */
export async function auto_sync_commands(
  context: vscode.ExtensionContext,
  command_storage: CommandStorageModule,
  core_commands: string[],
): Promise<void> {
  try {
    // 1. 获取扩展自身的命令，以便在同步时忽略它们
    const package_json_uri = vscode.Uri.file(path.join(context.extensionPath, 'package.json'));
    const package_json_content = await vscode.workspace.fs.readFile(package_json_uri);
    const package_json_data: unknown = JSON.parse(Buffer.from(package_json_content).toString('utf8'));

    const parsed_package_json = package_json_schema.safeParse(package_json_data);

    if (!parsed_package_json.success) {
      void vscode.window.showWarningMessage('无法解析 package.json 中的命令。');
      return;
    }

    const self_commands = new Set<string>(parsed_package_json.data.contributes?.commands?.map(c => c.command) || []);

    // 2. 获取所有已知的 VS Code 命令，并过滤掉本扩展自身的命令
    const all_command_ids = await vscode.commands.getCommands(true);
    const filtered_command_ids = all_command_ids.filter(id => !self_commands.has(id));

    // 3. 将过滤后的命令 ID 转换为 Command 对象
    const all_commands: Command[] = filtered_command_ids.map(id => ({
      id,
      command: id,
      is_builtin: true,
    }));

    // 4. 保存所有发现的命令
    await command_storage.save_commands(all_commands);

    // 5. 清理白名单，移除不再存在的命令，并确保核心命令始终存在
    const whitelist_ids = command_storage.get_whitelist_ids();
    const all_command_id_set = new Set(all_command_ids);
    const cleaned_whitelist_ids = whitelist_ids.filter((id: string) => all_command_id_set.has(id));

    const final_whitelist_set = new Set(cleaned_whitelist_ids);
    for (const core_cmd_id of core_commands) {
      final_whitelist_set.add(core_cmd_id);
    }

    await command_storage.save_whitelist(Array.from(final_whitelist_set));

    void vscode.window.showInformationMessage(`命令同步完成，当前白名单数量: ${final_whitelist_set.size}。`);
  } catch (error: unknown) {
    const error_message = error instanceof Error ? error.message : String(error);
    void vscode.window.showWarningMessage(`自动同步命令失败: ${error_message}`);
  }
}
