# MCP over HTTP/SSE 实现方案归档

**状态**: 已废弃  
**废弃原因**: 在 VS Code 插件这种嵌入式环境中，HTTP/SSE 方案引入了大量网络相关的复杂性和不稳定性，最终被更简单、更可靠的 `Stdio` 方案取代。详见 `mcp_http_sse_troubleshooting.md`。  
**归档目的**: 本文档旨在作为一份历史记录，详细描述最初基于 HTTP/SSE 的 MCP 实现方案的架构与技术细节，为未来的技术选型和架构设计提供参考。

---

## 1. 核心架构

该方案采用了经典的客户端-服务器模型，其核心组件和交互流程如下：

- **服务器**: 一个独立的 Node.js 进程，使用 `Express.js` 框架搭建一个轻量级的 HTTP 服务器。
- **客户端**: VS Code 扩展的主进程。
- **通信协议**:
  - **RPC 调用**: 客户端通过标准的 HTTP `POST` 请求向服务器发送 JSON-RPC 消息 (例如 `resources/list`)。
  - **服务器推送**: 客户端通过 HTTP `GET` 请求与服务器建立一个 Server-Sent Events (SSE) 长连接，用于接收服务器主动推送的事件 (例如 `resources/read` 触发的 `showMessage` 工具调用)。
- **服务发现**:
  1. 服务器启动时，会寻找一个可用的端口。
  2. 成功监听后，服务器会将所选端口号写入到一个约定好的临时文件中 (例如 `~/.llm-bridge/mcp.port`)。
  3. 客户端启动时，会读取这个临时文件来获取服务器的地址和端口，从而知道向哪里发送请求。

## 2. 服务端实现 (`src/mcp/server.ts` - 旧版)

### 2.1. 依赖

- `express`: 用于快速搭建 HTTP 服务器和路由。
- `cors`: 处理跨域请求。
- `@modelcontextprotocol/sdk`: MCP 协议的核心库，使用 `StreamableHTTPServerTransport`。
- `node:fs`, `node:path`, `node:net`: 用于处理文件系统、路径和网络端口检查。

### 2.2. 启动流程

1.  **寻找可用端口**: 实现一个 `findFreePort` 函数，通过尝试绑定端口来探测一个未被占用的端口。
2.  **创建 Express 应用**: `const app = express();`
3.  **配置中间件**: 使用 `cors()` 和 `express.json()` 来处理跨域和 JSON 请求体。
4.  **创建 MCP Transport**: `const transport = new StreamableHTTPServerTransport(app);`
5.  **创建 MCP Server**: `const mcpServer = new Server(...)`，配置服务器元数据和能力。
6.  **注册处理器**: 实现 `ListResourcesRequestSchema` 和 `ReadResourceRequestSchema` 的处理器。
7.  **连接 Server 与 Transport**: `mcpServer.connect(transport);`
8.  **启动 HTTP 服务器**: `const httpServer = app.listen(port, () => { ... });`
9.  **写入端口文件**: 服务器成功启动后，将 `port` 写入到 `~/.llm-bridge/mcp.port` 文件中。
10. **管理 Socket 连接**: 为了实现优雅关闭，需要手动追踪所有活跃的 HTTP socket 连接，并在关闭服务器时销毁它们。

## 3. 客户端实现 (`src/extension.ts` - 旧版)

### 3.1. 依赖

- `node:child_process`: 使用 `spawn` 来在后台启动服务器进程。
- `node-fetch` / `axios`: 用于发送 HTTP `POST` 请求。
- `eventsource`: 一个健壮的 SSE 客户端库，用于处理 SSE 连接。

### 3.2. 启动与连接流程

1.  **启动服务器子进程**: `spawn('node', ['path/to/server.js'])`。
2.  **读取端口文件**: 轮询 `~/.llm-bridge/mcp.port` 文件，直到读取到有效的端口号。
3.  **创建 MCP Client**: `const mcpClient = new Client(...)`。
4.  **连接 Transport**:
    - **HTTP 部分**: 使用 `node-fetch` 构造一个函数，向 `http://localhost:${port}` 发送 `POST` 请求。
    - **SSE 部分**: 使用 `EventSource` 库连接到 `http://localhost:${port}` 的 SSE 端点。
    - 将这两个部分组合成一个符合 MCP SDK 要求的 `transport` 对象，并传递给 `mcpClient.connect(transport)`。

## 4. 最终评估

尽管该方案采用了广泛使用的 Web 技术，但在 VS Code 插件这个特定的嵌入式场景下，暴露了诸多问题：

- **复杂性**: 端口管理、服务发现、Socket 管理、HTTP 与 SSE 的分离处理等，都带来了不必要的复杂性。
- **不稳定性**: 端口冲突、防火墙问题、依赖库之间的兼容性问题 (如 `express` 与 SDK transport 的底层冲突) 导致了大量难以调试的 Bug。
- **资源开销**: 相比于直接的 Stdio，网络堆栈带来了额外的性能和资源开销。

**结论**: 对于需要本地、可靠、高性能通信的父子进程模型（如 VS Code 扩展与后台服务），`Stdio` 是一种远比 `HTTP` 更简单、更健壮、更优雅的解决方案。
