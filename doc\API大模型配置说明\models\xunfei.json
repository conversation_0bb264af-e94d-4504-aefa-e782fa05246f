{"name": "讯飞星火大模型", "provider": "xun<PERSON>i", "api_key": "e2d8e0181478abaeffddb3dba610e0d5", "app_id": "3f86f9c9", "api_secret": "ZWU5ZjMyMjI0N2RmZWM2Yzc5YzdkOWE4", "api_password": "ClkATfbSHoKqleBWEQbR:EkAmkrTtQEbcCpVaiQUk", "base_url": "wss://spark-api.xf-yun.com/v3.5/chat", "api_base": "https://spark-api-open.xf-yun.com/v1/chat/completions", "model": "generalv3.5", "models": [{"id": "lite", "name": "Spark Lite", "max_tokens": 4096}, {"id": "generalv3", "name": "Spark Pro", "max_tokens": 8192}, {"id": "pro-128k", "name": "Spark Pro-128K", "max_tokens": 4096}, {"id": "generalv3.5", "name": "Spark Max", "max_tokens": 8192, "default": true}, {"id": "max-32k", "name": "Spark Max-32K", "max_tokens": 8192}, {"id": "4.0Ultra", "name": "Spark 4.0 Ultra", "max_tokens": 8192}], "default_model": "generalv3.5", "max_tokens": 4096, "temperature": 0.5, "top_k": 4, "top_p": 1.0, "presence_penalty": 0, "frequency_penalty": 0, "timeout": 60, "retry_count": 3, "retry_delay": 2, "stream": false, "system_prompt": "你是一个专业的代码分析和驱动开发专家。你需要对Windows驱动代码进行全面、深入的分析，提供详细的中文注释和解释，同时能够识别驱动代码中的潜在问题和优化空间。", "features": {"web_search": {"enable": true, "show_ref_label": true, "search_mode": "deep"}, "function_call": {"enable": false, "tool_calls_switch": false}}, "request_example": {"curl": "curl -i -k -X POST 'https://spark-api-open.xf-yun.com/v1/chat/completions' \n--header 'Authorization: Bearer ClkATfbSHoKqleBWEQbR:EkAmkrTtQEbcCpVaiQUk' \n--header 'Content-Type: application/json' \n--data '{\n    \"model\":\"generalv3.5\",\n    \"messages\": [\n        {\n            \"role\": \"user\",\n            \"content\": \"来一个只有程序员能听懂的笑话\"\n        }\n    ],\n    \"stream\": true\n}'", "javascript": "const options = {\n  method: 'POST',\n  headers: {\n    'Authorization': 'Bearer ClkATfbSHoKqleBWEQbR:EkAmkrTtQEbcCpVaiQUk', \n    'Content-Type': 'application/json'\n  },\n  body: JSON.stringify({\n    model: 'generalv3.5',\n    messages: [\n      {\n        role: 'user',\n        content: '来一个只有程序员能听懂的笑话'\n      }\n    ],\n    stream: false\n  })\n};\n\nfetch('https://spark-api-open.xf-yun.com/v1/chat/completions', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, "request_parameters": {"model": {"type": "string", "required": true, "description": "模型版本"}, "user": {"type": "string", "required": false, "description": "用户的唯一ID"}, "messages": {"type": "array", "required": true, "description": "对话历史"}, "temperature": {"type": "float", "required": false, "default": 1.0, "range": [0, 2], "description": "核采样阈值"}, "top_p": {"type": "float", "required": false, "default": 1.0, "range": "(0, 1]", "description": "生成过程中核采样方法概率阈值"}, "top_k": {"type": "int", "required": false, "default": 4, "range": [1, 6], "description": "从k个中随机选择一个(非等概率)"}, "presence_penalty": {"type": "float", "required": false, "default": 0, "range": [-2.0, 2.0], "description": "重复词的惩罚值"}, "frequency_penalty": {"type": "float", "required": false, "default": 0, "range": [-2.0, 2.0], "description": "频率惩罚值"}, "stream": {"type": "bool", "required": false, "default": false, "description": "是否流式返回结果"}, "max_tokens": {"type": "int", "required": false, "default": 4096, "range": [1, 8192], "description": "模型回答的tokens的最大长度"}}, "response_format": {"non_stream": {"code": 0, "message": "Success", "sid": "cha000b0003@dx1905cd86d6bb86d552", "choices": [{"message": {"role": "assistant", "content": "输出内容"}, "index": 0}], "usage": {"prompt_tokens": 6, "completion_tokens": 42, "total_tokens": 48}}, "stream": {"format": "data:{JSON数据}\n\ndata:[DONE]"}}, "status_codes": {"0": "成功", "10000": "输入参数不合法", "10001": "缺少必要参数", "10002": "鉴权失败，请检查鉴权信息", "10005": "请求超过发送限制", "10014": "服务暂不可用，请稍后重试", "10019": "账户余额不足", "11111": "模型内部错误，请稍后重试"}, "supported_features": ["web_search", "function_call", "json_output"]}