# MCP over Stdio: 最佳实践与迁移指南

## 1. 背景：为何从 HTTP 迁移？

本项目最初采用基于 Node.js `http` 模块和 `@modelcontextprotocol/sdk` 的 `StreamableHTTPServerTransport` 实现 MCP 服务器。尽管这在理论上提供了最大的灵活性（例如，允许浏览器客户端直接连接），但在实践中，我们遇到了由 SDK 的 HTTP/SSE 实现缺陷导致的一系列难以调试的底层网络问题：

- **Node.js 客户端 `SyntaxError`**: SDK 的 SSE 实现在握手后发送了无效的初始数据包，导致 Node.js 客户端解析失败。
- **浏览器客户端 `406 Not Acceptable`**: SDK 内部对 POST 请求的内容协商处理存在缺陷，导致浏览器无法正确发送 `resources/list` 等请求。

尽管我们通过“外科手术式”的补丁临时解决了这些问题，但这种方法脆弱且不可持续。根本问题在于，对于 VS Code 插件这种本地进程间通信（IPC）场景，引入复杂的 HTTP 网络协议栈不仅没有必要，反而带来了不必要的复杂性和不稳定性（如端口冲突、防火墙问题、CORS策略等）。

因此，我们决定重构，采用更直接、更稳定的 **Stdio (Standard Input/Output)** 传输方式。

## 2. Stdio 方案的核心优势

在 VS Code 插件与其 MCP 服务器子进程之间使用 Stdio 进行通信，具有压倒性的优势：

- **极致的简单性**: 无需管理端口、处理 HTTP 请求/响应、设置 CORS 或担心 SSE 的复杂性。通信就是简单地读写进程的标准输入/输出流。
- **无与伦比的稳定性**: 消除了所有网络层可能出现的问题。连接是即时的、可靠的，不受系统网络配置或防火墙的影响。
- **更高的安全性**: 通信被限制在父子进程之间，不暴露任何网络端口，从根本上杜绝了外部应用访问的可能性。
- **更高的效率**: 绕过了整个网络协议栈，数据直接在进程间通过管道传输，延迟更低，资源消耗更少。

## 3. 实现指南

迁移到 Stdio 模型涉及对服务器和客户端的同步改造。

### 3.1. 服务器端 (`src/mcp/server.ts`)

服务器端的改造最为彻底。所有与 `http` 相关的代码都被删除，只留下最核心的 MCP 逻辑。

**核心代码:**
```typescript
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import * as vscode from 'vscode';

// 1. 定义资源
const showMessageResource = {
    uri: 'tool:///showMessage',
    readable: true,
    async read(uri: string, { signal }: { signal: AbortSignal }) {
        const url = new URL(uri);
        const message = url.searchParams.get('message') ?? 'Default message';
        vscode.window.showInformationMessage(`[MCP Server]: ${message}`);
        return { content: `Message displayed: "${message}"`, contentType: 'text/plain' };
    },
};

// 2. 创建并启动服务器
async function main() {
    console.log('MCP Server (Stdio) is running.');

    const transport = new StdioServerTransport();
    const server = new Server({ transport });

    server.addResource(showMessageResource);

    await server.listen();
}

main().catch(console.error);
```

**关键点**:
- 只需引入 `Server` 和 `StdioServerTransport`。
- 创建 `StdioServerTransport` 实例，无需任何参数。
- 将 transport 传递给 `Server` 构造函数并启动监听。服务器会自动开始监听 `process.stdin` 并将响应写入 `process.stdout`。

### 3.2. 客户端 (`src/extension.ts`)

客户端（在我们的案例中是 VS Code 插件主进程）现在负责启动服务器子进程，并使用其标准输入/输出流进行通信。

**核心代码:**
```typescript
import { spawn, ChildProcess } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

let mcpClient: Client | null = null;
let serverProcess: ChildProcess | null = null;

async function startMcpClient(context: vscode.ExtensionContext) {
    await stopMcpClient(); // 确保清理旧进程

    // 1. 定位并启动服务器脚本
    const serverScriptPath = vscode.Uri.joinPath(context.extensionUri, 'out', 'mcp', 'server.js').fsPath;
    serverProcess = spawn('node', [serverScriptPath]);

    // 2. 监听子进程事件 (非常重要!)
    serverProcess.stdout?.on('data', (data) => console.log(`[MCP Server stdout]: ${data.toString().trim()}`));
    serverProcess.stderr?.on('data', (data) => console.error(`[MCP Server stderr]: ${data.toString().trim()}`));
    serverProcess.on('close', (code) => { /* ...处理进程关闭... */ });

    if (!serverProcess.stdin || !serverProcess.stdout) {
        // 必须确保这两个流存在
        return;
    }

    // 3. 创建 Stdio 客户端
    const transport = new StdioClientTransport(serverProcess.stdin, serverProcess.stdout);
    mcpClient = new Client({ transport });

    // 4. 连接并使用
    await mcpClient.connect();
    console.log('[Extension] MCP client connected to server via Stdio.');
    
    // ... 调用 mcpClient.listResources() 或 mcpClient.readResource()
}

async function stopMcpClient() {
    if (mcpClient) {
        mcpClient.close();
        mcpClient = null;
    }
    if (serverProcess && !serverProcess.killed) {
        // 确保子进程被终止
        serverProcess.kill('SIGTERM');
        serverProcess = null;
    }
}
```

## 4. 生命周期管理

正确管理服务器子进程的生命周期至关重要。

- **启动**: 在插件的 `activate` 函数中调用 `startMcpClient`。
- **停止**: 在插件的 `deactivate` 函数中调用 `stopMcpClient`。这**必须**包含 `serverProcess.kill()` 的逻辑，否则在重新加载窗口或停用插件时，旧的服务器进程将变成僵尸进程，导致资源泄漏和意外行为。

## 5. 调试技巧

- **日志是关键**: 如上述代码所示，在客户端为服务器进程的 `stdout` 和 `stderr` 添加监听器。所有服务器端的 `console.log` 和错误都会出现在客户端的调试控制台中，并带有 `[MCP Server ...]` 前缀，非常便于问题定位。
- **VS Code 调试器**: 可以为子进程配置一个单独的调试任务。在 `.vscode/launch.json` 中，添加一个 `attach` 类型的配置，以附加到 Node.js 进程上。但这通常没有直接查看日志来得方便快捷。
- **单独测试服务器**: 你可以直接在终端中运行 `node out/mcp/server.js` 来启动服务器。然后你可以手动输入 JSON-RPC 消息来测试其响应，尽管这比较繁琐。
