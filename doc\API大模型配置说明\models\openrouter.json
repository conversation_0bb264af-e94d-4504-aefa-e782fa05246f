{"name": "OpenRouter", "provider": "openrouter", "api_key": "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", "base_url": "https://openrouter.ai/api/v1/chat/completions", "api_base": "https://openrouter.ai/api/v1/chat/completions", "model": "deepseek/deepseek-coder:free", "alternate_models": ["deepseek/deepseek-chat-v3-0324:free", "anthropic/claude-3-opus:free", "meta-llama/codellama-70b-instruct:free"], "max_tokens": 4096, "temperature": 0.5, "timeout": 120, "retry_count": 3, "retry_delay": 2, "http_referer": "MACE-S_Windows_Driver_Analysis", "x_title": "Windows驱动代码分析工具", "system_prompt": "你是一个专业的Windows驱动开发专家，特别擅长分析和优化复杂的驱动程序。请对Windows驱动代码进行全面、深入的分析，关注以下方面：\n\n1. 代码结构和设计模式\n2. 内核交互机制\n3. 硬件操作和寄存器访问\n4. 驱动生命周期管理\n5. 异常处理和错误恢复\n6. 可能的安全弱点和优化空间\n\n请提供详细的中文注释和解释，并在需要时提供代码改进建议。"}