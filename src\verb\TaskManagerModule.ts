import { v4 as uuidv4 } from 'uuid';

export type TaskStatus = 'pending' | 'running' | 'success' | 'error' | 'cancelled';

export interface TaskResult {
  status: TaskStatus;
  output?: unknown;
  error?: string;
  progress?: string; // 可选，流式进度
}

/**
 * @description 统一管理异步任务状态的任务管理器
 */
export class TaskManager {
  private tasks: Map<string, TaskResult> = new Map();

  /** 创建新任务，初始状态 pending */
  public create_task(): string {
    const task_id = uuidv4();
    this.tasks.set(task_id, { status: 'pending' });
    return task_id;
  }

  /** 更新任务状态 */
  public update_task(task_id: string, result: TaskResult): void {
    this.tasks.set(task_id, result);
  }

  /** 查询任务状态 */
  public get_task(task_id: string): TaskResult | undefined {
    return this.tasks.get(task_id);
  }
}
