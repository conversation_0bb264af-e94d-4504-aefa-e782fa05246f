import * as vscode from 'vscode';

import { CommandManagerPanel } from '../../exit/CommandManagerPanel.js';
import type { CommandStorageModule } from '../../object/CommandStorageModule.js';

/**
 * 注册“管理命令白名单”命令
 * @param context VS Code 插件上下文
 * @param command_storage 命令存储模块
 */
export function register_manage_whitelist_command(
  context: vscode.ExtensionContext,
  command_storage: CommandStorageModule,
): void {
  context.subscriptions.push(
    vscode.commands.registerCommand('llm-bridge.manageWhitelist', () => {
      CommandManagerPanel.create_or_show(context, command_storage);
    }),
  );
}
