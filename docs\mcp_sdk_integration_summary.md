# VSCode 插件与 MCP SDK 集成实践学习总结

**日期:** 2025-06-25

## 核心原则

> “从头再次检查不要猜测，事实求是，到有报错的地方详细化报错日志，这才是正确方向。”

这句在调试过程中反复强调的原则，是本次项目最核心、最宝贵的行动指南。所有技术上的突破，最终都源于对这一基本原则的回归和坚守。

---

## 1. 架构演进：从 HTTP/SSE 到 Stdio 的战略转移

- **初始方案 (HTTP/SSE):** 最初尝试使用标准的 HTTP + Server-Sent Events (SSE) 作为 MCP 的传输层。尽管这是官方范例之一，但在实践中遇到了由 `@modelcontextprotocol/sdk` 库自身 Bug 导致的、难以调试的底层连接问题（Node.js 客户端的 `SyntaxError` 和浏览器客户端的 `406` 错误）。

- **最终方案 (Stdio):** 最终，我们战略性地放弃了网络传输层，全面转向基于标准输入/输出 (`Stdio`) 的进程间通信。客户端通过 `child_process.spawn` 启动服务端子进程，并通过 `StdioClientTransport` 和 `StdioServerTransport` 进行通信。

- **核心优势:**
  - **根除网络问题:** 彻底消除了所有与端口、CORS、SSE 握手、HTTP 头相关的复杂性和不确定性。
  - **极致的稳定性与性能:** 进程间通信是本地最高效、最可靠的通信方式之一，延迟极低，无网络抖动。
  - **架构简化:** 移除了所有 `express`, `http`, `net` 等模块的依赖和代码，使服务端逻辑更纯粹，专注于处理 MCP 消息。

**结论:** 对于 VSCode 插件这种“宿主-子进程”架构，Stdio 是远优于 HTTP 的 MCP 传输方案。

---

## 2. TypeScript 模块解析：从“魔法”到“标准”

- **问题根源:** 项目初期使用了 TypeScript 的路径别名 (Path Aliases) 功能，如 `import ... from '@/mcp/server'`。虽然这在编码时提供了便利，并能被 `tsc` 编译通过，但在 Node.js **运行时**却导致了 `MODULE_NOT_FOUND` 错误。因为 Node.js 的模块加载器不理解这些编译时的“魔法”路径。

- **解决方案:**
  1. **移除所有路径别名:** 删除了 `tsconfig.json` 中的 `baseUrl` 和 `paths` 配置。
  2. **回归相对路径:** 所有项目内部的 `import` 语句全部改为标准的、Node.js 可解析的相对路径 (e.g., `../../*.js`)。
  3. **`package.json` 的 `exports` 字段:** 深刻理解了现代 Node.js 库（如 MCP SDK）如何使用 `exports` 字段来定义其公共 API 的入口点。这使得我们可以直接 `import { Server } from '@modelcontextprotocol/sdk/server'`，而无需关心其内部 `dist/esm` 等具体文件结构。

**结论:** 在编写需要被 Node.js 直接执行的 TypeScript 代码时，应避免使用路径别名等非标准模块解析机制，以确保编译产物的可移植性和运行时稳定性。

---

## 3. MCP SDK 核心设计思想：从“寻找”到“创造”

- **最大的认知突破:** 项目中最关键的转折点，是意识到我们一直在**错误地“寻找”** SDK 中预定义的、应用层的文件操作类型（如 `ReadFile`, `ListFiles`）。

- **SDK 的真正哲学:** `@modelcontextprotocol/sdk` 本质上是一个**协议框架 (Framework)**，而非一个功能完备的库 (Library)。它提供的是：
  - **传输层抽象:** (Stdio, HTTP, etc.)
  - **JSON-RPC 消息处理机制。**
  - **基础类型和 Schema:** (如 `ListResourcesRequestSchema`)。

- **开发者的责任:** **具体的应用层协议需要由开发者自行定义。** SDK 的设计思想是，我们使用 `Zod` 等工具来**“创造”** 属于我们自己应用的请求和响应 Schema (如 `ListFilesRequestSchema`)，然后将其注册到 MCP 服务器中。

- **最终的修复:**
  1. 删除了所有试图从 SDK 导入自定义类型的错误 `import` 语句。
  2. 在 `server.ts` 中，使用 `Zod` 完整定义了 `file/list`, `file/read`, `command/run` 等所有应用层协议的 Schema。
  3. 使用 `z.infer<typeof ...>` 从 Schema 中自动推断出相应的 TypeScript 类型，实现了完美的类型安全。

**结论:** 使用 MCP SDK 的正确模式是“**定义你自己的协议**”。任何试图在 SDK 内部寻找具体业务逻辑类型的尝试，都源于对该框架核心设计思想的误解。

---

## 4. 调试方法论：从“猜测”到“求是”

- **隔离变量:** 当遇到多个潜在问题时（如网络、模块解析、类型错误），通过架构重构（切换到 Stdio）来隔离和排除变量，是最高效的调试策略。
- **信任编译器:** `tsc` 编译器的输出是判断代码健康状况的最终标准。IDE 的错误提示可能因缓存而过时。
- **日志是眼睛:** 在调试 SDK 黑盒问题时，深入其内部、添加详细的日志记录，是照亮真相的唯一途径。

这份总结凝聚了我们整个调试过程中的所有关键洞察。它将作为项目的核心技术文档，指导未来的开发和维护工作。
