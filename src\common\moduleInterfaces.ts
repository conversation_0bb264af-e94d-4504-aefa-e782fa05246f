import type { z } from 'zod';

import type { ContextPackage, ExecutionResult, LlmCommand } from '../object/dataModels.js';
import type { IComposableModule } from './ModuleUniverse.js';

// ====================================================================================
// Core Composable Module Interfaces for the Natural Language Programming Paradigm
// ====================================================================================

/**
 * @description Captures the user's context. Implements IComposableModule.
 * @input `string` - The user's raw request.
 * @output `Promise<ContextPackage>` - A structured context package.
 */
export type IContextCapturer = IComposableModule<string, ContextPackage>;

/**
 * @description Serializes/prepares the context for the LLM. Implements IComposableModule.
 * @input `ContextPackage` - The initial context package.
 * @output `Promise<ContextPackage>` - The processed context package ready for the LLM.
 */
export type IPromptSerializer = IComposableModule<ContextPackage, ContextPackage>;

/**
 * @description Dispatches the context to an LLM and gets a command back. Implements IComposableModule.
 * It also contains non-composable methods like `notify`.
 * @input `ContextPackage` - The context package for the LLM.
 * @output `Promise<LlmCommand | null>` - The command returned by the LLM.
 */
export interface IExternalLlmClient extends IComposableModule<ContextPackage, LlmCommand | null> {
  notify(result: ExecutionResult): Promise<string>;
}

/**
 * @description Parses the raw LLM response into a structured command. Implements IComposableModule.
 * @input `LlmCommand | null` - The raw command from the LLM.
 * @output `Promise<LlmCommand | null>` - A structured, executable command.
 */
export type ICommandParser = IComposableModule<LlmCommand | null, LlmCommand | null>;

/**
 * @description Executes a structured command. Implements IComposableModule.
 * @input `LlmCommand` - The structured command to execute.
 * @output `Promise<ExecutionResult>` - The result of the command execution.
 */
export interface ICommandExecutor extends IComposableModule<LlmCommand, ExecutionResult> {
  get_available_tools(): unknown[];
}

// ====================================================================================
// Other Interfaces
// ====================================================================================

/**
 * @description Interface for the UI Manager module.
 */
export interface IUiManager {
  show(message_handler: (message: unknown) => void): void;
  update(data: unknown): void;
  hide(): void;
  on_user_input?: (input: string, model: string) => void;
}

/**
 * @description Mistral 模型配置接口
 */
export interface MistralConfig {
  name: string;
  api_key: string;
  base_url: string;
  model: string;
  [key: string]: unknown;
}

/**
 * @description Spark 模型配置接口
 */
export interface SparkConfig {
  api_key: string;
  name?: string;
  [key: string]: unknown;
}

// ====================================================================================
// Tool Definition
// ====================================================================================

/**
 * @description Defines the standard structure for a tool.
 * This interface is used across the application to ensure consistency.
 */
export interface Tool {
  description: string;
  parameters_schema: z.AnyZodObject;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handler: (params: any) => Promise<unknown>;
}

export type ToolMap = Map<string, Tool>;
