/**
 * 模块宇宙 - 中央注册表
 * 实现"万物皆模块"的核心理念
 * 所有模块通过统一接口进行注册和调用
 */

/**
 * 模块统一接口
 * 所有模块必须实现此接口
 */
export interface I模块 {
  readonly 名称: string;       // 唯一标识符
  readonly 类型: string;       // 模块类型（名词/动词/事件/工具）
  执行(...参数: any[]): any;   // 统一执行方法
}

/**
 * 模块注册中心
 * 单例模式，全局唯一的模块管理器
 */
class ModuleRegistry {
  private static instance: ModuleRegistry;
  private modules = new Map<string, I模块>();
  private 执行历史: Array<{时间: Date, 模块: string, 参数: any[], 结果: any}> = [];

  private constructor() {}

  static getInstance(): ModuleRegistry {
    if (!ModuleRegistry.instance) {
      ModuleRegistry.instance = new ModuleRegistry();
    }
    return ModuleRegistry.instance;
  }

  /**
   * 注册新模块
   */
  注册(模块: I模块): void {
    if (this.modules.has(模块.名称)) {
      console.warn(`模块 ${模块.名称} 已存在，将被覆盖`);
    }
    this.modules.set(模块.名称, 模块);
    console.log(`✅ 模块已注册: ${模块.名称} (${模块.类型})`);
  }

  /**
   * 调用模块
   */
  调用(名称: string, ...参数: any[]): any {
    const 模块 = this.modules.get(名称);
    if (!模块) {
      throw new Error(`模块 ${名称} 未找到`);
    }

    try {
      const 开始时间 = Date.now();
      const 结果 = 模块.执行(...参数);
      const 执行时间 = Date.now() - 开始时间;
      
      // 记录执行历史
      this.执行历史.push({
        时间: new Date(),
        模块: 名称,
        参数,
        结果
      });

      console.log(`🚀 模块执行: ${名称} (${执行时间}ms)`);
      return 结果;
    } catch (error) {
      console.error(`❌ 模块执行失败: ${名称}`, error);
      throw error;
    }
  }

  /**
   * 组合多个模块执行
   */
  组合(...模块序列: [string, ...any[]][]): Function {
    return (...初始参数: any[]) => {
      let 当前结果 = 初始参数;
      
      for (const [模块名, ...模块参数] of 模块序列) {
        当前结果 = this.调用(模块名, ...当前结果, ...模块参数);
      }
      
      return 当前结果;
    };
  }

  /**
   * 获取模块列表
   */
  获取模块列表(): Array<{名称: string, 类型: string}> {
    return Array.from(this.modules.values()).map(模块 => ({
      名称: 模块.名称,
      类型: 模块.类型
    }));
  }

  /**
   * 获取执行统计
   */
  获取统计(): {
    总模块数: number;
    名词模块: number;
    动词模块: number;
    事件模块: number;
    工具模块: number;
    执行次数: number;
  } {
    const 模块列表 = Array.from(this.modules.values());
    return {
      总模块数: 模块列表.length,
      名词模块: 模块列表.filter(m => m.类型.includes('名词')).length,
      动词模块: 模块列表.filter(m => m.类型.includes('动词')).length,
      事件模块: 模块列表.filter(m => m.类型.includes('事件')).length,
      工具模块: 模块列表.filter(m => m.类型.includes('工具')).length,
      执行次数: this.执行历史.length
    };
  }

  /**
   * 动态创建模块
   */
  创建模块(名称: string, 类型: string, 执行函数: (...参数: any[]) => any): void {
    const 新模块: I模块 = {
      名称,
      类型,
      执行: 执行函数
    };
    this.注册(新模块);
  }
}

// 导出全局单例
export const 模块宇宙 = ModuleRegistry.getInstance();

// 便捷的全局函数
export const 注册模块 = (模块: I模块) => 模块宇宙.注册(模块);
export const 调用模块 = (名称: string, ...参数: any[]) => 模块宇宙.调用(名称, ...参数);
export const 组合模块 = (...模块序列: [string, ...any[]][]) => 模块宇宙.组合(...模块序列);
