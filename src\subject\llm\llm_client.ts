import type { AxiosError, AxiosResponse } from 'axios';
import axios from 'axios';
import * as vscode from 'vscode';
import { z } from 'zod';

import type { ChatMessage } from '../../object/dataModels.js';

// #region Zod Schemas for Validation

// Zod schema for validating the agent's tool call response.
const tool_call_schema = z.object({
  tool: z.string(),
  parameters: z.record(z.unknown()),
});

// Zod schema for a single message in the LLM API response.
const message_schema = z.object({
  content: z.string(),
});

// Zod schema for a single choice in the LLM API response.
const choice_schema = z.object({
  message: message_schema,
});

// Zod schema for the overall expected LLM API response structure.
const llm_api_response_schema = z.object({
  choices: z.array(choice_schema).min(1),
});

// Zod schema for validating the error response from the LLM API.
export const llm_error_response_schema = z.object({
  error: z.object({
    message: z.string(),
  }),
});

// #endregion

// #region Type Definitions
interface ApiConfig {
  endpoints: Record<string, string>;
  models: Record<string, string>;
  api_key: string;
}

interface BasePayload {
  temperature?: number;
  options?: Record<string, unknown>;
}

interface ChatPayload extends BasePayload {
  messages: ChatMessage[];
  system_prompt?: string;
}

interface EmbeddingPayload extends BasePayload {
  input: string | string[];
}

interface AgentPayload extends BasePayload {
  messages: ChatMessage[];
  json_schema?: object;
}

type LlmPayload = ChatPayload | EmbeddingPayload | AgentPayload;

// Extend AxiosError for type-safe access to response data.
interface TypedAxiosError<T = unknown> extends AxiosError<T> {
  response?: AxiosResponse<T>;
}

// #endregion

const sleep = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

function build_request_body(tool_type: string, payload: LlmPayload, model?: string): Record<string, unknown> {
  const body: Record<string, unknown> = {
    model: model || 'default-model',
    temperature: payload.temperature ?? 0.7,
    options: payload.options ?? {},
  };

  if ('messages' in payload) {
    body.messages = payload.messages;
  }

  if (tool_type === 'chat' && 'system_prompt' in payload) {
    body.system_prompt = payload.system_prompt;
  }

  if (tool_type === 'embedding' && 'input' in payload) {
    body.input = payload.input;
  }

  if (tool_type === 'agent' && 'json_schema' in payload) {
    body.json_schema = payload.json_schema;
  }

  return body;
}

async function handle_api_error(
  error: unknown,
  attempt: number,
  max_retries: number,
  tool_type: string,
  payload: LlmPayload,
): Promise<[boolean, LlmPayload]> {
  const axios_error = error as TypedAxiosError<z.infer<typeof llm_error_response_schema>>;
  const status_code = axios_error.response?.status;

  if (status_code === 429) {
    const retry_after = Number(axios_error.response?.headers['retry-after'] || '1');
    const backoff_ms = 1000 * retry_after + Math.floor(Math.random() * 1000);
    void vscode.window.showWarningMessage(`Rate limited by API. Retrying in ${backoff_ms / 1000} seconds...`);
    await sleep(backoff_ms);
    return [true, payload];
  }

  if (attempt < max_retries) {
    void vscode.window.showWarningMessage(`API request failed. Retrying... (Attempt ${attempt + 1}/${max_retries})`);
    await sleep(1000 * Math.pow(2, attempt)); // Exponential backoff
    return [true, payload];
  }

  const error_message = axios_error.response?.data?.error?.message || 'An unknown API error occurred.';
  void vscode.window.showErrorMessage(`LLM API Error: ${error_message}`);
  return [false, payload];
}

export async function call_llm_api(
  tool_type: string,
  payload: LlmPayload,
  api_config: ApiConfig,
  model?: string,
  max_retries = 3,
): Promise<z.infer<typeof llm_api_response_schema> | z.infer<typeof tool_call_schema> | null> {
  const endpoint = api_config.endpoints[tool_type];
  if (!endpoint) {
    void vscode.window.showErrorMessage(`Endpoint for tool type '${tool_type}' is not configured.`);
    return null;
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
     
    Authorization: `Bearer ${api_config.api_key}`,
  };

  let current_payload = payload;

  for (let attempt = 1; attempt <= max_retries; attempt++) {
    try {
      const body = build_request_body(tool_type, current_payload, model);
      const response = await axios.post(endpoint, body, { headers });

      const parsed_response = llm_api_response_schema.safeParse(response.data);
      if (!parsed_response.success) {
        throw new Error('LLM API response did not match the expected schema.');
      }
      const response_data = parsed_response.data;

      if (tool_type === 'agent') {
        const content = response_data.choices[0].message.content;
        try {
          const parsed_content: unknown = JSON.parse(content);
          const parsed_json = tool_call_schema.safeParse(parsed_content);
          if (parsed_json.success) {
            return parsed_json.data; // Success
          }
        } catch (json_error: unknown) {
          const error_message = json_error instanceof Error ? json_error.message : String(json_error);
          void vscode.window.showErrorMessage(`Failed to parse LLM agent response as JSON: ${error_message}`);
        }
        throw new Error('LLM response for agent tool did not match the required JSON schema.');
      }

      return response_data; // Success for other tool types
    } catch (error) {
      const [should_retry, updated_payload]: [boolean, LlmPayload] = await handle_api_error(
        error,
        attempt,
        max_retries,
        tool_type,
        current_payload,
      );
      current_payload = updated_payload;
      if (!should_retry) {
        return null; // Stop retrying
      }
    }
  }

  void vscode.window.showErrorMessage(`Failed to get a valid response from LLM after ${max_retries} attempts.`);
  return null;
}
