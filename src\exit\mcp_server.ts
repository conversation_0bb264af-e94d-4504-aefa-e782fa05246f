import express, { type Express, type Request, type Response } from 'express';
import type { Server } from 'http';
import { z } from 'zod';
import cors from 'cors';

import type { Tool } from '../verb/moduleInterfaces.js';

// MCP JSON-RPC 2.0 请求架构
const mcp_request_schema = z.object({
  jsonrpc: z.literal('2.0'),
  method: z.string(),
  params: z.record(z.unknown()).optional(),
  id: z.union([z.string(), z.number()]).optional(),
});

// 传统工具执行请求架构（向后兼容）
const execute_tool_request_schema = z.object({
  tool_name: z.string(),
  parameters: z.record(z.unknown()),
});

export class McpServer {
  private readonly _app: Express;
  private _server: Server | null = null;
  private readonly _tools: Record<string, Tool>;

  constructor(tools: Record<string, Tool>) {
    this._app = express();
    this._app.use(cors());
    this._app.use(express.json());
    this._tools = tools;
    this._setup_routes();
  }

  private _setup_routes(): void {
    // MCP协议根路径 - 处理JSON-RPC 2.0请求
    this._app.post('/', async (req: Request, res: Response): Promise<void> => {
      try {
        console.log('📥 [MCP Server] 收到请求:', JSON.stringify(req.body, null, 2));

        const validation_result = mcp_request_schema.safeParse(req.body);
        if (!validation_result.success) {
          const error_response = {
            jsonrpc: '2.0',
            error: {
              code: -32600,
              message: 'Invalid Request',
              data: validation_result.error.issues,
            },
            id: req.body?.id || null,
          };
          res.status(400).json(error_response);
          return;
        }

        const { method, params, id } = validation_result.data;

        // 处理不同的MCP方法
        switch (method) {
          case 'initialize':
            await this._handle_initialize(req, res, params, id);
            break;
          case 'notifications/initialized':
            await this._handle_initialized_notification(req, res, params, id);
            break;
          case 'tools/list':
            await this._handle_tools_list(req, res, params, id);
            break;
          case 'tools/call':
            await this._handle_tools_call(req, res, params, id);
            break;
          case 'ping':
            await this._handle_ping(req, res, params, id);
            break;
          default:
            console.log(`⚠️ [MCP Server] 未知方法: ${method}`);
            const error_response = {
              jsonrpc: '2.0',
              error: {
                code: -32601,
                message: 'Method not found',
                data: `Unknown method: ${method}`,
              },
              id: id || null,
            };
            res.status(404).json(error_response);
        }
      } catch (error: unknown) {
        const error_message = error instanceof Error ? error.message : 'An unknown error occurred';
        const error_response = {
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: 'Internal error',
            data: error_message,
          },
          id: req.body?.id || null,
        };
        res.status(500).json(error_response);
      }
    });

    // 传统工具执行端点（向后兼容）
    this._app.post('/execute_tool', async (req: Request, res: Response): Promise<void> => {
      try {
        const validation_result = execute_tool_request_schema.safeParse(req.body);
        if (!validation_result.success) {
          res.status(400).json({ error: 'Invalid request body', details: validation_result.error.issues });
          return;
        }

        const { tool_name, parameters } = validation_result.data;
        const tool = this._tools[tool_name];

        if (!tool) {
          res.status(404).json({ error: `Tool '${tool_name}' not found.` });
          return;
        }

        const result: unknown = await tool.handler(parameters);
        res.status(200).json({ result });
      } catch (error: unknown) {
        const error_message = error instanceof Error ? error.message : 'An unknown error occurred';
        const tool_name_for_error = execute_tool_request_schema.safeParse(req.body).success
          ? execute_tool_request_schema.parse(req.body).tool_name
          : 'unknown';
        res.status(500).json({ error: `Error executing tool '${tool_name_for_error}': ${error_message}` });
      }
    });

    // SSE端点支持
    this._app.get('/sse', (req: Request, res: Response): void => {
      console.log('🔗 [MCP Server] SSE连接请求');

      // 设置SSE头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // 发送初始连接确认
      res.write('data: {"type":"connection","status":"connected"}\n\n');

      // 保持连接活跃
      const heartbeat = setInterval(() => {
        res.write('data: {"type":"heartbeat","timestamp":"' + new Date().toISOString() + '"}\n\n');
      }, 30000);

      // 处理连接关闭
      req.on('close', () => {
        console.log('🔌 [MCP Server] SSE连接关闭');
        clearInterval(heartbeat);
      });
    });

    // 健康检查端点
    this._app.get('/health', (req: Request, res: Response): void => {
      res.json({
        status: 'healthy',
        server: 'MCP Server',
        tools_count: Object.keys(this._tools).length,
        timestamp: new Date().toISOString(),
      });
    });
  }

  // MCP协议处理方法
  private async _handle_initialize(req: Request, res: Response, params: any, id: any): Promise<void> {
    console.log('🚀 [MCP Server] 处理初始化请求');

    const response = {
      jsonrpc: '2.0',
      result: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
        },
        serverInfo: {
          name: 'VS Code Extension MCP Server',
          version: '1.0.0',
        },
      },
      id,
    };

    console.log('✅ [MCP Server] 初始化响应:', JSON.stringify(response, null, 2));
    res.json(response);
  }

  private async _handle_initialized_notification(req: Request, res: Response, params: any, id: any): Promise<void> {
    console.log('📢 [MCP Server] 处理初始化完成通知');

    // 对于通知，不需要返回响应（根据JSON-RPC 2.0规范）
    // 但我们可以返回一个确认
    res.status(200).json({
      jsonrpc: '2.0',
      result: {
        status: 'acknowledged',
        message: 'Initialization notification received',
      },
      id,
    });
  }

  private async _handle_ping(req: Request, res: Response, params: any, id: any): Promise<void> {
    console.log('🏓 [MCP Server] 处理ping请求');

    const response = {
      jsonrpc: '2.0',
      result: {
        status: 'pong',
        timestamp: new Date().toISOString(),
        server: 'VS Code Extension MCP Server',
      },
      id,
    };

    res.json(response);
  }

  private async _handle_tools_list(req: Request, res: Response, params: any, id: any): Promise<void> {
    console.log('📋 [MCP Server] 处理工具列表请求');

    const tools = Object.entries(this._tools).map(([name, tool]) => ({
      name,
      description: tool.description,
      inputSchema: {
        type: 'object',
        properties: this._zod_to_json_schema(tool.parameters_schema),
      },
    }));

    const response = {
      jsonrpc: '2.0',
      result: {
        tools,
      },
      id,
    };

    console.log('✅ [MCP Server] 工具列表响应:', JSON.stringify(response, null, 2));
    res.json(response);
  }

  private async _handle_tools_call(req: Request, res: Response, params: any, id: any): Promise<void> {
    console.log('🔧 [MCP Server] 处理工具调用请求:', params);

    try {
      const { name: tool_name, arguments: tool_arguments } = params;
      const tool = this._tools[tool_name];

      if (!tool) {
        const error_response = {
          jsonrpc: '2.0',
          error: {
            code: -32602,
            message: 'Invalid params',
            data: `Tool '${tool_name}' not found`,
          },
          id,
        };
        res.status(404).json(error_response);
        return;
      }

      // 验证参数
      const validation_result = tool.parameters_schema.safeParse(tool_arguments);
      if (!validation_result.success) {
        const error_response = {
          jsonrpc: '2.0',
          error: {
            code: -32602,
            message: 'Invalid params',
            data: validation_result.error.issues,
          },
          id,
        };
        res.status(400).json(error_response);
        return;
      }

      // 执行工具
      const result = await tool.handler(validation_result.data);

      const response = {
        jsonrpc: '2.0',
        result: {
          content: [
            {
              type: 'text',
              text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
            },
          ],
        },
        id,
      };

      console.log('✅ [MCP Server] 工具调用响应:', JSON.stringify(response, null, 2));
      res.json(response);
    } catch (error: unknown) {
      const error_message = error instanceof Error ? error.message : 'An unknown error occurred';
      const error_response = {
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal error',
          data: `Error executing tool: ${error_message}`,
        },
        id,
      };
      res.status(500).json(error_response);
    }
  }

  // 将Zod schema转换为JSON Schema（简化版）
  private _zod_to_json_schema(zodSchema: any): any {
    try {
      // 这是一个简化的转换，实际项目中可能需要更完整的实现
      if (zodSchema._def?.typeName === 'ZodObject') {
        const properties: any = {};
        const shape = zodSchema._def.shape();

        for (const [key, value] of Object.entries(shape)) {
          const fieldSchema = value as any;
          if (fieldSchema._def?.typeName === 'ZodString') {
            properties[key] = { type: 'string' };
            if (fieldSchema._def.description) {
              properties[key].description = fieldSchema._def.description;
            }
          } else if (fieldSchema._def?.typeName === 'ZodNumber') {
            properties[key] = { type: 'number' };
          } else if (fieldSchema._def?.typeName === 'ZodBoolean') {
            properties[key] = { type: 'boolean' };
          } else if (fieldSchema._def?.typeName === 'ZodEnum') {
            properties[key] = {
              type: 'string',
              enum: fieldSchema._def.values,
            };
          } else if (fieldSchema._def?.typeName === 'ZodOptional') {
            // 处理可选字段
            const innerSchema = fieldSchema._def.innerType;
            properties[key] = this._zod_to_json_schema(innerSchema);
          } else {
            properties[key] = { type: 'string' }; // 默认类型
          }
        }

        return properties;
      }

      // 处理基本类型
      if (zodSchema._def?.typeName === 'ZodString') {
        return { type: 'string' };
      } else if (zodSchema._def?.typeName === 'ZodNumber') {
        return { type: 'number' };
      } else if (zodSchema._def?.typeName === 'ZodBoolean') {
        return { type: 'boolean' };
      }

      return {};
    } catch (error) {
      console.warn('⚠️ [MCP Server] Zod schema转换失败:', error);
      return {};
    }
  }

  public start(port: number): void {
    this._server = this._app.listen(port, () => {
      console.log(`🚀 [MCP Server] 服务器启动成功，监听端口: ${port}`);
      console.log(`📋 [MCP Server] 已注册工具数量: ${Object.keys(this._tools).length}`);
      console.log(`🔗 [MCP Server] 健康检查: http://localhost:${port}/health`);
    });
  }

  public stop(): void {
    if (this._server) {
      this._server.close(() => {
        console.log('🛑 [MCP Server] 服务器已停止');
      });
    }
  }
}
