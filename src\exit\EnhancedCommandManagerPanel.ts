import * as vscode from 'vscode';
import type { EnhancedCommandManager, Command } from '../object/EnhancedCommandManager.js';

/**
 * 增强命令管理面板
 * 提供完整的白名单/黑名单管理界面
 */
export class EnhancedCommandManagerPanel {
  public static current_panel: EnhancedCommandManagerPanel | undefined;
  private readonly _panel: vscode.WebviewPanel;
  private readonly _disposables: vscode.Disposable[] = [];

  public static create_or_show(
    context: vscode.ExtensionContext,
    enhanced_command_manager: EnhancedCommandManager,
  ): void {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    if (EnhancedCommandManagerPanel.current_panel) {
      EnhancedCommandManagerPanel.current_panel._panel.reveal(column);
      return;
    }

    const panel = vscode.window.createWebviewPanel(
      'enhancedCommandManager',
      '增强命令管理器',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
      },
    );

    EnhancedCommandManagerPanel.current_panel = new EnhancedCommandManagerPanel(
      panel,
      context,
      enhanced_command_manager,
    );
  }

  private constructor(
    panel: vscode.WebviewPanel,
    private readonly _context: vscode.ExtensionContext,
    private readonly _enhanced_command_manager: EnhancedCommandManager,
  ) {
    this._panel = panel;
    this._update_webview_content();
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
    this._panel.webview.onDidReceiveMessage(this._handle_webview_message.bind(this), null, this._disposables);
  }

  private async _handle_webview_message(message: any): Promise<void> {
    try {
      switch (message.action) {
        case 'refresh_commands':
          await this._enhanced_command_manager.refresh_commands();
          void vscode.window.showInformationMessage('命令系统已刷新');
          await this._update_webview_content();
          break;

        case 'capture_commands':
          const capture_result = await this._enhanced_command_manager.capture_and_update_commands();
          void vscode.window.showInformationMessage(
            `捕获完成：新增 ${capture_result.newly_captured.length} 个命令到黑名单，移除 ${capture_result.removed_commands.length} 个无效命令`
          );
          await this._update_webview_content();
          break;

        case 'move_to_whitelist':
          if (message.command_ids && Array.isArray(message.command_ids)) {
            const result = await this._enhanced_command_manager.move_to_whitelist(message.command_ids);
            void vscode.window.showInformationMessage(
              `成功移动 ${result.success.length} 个命令到白名单`
            );
            await this._update_webview_content();
          }
          break;

        case 'remove_from_whitelist':
          if (message.command_ids && Array.isArray(message.command_ids)) {
            const result = await this._enhanced_command_manager.remove_from_whitelist(message.command_ids);
            void vscode.window.showInformationMessage(
              `成功从白名单移除 ${result.success.length} 个命令，${result.protected_commands.length} 个受保护命令无法移除`
            );
            await this._update_webview_content();
          }
          break;

        case 'get_classification':
          await this._update_webview_content();
          break;
      }
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      void vscode.window.showErrorMessage(`操作失败: ${error_message}`);
    }
  }

  private async _update_webview_content(): Promise<void> {
    const classification = this._enhanced_command_manager.get_command_classification();
    
    this._panel.webview.html = this._generate_html(classification);
  }

  private _generate_html(classification: {
    whitelist: Command[];
    blacklist: Command[];
    plugin_registered: Command[];
    mcp_core: Command[];
    unclassified: Command[];
  }): string {
    const nonce = this._get_nonce();

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
    <title>增强命令管理器</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        .title {
            font-size: 24px;
            font-weight: bold;
        }
        .actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .btn-secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        .btn-secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            padding: 15px;
            border-radius: 6px;
            border: 1px solid var(--vscode-panel-border);
        }
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        .stat-label {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .command-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }
        .command-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        .command-item:last-child {
            border-bottom: none;
        }
        .command-item:hover {
            background-color: var(--vscode-list-hoverBackground);
        }
        .command-info {
            flex: 1;
        }
        .command-id {
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
        }
        .command-meta {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-top: 2px;
        }
        .command-actions {
            display: flex;
            gap: 5px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        .protected {
            color: var(--vscode-errorForeground);
            font-weight: bold;
        }
        .mcp-core {
            color: var(--vscode-terminal-ansiYellow);
        }
        .plugin-registered {
            color: var(--vscode-terminal-ansiCyan);
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">🛠️ 增强命令管理器</div>
        <div class="actions">
            <button class="btn" onclick="refreshCommands()">🔄 刷新命令</button>
            <button class="btn btn-secondary" onclick="captureCommands()">📥 捕获命令</button>
        </div>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">${classification.whitelist.length}</div>
            <div class="stat-label">✅ 白名单命令</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${classification.blacklist.length}</div>
            <div class="stat-label">❌ 黑名单命令</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${classification.mcp_core.length}</div>
            <div class="stat-label">🔒 MCP核心工具</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${classification.plugin_registered.length}</div>
            <div class="stat-label">🔌 插件注册命令</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${classification.unclassified.length}</div>
            <div class="stat-label">❓ 未分类命令</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            ✅ 白名单命令 (可执行)
            <span>${classification.whitelist.length} 个命令</span>
        </div>
        <div class="command-list">
            ${classification.whitelist.length === 0 
                ? '<div class="empty-state">暂无白名单命令</div>'
                : classification.whitelist.map(cmd => `
                    <div class="command-item">
                        <div class="command-info">
                            <div class="command-id">${cmd.id}</div>
                            <div class="command-meta">
                                ${cmd.source} | ${cmd.category || 'general'}
                                ${this._enhanced_command_manager.is_mcp_core_tool(cmd.id) ? '<span class="mcp-core">MCP核心</span>' : ''}
                                ${this._enhanced_command_manager.is_plugin_registered_command(cmd.id) ? '<span class="plugin-registered">插件注册</span>' : ''}
                            </div>
                        </div>
                        <div class="command-actions">
                            ${!this._enhanced_command_manager.is_mcp_core_tool(cmd.id) 
                                ? `<button class="btn btn-small btn-secondary" onclick="removeFromWhitelist(['${cmd.id}'])">移除</button>`
                                : '<span class="protected">受保护</span>'
                            }
                        </div>
                    </div>
                `).join('')
            }
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            ❌ 黑名单命令 (不可执行)
            <span>${classification.blacklist.length} 个命令</span>
        </div>
        <div class="command-list">
            ${classification.blacklist.length === 0 
                ? '<div class="empty-state">暂无黑名单命令</div>'
                : classification.blacklist.slice(0, 100).map(cmd => `
                    <div class="command-item">
                        <div class="command-info">
                            <div class="command-id">${cmd.id}</div>
                            <div class="command-meta">${cmd.source} | ${cmd.category || 'general'}</div>
                        </div>
                        <div class="command-actions">
                            <button class="btn btn-small" onclick="moveToWhitelist(['${cmd.id}'])">移至白名单</button>
                        </div>
                    </div>
                `).join('')
            }
            ${classification.blacklist.length > 100 ? `<div class="command-item"><div class="command-info">... 还有 ${classification.blacklist.length - 100} 个命令</div></div>` : ''}
        </div>
    </div>

    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();

        function refreshCommands() {
            vscode.postMessage({ action: 'refresh_commands' });
        }

        function captureCommands() {
            vscode.postMessage({ action: 'capture_commands' });
        }

        function moveToWhitelist(commandIds) {
            vscode.postMessage({ 
                action: 'move_to_whitelist', 
                command_ids: commandIds 
            });
        }

        function removeFromWhitelist(commandIds) {
            if (confirm('确定要从白名单中移除这些命令吗？')) {
                vscode.postMessage({ 
                    action: 'remove_from_whitelist', 
                    command_ids: commandIds 
                });
            }
        }
    </script>
</body>
</html>`;
  }

  private _get_nonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  public dispose(): void {
    EnhancedCommandManagerPanel.current_panel = undefined;
    this._panel.dispose();
    while (this._disposables.length) {
      const disposable = this._disposables.pop();
      if (disposable) {
        disposable.dispose();
      }
    }
  }
}
