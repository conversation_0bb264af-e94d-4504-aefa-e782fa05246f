import os
import openai

client = openai.OpenAI(
  api_key=os.environ.get("GLHF_API_KEY"),
  base_url="https://api.glhf.chat/v1",
)

url:https://api.glhf.chat/v1
api:glhf_c9ad8e5f1b104a2776a38e4a36a20eaf
请注意，模型名称需要附加 “hf：”。例如，hf：meta-llama/llama-3.1-405B-Instruct。
和 endpoints 均可用。/chat/completions/completions

终端节点也可用，并显示任何始终可用的模型，以及您在过去 200 个线程中使用的任何模型。/models
completion = client.chat.completions.create(
  model="hf:mistralai/Mistral-7B-Instruct-v0.3",
  messages=[
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Compose a poem that explains the concept of recursion in programming."}
  ]
)

print(completion.choices[0].message)

Model names must be prefaced with and in format.hf:hf:owner/model

completion = client.chat.completions.create(
  stream=True,
  model="hf:mistralai/Mistral-7B-Instruct-v0.3",
  messages=[
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Compose a poem that explains the concept of recursion in programming."}
  ],
)

for chunk in completion:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end='', flush=True)
If you're using a large, slow-booting model that isn't always-on, you might experience timeouts when using the non-streaming API. For large models we strongly recommend opting into streaming, which shouldn't time out.