{"id": "spark", "name": "Spark", "description": "科大讯飞星火认知大模型配置", "version": "1.0.0", "api_type": "spark", "capabilities": {"max_tokens": 4096, "supports_streaming": true, "supports_function_calling": true, "supports_vision": false, "supports_code_execution": true}, "available_tools": [{"name": "reply_to_user", "description": "直接回复用户消息", "parameters": {"type": "object", "properties": {"message": {"type": "string", "description": "回复内容"}}, "required": ["message"]}, "enabled": true}, {"name": "search_commands", "description": "搜索VS Code命令", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "搜索关键词"}}, "required": ["query"]}, "enabled": true}, {"name": "execute_code", "description": "执行代码片段", "parameters": {"type": "object", "properties": {"language": {"type": "string", "description": "编程语言"}, "code": {"type": "string", "description": "要执行的代码"}}, "required": ["language", "code"]}, "enabled": true}], "prompt_templates": {"system_prompt": "你是科大讯飞星火认知大模型，一个专业的VS Code智能编程助手。你擅长中文对话和代码理解，可以帮助用户完成各种编程任务。", "user_prompt_template": "用户请求: {{user_request}}\n\n当前环境:\n- 工作区: {{workspace_root}}\n- 活动文件: {{active_file_path}}\n- 选中文本: {{selected_text}}", "tool_instruction_template": "请使用JSON格式返回工具调用指令:\n{\n  \"tool_name\": \"工具名称\",\n  \"parameters\": { \"参数名\": \"参数值\" }\n}", "context_template": "## 可用工具\n{{available_tools}}\n\n## 环境信息\n{{context_info}}\n\n## 用户请求\n{{user_request}}"}, "ui_config": {"panel_title": "Spark Chat", "panel_icon": "zap", "theme_color": "#1890FF", "welcome_message": "欢迎使用科大讯飞星火智能助手！我可以帮助您完成各种编程和代码分析任务。"}, "api_config": {"ws_url": "wss://spark-api.xf-yun.com/v1/x1", "api_secret": "ZWU5ZjMyMjI0N2RmZWM2Yzc5YzdkOWE4", "api_key": "e2d8e0181478abaeffddb3dba610e0d5", "appid": "3f86f9c9", "domain": "x1", "timeout": 30000}}