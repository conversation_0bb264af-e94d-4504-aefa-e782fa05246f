import * as vscode from 'vscode';

import type { Tool } from '../../verb/moduleInterfaces.js';
import { McpServer } from '../mcp_server.js';

/**
 * 启动MCP Server并处理错误，返回实例
 * @param all_tools 工具字典
 * @returns MCP Server实例
 */
export function start_mcp_server(all_tools: Record<string, Tool>): McpServer | null {
  try {
    const mcp_server = new McpServer(all_tools);
    mcp_server.start(3000);
    return mcp_server;
  } catch (error) {
    const error_message = error instanceof Error ? error.message : String(error);
    void vscode.window.showErrorMessage(`LLM Bridge: Failed to start MCP server. Error: ${error_message}`);
    return null;
  }
}
