import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { z } from 'zod';

import { model_config_schema, generate_config_template, type ModelConfig } from '../subject/modelConfigs.js';

/**
 * 统一配置管理器
 * 负责动态扫描、验证、生成和管理所有模型配置
 */
export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private _configs = new Map<string, ModelConfig>();
  private _config_dir: string;
  private _watcher: vscode.FileSystemWatcher | null = null;

  private constructor() {
    this._config_dir = path.join(__dirname, '../config');
  }

  static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  /**
   * 初始化配置管理器
   */
  async initialize(): Promise<void> {
    console.log('🔧 初始化配置管理器...');
    
    // 确保配置目录存在
    await this._ensure_config_directory();
    
    // 加载所有配置
    await this._load_all_configs();
    
    // 设置文件监听
    this._setup_file_watcher();
    
    console.log(`✅ 配置管理器初始化完成，加载了 ${this._configs.size} 个配置`);
  }

  /**
   * 获取所有配置
   */
  get_all_configs(): Map<string, ModelConfig> {
    return new Map(this._configs);
  }

  /**
   * 获取特定配置
   */
  get_config(model_id: string): ModelConfig | undefined {
    return this._configs.get(model_id);
  }

  /**
   * 添加新配置
   */
  async add_config(api_type: 'mistral' | 'spark' | 'openai', model_id: string): Promise<boolean> {
    try {
      // 检查是否已存在
      if (this._configs.has(model_id)) {
        void vscode.window.showWarningMessage(`配置 ${model_id} 已存在`);
        return false;
      }

      // 生成配置模板
      const template = generate_config_template(api_type, model_id);
      
      // 保存到文件
      const config_file = path.join(this._config_dir, `${model_id}_config.json`);
      await fs.promises.writeFile(config_file, JSON.stringify(template, null, 2), 'utf-8');
      
      // 验证并加载配置
      const loaded_config = await this._load_single_config(config_file);
      if (loaded_config) {
        this._configs.set(model_id, loaded_config);
        void vscode.window.showInformationMessage(`✅ 成功添加配置: ${model_id}`);
        return true;
      }
      
      return false;
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      void vscode.window.showErrorMessage(`添加配置失败: ${error_message}`);
      return false;
    }
  }

  /**
   * 删除配置
   */
  async remove_config(model_id: string): Promise<boolean> {
    try {
      const config_file = path.join(this._config_dir, `${model_id}_config.json`);
      
      // 删除文件
      if (fs.existsSync(config_file)) {
        await fs.promises.unlink(config_file);
      }
      
      // 从内存中移除
      this._configs.delete(model_id);
      
      void vscode.window.showInformationMessage(`✅ 成功删除配置: ${model_id}`);
      return true;
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      void vscode.window.showErrorMessage(`删除配置失败: ${error_message}`);
      return false;
    }
  }

  /**
   * 更新配置
   */
  async update_config(model_id: string, updates: Partial<ModelConfig>): Promise<boolean> {
    try {
      const existing_config = this._configs.get(model_id);
      if (!existing_config) {
        void vscode.window.showErrorMessage(`配置 ${model_id} 不存在`);
        return false;
      }

      // 合并更新
      const updated_config = { ...existing_config, ...updates };
      
      // 验证配置
      const validated_config = model_config_schema.parse(updated_config);
      
      // 保存到文件
      const config_file = path.join(this._config_dir, `${model_id}_config.json`);
      await fs.promises.writeFile(config_file, JSON.stringify(validated_config, null, 2), 'utf-8');
      
      // 更新内存
      this._configs.set(model_id, validated_config);
      
      void vscode.window.showInformationMessage(`✅ 成功更新配置: ${model_id}`);
      return true;
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      void vscode.window.showErrorMessage(`更新配置失败: ${error_message}`);
      return false;
    }
  }

  /**
   * 获取配置统计信息
   */
  get_stats(): {
    total: number;
    by_api_type: Record<string, number>;
    enabled_tools: number;
  } {
    const stats = {
      total: this._configs.size,
      by_api_type: {} as Record<string, number>,
      enabled_tools: 0,
    };

    for (const config of this._configs.values()) {
      // 统计API类型
      stats.by_api_type[config.api_type] = (stats.by_api_type[config.api_type] || 0) + 1;
      
      // 统计启用的工具
      stats.enabled_tools += config.available_tools.filter(tool => tool.enabled).length;
    }

    return stats;
  }

  /**
   * 确保配置目录存在
   */
  private async _ensure_config_directory(): Promise<void> {
    if (!fs.existsSync(this._config_dir)) {
      await fs.promises.mkdir(this._config_dir, { recursive: true });
      console.log(`📁 创建配置目录: ${this._config_dir}`);
    }
  }

  /**
   * 加载所有配置文件
   */
  private async _load_all_configs(): Promise<void> {
    try {
      const files = await fs.promises.readdir(this._config_dir);
      const config_files = files.filter(file => file.endsWith('_config.json'));
      
      console.log(`🔍 发现 ${config_files.length} 个配置文件:`, config_files);
      
      for (const file of config_files) {
        const config_path = path.join(this._config_dir, file);
        const config = await this._load_single_config(config_path);
        if (config) {
          this._configs.set(config.id, config);
          console.log(`✅ 加载配置: ${config.id} (${config.api_type})`);
        }
      }
    } catch (error) {
      console.error('❌ 加载配置文件失败:', error);
    }
  }

  /**
   * 加载单个配置文件
   */
  private async _load_single_config(config_path: string): Promise<ModelConfig | null> {
    try {
      const content = await fs.promises.readFile(config_path, 'utf-8');
      const raw_config = JSON.parse(content);
      
      // 验证配置
      const validated_config = model_config_schema.parse(raw_config);
      return validated_config;
    } catch (error) {
      const error_message = error instanceof Error ? error.message : String(error);
      console.error(`❌ 加载配置文件失败 ${config_path}:`, error_message);
      void vscode.window.showErrorMessage(`配置文件格式错误: ${path.basename(config_path)}\n${error_message}`);
      return null;
    }
  }

  /**
   * 设置文件监听
   */
  private _setup_file_watcher(): void {
    const pattern = new vscode.RelativePattern(this._config_dir, '*_config.json');
    this._watcher = vscode.workspace.createFileSystemWatcher(pattern);

    this._watcher.onDidCreate(async (uri) => {
      console.log(`📁 检测到新配置文件: ${uri.fsPath}`);
      const config = await this._load_single_config(uri.fsPath);
      if (config) {
        this._configs.set(config.id, config);
        void vscode.window.showInformationMessage(`🆕 自动加载新配置: ${config.id}`);
      }
    });

    this._watcher.onDidChange(async (uri) => {
      console.log(`📝 检测到配置文件变更: ${uri.fsPath}`);
      const config = await this._load_single_config(uri.fsPath);
      if (config) {
        this._configs.set(config.id, config);
        void vscode.window.showInformationMessage(`🔄 自动重载配置: ${config.id}`);
      }
    });

    this._watcher.onDidDelete((uri) => {
      const file_name = path.basename(uri.fsPath, '_config.json');
      this._configs.delete(file_name);
      void vscode.window.showInformationMessage(`🗑️ 自动移除配置: ${file_name}`);
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this._watcher) {
      this._watcher.dispose();
      this._watcher = null;
    }
  }
}

// 导出单例实例
export const configuration_manager = ConfigurationManager.getInstance();
