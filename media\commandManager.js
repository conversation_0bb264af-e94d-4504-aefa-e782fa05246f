(function () {
  const vscode = acquireVsCodeApi();
  let all_commands = [];
  let whitelisted_ids = new Set();

  const available_list = document.getElementById('available-list');
  const whitelist_list = document.getElementById('whitelist-list');
  const filter_available = document.getElementById('filter-available');
  const filter_whitelist = document.getElementById('filter-whitelist');
  const available_count = document.getElementById('available-count');
  const whitelist_count = document.getElementById('whitelist-count');

  function render() {
    if (
      !available_list ||
      !whitelist_list ||
      !filter_available ||
      !filter_whitelist ||
      !available_count ||
      !whitelist_count
    ) {
      return;
    }

    const filter_available_text = filter_available.value.toLowerCase();
    const filter_whitelist_text = filter_whitelist.value.toLowerCase();

    available_list.innerHTML = '';
    whitelist_list.innerHTML = '';

    let available_display_count = 0;

    all_commands.forEach(cmd => {
      if (!whitelisted_ids.has(cmd.id)) {
        if (cmd.id.toLowerCase().includes(filter_available_text)) {
          available_display_count++;
          const li = document.createElement('li');
          li.dataset.id = cmd.id;
          li.innerHTML = `<span class="id">${cmd.id}</span>`;
          const to_whitelist_btn = document.createElement('button');
          to_whitelist_btn.textContent = '>>';
          to_whitelist_btn.title = 'Add to Whitelist';
          to_whitelist_btn.className = 'move-btn';
          to_whitelist_btn.onclick = e => {
            e.stopPropagation();
            whitelisted_ids.add(cmd.id);
            render();
          };
          li.appendChild(to_whitelist_btn);
          available_list.appendChild(li);
        }
      } else {
        if (cmd.id.toLowerCase().includes(filter_whitelist_text)) {
          whitelist_display_count++;
          const li = document.createElement('li');
          li.dataset.id = cmd.id;

          // 检查是否为MCP核心工具
          const is_mcp_core = cmd.is_mcp_core || false;

          if (is_mcp_core) {
            li.innerHTML = `<span class="id mcp-core">${cmd.id}</span><span class="mcp-badge">🔒 MCP核心</span>`;
            li.title = 'MCP核心工具，不可移除';
            li.className = 'mcp-core-item';
          } else {
            li.innerHTML = `<span class="id">${cmd.id}</span>`;
            const from_whitelist_btn = document.createElement('button');
            from_whitelist_btn.textContent = '<<';
            from_whitelist_btn.title = 'Remove from Whitelist';
            from_whitelist_btn.className = 'move-btn';
            from_whitelist_btn.onclick = e => {
              e.stopPropagation();
              whitelisted_ids.delete(cmd.id);
              render();
            };
            li.appendChild(from_whitelist_btn);
          }

          whitelist_list.appendChild(li);
        }
      }
    });

    available_count.textContent = available_display_count.toString();
    whitelist_count.textContent = whitelisted_ids.size.toString();
  }

  if (filter_available) {
    filter_available.addEventListener('input', render);
  }
  if (filter_whitelist) {
    filter_whitelist.addEventListener('input', render);
  }

  const saveButton = document.getElementById('save-button');
  if (saveButton) {
    saveButton.addEventListener('click', () => {
      vscode.postMessage({ command: 'saveWhitelist', whitelist: Array.from(whitelisted_ids) });
    });
  }

  const resetButton = document.getElementById('reset-button');
  if (resetButton) {
    resetButton.addEventListener('click', () => {
      vscode.postMessage({ command: 'resetWhitelist' });
    });
  }

  const autosyncButton = document.getElementById('autosync-button');
  if (autosyncButton) {
    autosyncButton.addEventListener('click', () => {
      vscode.postMessage({ command: 'autoSyncCommands' });
    });
  }

  window.addEventListener('message', event => {
    const message = event.data;
    if (message.command === 'loadCommands') {
      all_commands = message.all_commands || [];
      whitelisted_ids = new Set(message.whitelist || []);
      render();
    }
  });
})();
