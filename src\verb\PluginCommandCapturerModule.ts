import * as vscode from 'vscode';

export interface PluginCommand {
  id: string;
  plugin_name: string;
  command_name: string;
  category: string;
  is_builtin: boolean;
  is_extension: boolean;
}

export interface PluginInfo {
  name: string;
  command_count: number;
  commands: PluginCommand[];
  is_active: boolean;
}

interface PackageJson {
  is_builtin?: boolean;
}

/**
 * @description 专门用于捕获和分析VS Code插件命令的模块
 */
export class PluginCommandCapturerModule {
  /**
   * @description 捕获所有可用的VS Code命令，包括插件命令
   * @returns 所有命令的列表
   */
  public async capture_all_commands(): Promise<string[]> {
    try {
      // 使用 getCommands(true) 获取所有可发现的命令，包括插件命令
      const all_commands = await vscode.commands.getCommands(true);
      return all_commands;
    } catch {
      // Silently fail and return an empty array.
      return [];
    }
  }

  /**
   * @description 分析命令并分类
   * @param commands 命令列表
   * @returns 分类后的命令信息
   */
  public analyze_commands(commands: string[]): {
    builtin_commands: PluginCommand[];
    extension_commands: PluginCommand[];
    plugin_groups: Map<string, PluginInfo>;
  } {
    const builtin_commands: PluginCommand[] = [];
    const extension_commands: PluginCommand[] = [];
    const plugin_groups = new Map<string, PluginInfo>();

    commands.forEach(cmd_id => {
      const command = this.parse_command(cmd_id);

      if (command.is_builtin) {
        builtin_commands.push(command);
      } else {
        extension_commands.push(command);

        // 按插件分组
        if (!plugin_groups.has(command.plugin_name)) {
          plugin_groups.set(command.plugin_name, {
            name: command.plugin_name,
            command_count: 0,
            commands: [],
            is_active: true,
          });
        }

        const plugin_info = plugin_groups.get(command.plugin_name)!;
        plugin_info.command_count++;
        plugin_info.commands.push(command);
      }
    });

    return { builtin_commands, extension_commands, plugin_groups };
  }

  /**
   * @description 解析单个命令ID
   * @param commandId 命令ID
   * @returns 解析后的命令对象
   */
  public parse_command(command_id: string): PluginCommand {
    const parts = command_id.split('.');
    let plugin_name = 'built-in';
    let command_name = command_id;
    let category = 'other';
    const is_builtin = !command_id.includes('.');

    if (!is_builtin) {
      // 尝试更智能地解析插件名称和命令
      if (parts.length > 1) {
        // 假设第一个部分是插件作者或插件名
        plugin_name = parts[0];
        command_name = parts.slice(1).join('.');
      }

      // 尝试从命令中提取类别
      if (command_name.includes(':')) {
        const cmd_parts = command_name.split(':');
        category = cmd_parts[0];
        command_name = cmd_parts[1];
      }
    }

    return {
      id: command_id,
      plugin_name,
      command_name,
      category,
      is_builtin,
      is_extension: !is_builtin,
    };
  }

  /**
   * @description 激活所有插件
   * @returns 所有插件的列表
   */
  public async activate_all_plugins(): Promise<vscode.Extension<unknown>[]> {
    const all_plugins = vscode.extensions.all.filter(ext => {
      const package_json = ext.packageJSON as PackageJson;
      return !package_json.is_builtin;
    });

    for (const plugin of all_plugins) {
      if (!plugin.isActive) {
        try {
          await plugin.activate();
        } catch {
          // Silently fail for any extensions that fail to activate.
        }
      }
    }
    return all_plugins;
  }

  /**
   * @description 获取所有插件的信息
   * @returns 所有插件的信息
   */
  public async get_all_plugins_info(): Promise<PluginInfo[]> {
    const all_plugins = await this.activate_all_plugins();
    const all_commands = await this.capture_all_commands();
    const { plugin_groups } = this.analyze_commands(all_commands);

    return all_plugins.map(ext => {
      const info = plugin_groups.get(ext.id);
      return {
        name: ext.id,
        command_count: info ? info.command_count : 0,
        commands: info ? info.commands : [],
        is_active: ext.isActive,
      };
    });
  }

  /**
   * @description 搜索命令
   * @param query 搜索查询
   * @returns 匹配的命令列表
   */
  public async search_commands(query: string): Promise<PluginCommand[]> {
    const all_commands = await this.capture_all_commands();
    const { builtin_commands, extension_commands } = this.analyze_commands(all_commands);

    const all_plugin_commands = [...builtin_commands, ...extension_commands];
    const lower_query = query.toLowerCase();

    return all_plugin_commands.filter(
      cmd =>
        cmd.id.toLowerCase().includes(lower_query) ||
        cmd.plugin_name.toLowerCase().includes(lower_query) ||
        cmd.command_name.toLowerCase().includes(lower_query) ||
        cmd.category.toLowerCase().includes(lower_query),
    );
  }

  /**
   * @description 生成命令统计报告
   * @returns 统计报告
   */
  public async generate_report(): Promise<{
    total_commands: number;
    builtin_commands: number;
    extension_commands: number;
    active_plugins: number;
    top_plugins: PluginInfo[];
    categories: Map<string, number>;
  }> {
    const all_commands = await this.capture_all_commands();
    const { builtin_commands, extension_commands, plugin_groups } = this.analyze_commands(all_commands);

    // 统计类别
    const categories = new Map<string, number>();
    [...builtin_commands, ...extension_commands].forEach(cmd => {
      const count = categories.get(cmd.category) || 0;
      categories.set(cmd.category, count + 1);
    });

    // 获取前10个插件
    const top_plugins = Array.from(plugin_groups.values())
      .sort((a, b) => b.command_count - a.command_count)
      .slice(0, 10);

    return {
      total_commands: all_commands.length,
      builtin_commands: builtin_commands.length,
      extension_commands: extension_commands.length,
      active_plugins: plugin_groups.size,
      top_plugins,
      categories,
    };
  }

  /**
   * @description 验证命令是否可执行
   * @param commandId 命令ID
   * @returns 是否可执行
   */
  public async is_command_executable(command_id: string): Promise<boolean> {
    try {
      const all_commands = await this.capture_all_commands();
      return all_commands.includes(command_id);
    } catch {
      return false;
    }
  }
}
