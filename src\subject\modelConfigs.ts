import * as fs from 'fs';
import * as path from 'path';
import { z } from 'zod';

// ====================================================================================
// 统一配置模板系统 - 每个模型配置包含完整的能力描述
// ====================================================================================

/**
 * 工具能力定义
 */
const tool_capability_schema = z.object({
  name: z.string(),
  description: z.string(),
  parameters: z.record(z.unknown()),
  enabled: z.boolean().default(true),
});

/**
 * 提示词模板定义
 */
const prompt_template_schema = z.object({
  system_prompt: z.string(),
  user_prompt_template: z.string(),
  tool_instruction_template: z.string(),
  context_template: z.string(),
});

/**
 * UI配置定义
 */
const ui_config_schema = z.object({
  panel_title: z.string(),
  panel_icon: z.string().optional(),
  theme_color: z.string().optional(),
  welcome_message: z.string().optional(),
});

/**
 * 基础模型配置
 */
const base_model_config_schema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),

  // 能力配置
  capabilities: z.object({
    max_tokens: z.number().optional(),
    supports_streaming: z.boolean().default(false),
    supports_function_calling: z.boolean().default(true),
    supports_vision: z.boolean().default(false),
    supports_code_execution: z.boolean().default(false),
  }),

  // 工具配置
  available_tools: z.array(tool_capability_schema).default([]),

  // 提示词配置
  prompt_templates: prompt_template_schema,

  // UI配置
  ui_config: ui_config_schema,
});

/**
 * Mistral API配置
 */
const mistral_config_schema = base_model_config_schema.extend({
  api_type: z.literal('mistral'),
  api_config: z.object({
    api_key: z.string(),
    base_url: z.string(),
    model: z.string(),
    timeout: z.number().default(30000),
  }),
});

/**
 * Spark API配置
 */
const spark_config_schema = base_model_config_schema.extend({
  api_type: z.literal('spark'),
  api_config: z.object({
    ws_url: z.string(),
    api_secret: z.string(),
    api_key: z.string(),
    appid: z.string(),
    domain: z.string(),
    timeout: z.number().default(30000),
  }),
});

/**
 * OpenAI API配置
 */
const openai_config_schema = base_model_config_schema.extend({
  api_type: z.literal('openai'),
  api_config: z.object({
    api_key: z.string(),
    base_url: z.string(),
    model: z.string(),
    organization: z.string().optional(),
    timeout: z.number().default(30000),
  }),
});

export const model_config_schema = z.discriminatedUnion('api_type', [
  mistral_config_schema,
  spark_config_schema,
  openai_config_schema,
]);

// ====================================================================================
// Type Definitions Inferred from Zod Schemas
// ====================================================================================

export type ToolCapability = z.infer<typeof tool_capability_schema>;
export type PromptTemplate = z.infer<typeof prompt_template_schema>;
export type UIConfig = z.infer<typeof ui_config_schema>;
export type MistralConfig = z.infer<typeof mistral_config_schema>;
export type SparkConfig = z.infer<typeof spark_config_schema>;
export type OpenAIConfig = z.infer<typeof openai_config_schema>;
export type ModelConfig = z.infer<typeof model_config_schema>;

// ====================================================================================
// 配置模板生成器
// ====================================================================================

/**
 * 生成默认的配置模板
 */
export function generate_config_template(api_type: 'mistral' | 'spark' | 'openai', model_id: string): Partial<ModelConfig> {
  const base_template = {
    id: model_id,
    name: model_id.charAt(0).toUpperCase() + model_id.slice(1),
    description: `${api_type.toUpperCase()} API 配置`,
    version: '1.0.0',

    capabilities: {
      max_tokens: 4096,
      supports_streaming: false,
      supports_function_calling: true,
      supports_vision: false,
      supports_code_execution: false,
    },

    available_tools: [
      {
        name: 'reply_to_user',
        description: '直接回复用户消息',
        parameters: {
          type: 'object',
          properties: {
            message: { type: 'string', description: '回复内容' }
          },
          required: ['message']
        },
        enabled: true,
      },
      {
        name: 'search_commands',
        description: '搜索VS Code命令',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: '搜索关键词' }
          },
          required: ['query']
        },
        enabled: true,
      }
    ],

    prompt_templates: {
      system_prompt: `你是一个VS Code智能助手，可以帮助用户执行各种编程任务。你可以使用以下工具来完成用户的请求。`,
      user_prompt_template: `用户请求: {{user_request}}\n\n当前环境:\n- 工作区: {{workspace_root}}\n- 活动文件: {{active_file_path}}\n- 选中文本: {{selected_text}}`,
      tool_instruction_template: `请使用JSON格式返回工具调用指令:\n{\n  "tool_name": "工具名称",\n  "parameters": { "参数名": "参数值" }\n}`,
      context_template: `## 可用工具\n{{available_tools}}\n\n## 环境信息\n{{context_info}}\n\n## 用户请求\n{{user_request}}`
    },

    ui_config: {
      panel_title: `${model_id.charAt(0).toUpperCase() + model_id.slice(1)} Chat`,
      panel_icon: 'comment-discussion',
      theme_color: '#007ACC',
      welcome_message: `欢迎使用 ${api_type.toUpperCase()} 智能助手！我可以帮助您完成各种VS Code任务。`
    }
  };

  // 根据API类型添加特定配置
  switch (api_type) {
    case 'mistral':
      return {
        ...base_template,
        api_type: 'mistral' as const,
        api_config: {
          api_key: 'YOUR_MISTRAL_API_KEY',
          base_url: 'https://api.mistral.ai/v1/chat/completions',
          model: 'mistral-large-latest',
          timeout: 30000,
        }
      };

    case 'spark':
      return {
        ...base_template,
        api_type: 'spark' as const,
        api_config: {
          ws_url: 'wss://spark-api.xf-yun.com/v1/x1',
          api_secret: 'YOUR_API_SECRET',
          api_key: 'YOUR_API_KEY',
          appid: 'YOUR_APP_ID',
          domain: 'x1',
          timeout: 30000,
        }
      };

    case 'openai':
      return {
        ...base_template,
        api_type: 'openai' as const,
        api_config: {
          api_key: 'YOUR_OPENAI_API_KEY',
          base_url: 'https://api.openai.com/v1/chat/completions',
          model: 'gpt-4',
          timeout: 30000,
        }
      };
  }
}

// ====================================================================================
// Configuration Loading and Exporting
// ====================================================================================

function load_json_config(file_path: string): unknown {
  const full_path = path.join(__dirname, file_path);
  const file_contents = fs.readFileSync(full_path, 'utf-8');
  return JSON.parse(file_contents);
}

function load_model_configs(): ModelConfig[] {
  // 动态扫描config目录下的所有配置文件
  const config_dir = path.join(__dirname, '../config');
  let config_files: string[] = [];

  try {
    const files = fs.readdirSync(config_dir);
    console.log('🔍 扫描到的配置文件:', files);
    config_files = files
      .filter(file => file.endsWith('_config.json'))
      .map(file => `../config/${file}`);
    console.log('🔍 过滤后的配置文件:', config_files);
  } catch (error) {
    console.warn('无法读取config目录，使用默认配置文件列表:', error);
    // 回退到硬编码的配置文件列表
    config_files = [
      '../config/mistral_config.json',
      '../config/xfyun_spark_config.json',
    ];
  }
  const all_raw_configs: unknown[] = [];
  for (const file of config_files) {
    const raw = load_json_config(file);
    if (Array.isArray(raw)) {
      for (const item of raw) {
        all_raw_configs.push(item);
      }
    } else if (typeof raw === 'object' && raw !== null) {
      all_raw_configs.push(raw);
    }
  }
  // 统一基础schema校验
  const base_schema = z.object({ id: z.string(), name: z.string(), api_type: z.string() });
  for (const model_config_raw of all_raw_configs) {
    if (typeof model_config_raw !== 'object' || model_config_raw === null) {
      throw new Error(`模型配置不是对象: ${JSON.stringify(model_config_raw)}`);
    }
    try {
      base_schema.parse(model_config_raw);
    } catch (e) {
      const error_message = e instanceof Error ? e.message : String(e);
      throw new Error(`模型配置缺少基础字段: ${JSON.stringify(model_config_raw)}\n错误: ${error_message}`);
    }
  }
  // 按api_type分发到专用schema
  const configs: ModelConfig[] = [];
  for (const model_config_raw of all_raw_configs) {
    if (typeof model_config_raw !== 'object' || model_config_raw === null) {
      continue;
    }
    try {
      const cfg = model_config_raw as { api_type: string; id?: string; name?: string };
      switch (cfg.api_type) {
        case 'mistral':
          configs.push(mistral_config_schema.parse(model_config_raw));
          break;
        case 'spark':
          configs.push(spark_config_schema.parse(model_config_raw));
          break;
        case 'openai':
          configs.push(openai_config_schema.parse(model_config_raw));
          break;
        default:
          throw new Error(`未知api_type: ${String(cfg.api_type)}`);
      }
    } catch (e) {
      const error_message = e instanceof Error ? e.message : String(e);
      const cfg = model_config_raw as { id?: string; name?: string; api_type?: string };
      throw new Error(
        `模型(id=${String(cfg.id)}, name=${String(cfg.name)}, api_type=${String(cfg.api_type)})配置校验失败: ${error_message}`,
      );
    }
  }
  return configs;
}

export const all_model_configs = new Map<string, ModelConfig>(load_model_configs().map(config => [config.id, config]));
