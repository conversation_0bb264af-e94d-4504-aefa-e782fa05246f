import type { Zod<PERSON>ype<PERSON>ny } from 'zod';

import type { ContextPackage } from '../object/dataModels.js';

/**
 * @description Represents a composable module in the processing pipeline.
 * Each module takes the entire context package and returns a modified version of it.
 */
export interface IComposableModule {
  execute(context: ContextPackage): Promise<ContextPackage>;
}

/**
 * @description Defines the structure of a tool that can be executed by the system.
 */
export interface Tool {
  description: string;
  parameters_schema: ZodTypeAny;
  handler: (parameters: unknown) => Promise<unknown>;
}

/**
 * @description A map of tool names to their corresponding Tool definitions.
 */
export type ToolMap = Map<string, Tool>;
