MEM0_API_KEY:m0-1dr4hg07KNIPWaXCNJ7YxKMcLkzVx62oVVfSoTff
gemini API key:AIzaSyB66wIbZKJJR4fgrgf59CKkaELXG67WDdE
openAI  API:********************************************************************************************************************************************************************

upstashAPI:8566e0bb-870e-471e-9663-50790b74b430
        "UPSTASH_EMAIL": "<EMAIL>",
        "UPSTASH_API_KEY": "8566e0bb-870e-471e-9663-50790b74b430"
anthropicAPI:************************************************************************************************************
exa.ai/api-keys:98914d14-e0c2-48c4-994e-41b4bcef7b53
 Search1API:F9B85E5E-8EF4-4752-B41E-4E7C01B6A1A9
firecrawl APIfirecrawl:fc-23ccb784164b4af5bf62a1f5c9cca3fd
cloud.nx key:npx nx-cloud configure --personal-access-token ZTNiMWYyODYtNDM5Zi00MmQ3LWJlN2EtMzQ5YTBmYmM5Yjc0
figma key:*********************************************
"name": "SiliconFlow",
  "api_key": "sk-jexocxnjnjwlchqwxwshlcnjbawcvrvldcjulxepaonyerju",
  "base_url": "https://api.siliconflow.cn/v1",
  "models": [
    "Qwen/QwQ-32B",
    "Qwen/QwQ-7B",
    "Qwen/QwQ-Max"
  ],
  "default_model": "Qwen/QwQ-32B",
"name": "openrouter",
    "api_key": "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",
    "base_url": "https://openrouter.ai/api/v1/chat/completions",
    "model": "deepseek/deepseek-chat-v3-0324:free",
from openai import OpenAI

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="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",
)

completion = client.chat.completions.create(
  extra_headers={
    "HTTP-Referer": "<YOUR_SITE_URL>", # Optional. Site URL for rankings on openrouter.ai.
    "X-Title": "<YOUR_SITE_NAME>", # Optional. Site title for rankings on openrouter.ai.
  },
  extra_body={},
  model="google/gemini-2.0-flash-exp:free",
  messages=[
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "What is in this image?"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
          }
        }
      ]
    }
  ]
)
print(completion.choices[0].message.content)
"name": "cloudflare",
    "api_key": "c3bca45b1fb78720ccbae68aab295b064a77b",
    "base_url": "https://api.cloudflare.com/client/v4/accounts/edfc96ded4826f3aed180debc6331126/ai/run/",


"name": "gemini",
    "api_key": "zaSyB66wIbZKJJR4fgrgf59CKkaELXG67WDdE",
    "base_url": "https://generativelanguage.googleapis.com/v1beta/models/",


"name": "deepseek",
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com/v1/chat/completions",


name": "mistral",
    "api_key": "tsJOAMaKB2dZN8jitd0QM7n2BCg12kdM",
    "base_url": "https://api.mistral.ai/v1/chat/completions",
    "model": "mistral-large-latest",


name": "deepseek",
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com/v1/chat/completions",


"name": "E2B API",
            "api_key": "e2b_7a0f53892160e53d10bb817491d4f04eb95a09e7",
            "base_url": "https://api.e2b.dev/v1/chat/completions",

chatanywhere:
https://api.chatanywhere.tech/v1/chat/completions

pollinations.ai
url:https://pollinations.ai/
## Pollinations.AI Cheatsheet for Coding Assistants

### Image Generation
Generate Image: `GET https://image.pollinations.ai/prompt/{prompt}`

### Image Models
List Models: `GET https://image.pollinations.ai/models`

### Text Generation
Generate (GET): `GET https://text.pollinations.ai/{prompt}`

### Text Generation (Advanced)
Generate (POST): `POST https://text.pollinations.ai/`

### Audio Generation
Generate Audio: `GET https://text.pollinations.ai/{prompt}?model=openai-audio&voice={voice}`

### OpenAI Compatible Endpoint
OpenAI Compatible: `POST https://text.pollinations.ai/openai`

### Text Models
List Models: `GET https://text.pollinations.ai/models`

### Real-time Feeds
Image Feed: `GET https://image.pollinations.ai/feed`
Text Feed: `GET https://text.pollinations.ai/feed`
*\* required parameter*
MCP
# Model Context Protocol (MCP) Server for AI Assistants

The Pollinations MCP server enables AI assistants like Claude to generate images and audio directly.

## Installation & Usage

```bash
# Run with npx (no installation required)
npx @pollinations/model-context-protocol
```

## Features

- Generate images from text descriptions
- Create text-to-speech audio with various voice options
- List available models and capabilities
- No authentication required

## Example Usage (Node.js)

```javascript
import { generateImageUrl, generateImage, respondAudio, sayText } from '@pollinations/model-context-protocol';

// Generate an image URL
const imageResult = await generateImageUrl('A beautiful sunset over the ocean', {
  width: 512,
  height: 512,
  model: 'flux.schnell'
});
console.log(imageResult.imageUrl);

// Generate audio from text
const audioResult = await respondAudio('Hello, world!', 'alloy');
// Audio will be played automatically
```

For more details, see the [MCP Server Documentation](https://github.com/pollinations/pollinations/tree/master/model-context-protocol).

https://developer.wolframalpha.com
API:6T2TVY-2Q2RXW7UUV