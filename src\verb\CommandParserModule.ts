import type { ContextPackage, LlmCommand } from '../object/dataModels.js';
import type { IComposableModule } from './moduleInterfaces.js';

/**
 * @description A verb module responsible for parsing and validating commands from the LLM.
 */
export class CommandParserModule implements IComposableModule {
  public execute(context: ContextPackage): Promise<ContextPackage> {
    if (!context.tool_executions || context.tool_executions.length === 0) {
      // No command to parse, pass through.
      return Promise.resolve(context);
    }

    const last_execution = context.tool_executions[context.tool_executions.length - 1];
    const command = last_execution.command as LlmCommand | null;

    if (
      command &&
      typeof command.tool_name === 'string' &&
      typeof command.parameters === 'object' &&
      command.parameters !== null
    ) {
      // The command is valid, do nothing.
    } else {
      // The command is not valid.
      last_execution.is_success = false;
      last_execution.error = 'Invalid command structure received from LLM.';
    }

    return Promise.resolve(context);
  }
}
