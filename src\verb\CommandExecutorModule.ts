import * as vscode from 'vscode';

import type { ContextPackage, ExecutionResult } from '../object/dataModels.js';
import type { IComposableModule, ToolMap } from './moduleInterfaces.js';

/**
 * @description Executes the latest command in the context package.
 */
export class CommandExecutorModule implements IComposableModule {
  private readonly all_tools: ToolMap;

  constructor(all_tools: ToolMap) {
    this.all_tools = all_tools;
  }

  public async execute(context: ContextPackage): Promise<ContextPackage> {
    if (!context.tool_executions || context.tool_executions.length === 0) {
      // No command to execute, pass through.
      return context;
    }

    // Get the last execution object, which should contain the command to be executed.
    const last_execution_index = context.tool_executions.length - 1;
    const execution_to_process: ExecutionResult = context.tool_executions[last_execution_index];

    // If it already has an output or error, it means it has been executed.
    if (execution_to_process.output || execution_to_process.error) {
      return context;
    }

    const { command } = execution_to_process;
    const tool = this.all_tools.get(command.tool_name);

    if (!tool) {
      const error_msg = `Error: Tool "${command.tool_name}" is not a known tool.`;
      void vscode.window.showErrorMessage(error_msg);
      execution_to_process.is_success = false;
      execution_to_process.error = error_msg;
    } else {
      try {
        // Validate parameters against the tool's Zod schema
        const parsed_params: unknown = tool.parameters_schema.parse(command.parameters);
        const output = await tool.handler(parsed_params);

        execution_to_process.is_success = true;
        execution_to_process.output = typeof output === 'string' ? output : JSON.stringify(output);
      } catch (e) {
        const error_message = e instanceof Error ? e.message : String(e);
        void vscode.window.showErrorMessage(`Command execution failed: ${error_message}`);
        execution_to_process.is_success = false;
        execution_to_process.error = error_message;
      }
    }

    // Update the context with the processed execution result
    context.tool_executions[last_execution_index] = execution_to_process;
    return context;
  }
}
