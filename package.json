{"name": "llm-bridge", "publisher": "long", "displayName": "LLM Bridge", "description": "A bridge for external LLMs to interact with the VS Code environment.", "version": "0.0.1", "icon": "images/icon.png", "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "command_manager", "title": "MCP: Command Manager"}, {"command": "prompt_manager", "title": "MCP: Prompt Manager"}, {"command": "long_running_tool", "title": "MCP: <PERSON> Tool"}, {"command": "llm-bridge.start", "title": "LLM Bridge: Start New Task"}, {"command": "llm-bridge.recaptureCommands", "title": "LLM Bridge: Recapture Available Commands"}, {"command": "llm-bridge.showCapturedCommands", "title": "LLM Bridge: Show Captured Commands"}, {"command": "llm-bridge.manageCommands", "title": "LLM Bridge: Manage Command Whitelist"}, {"command": "llm-bridge.<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "管理白名单"}, {"command": "llm-bridge.addConfig", "title": "添加配置"}, {"command": "llm-bridge.openSettings", "title": "设置"}, {"command": "myExtension.showWebview", "title": "Show My Webview"}], "viewsContainers": {"activitybar": [{"id": "llm-bridge-container", "title": "LLM Bridge", "icon": "media/icon.svg"}]}, "views": {"llm-bridge-container": [{"id": "chat<PERSON><PERSON><PERSON>-mistral", "name": "MISTRAL", "icon": "media/icon.svg", "type": "webview"}, {"id": "chatPanel-spark", "name": "XFYUN SPARK", "icon": "media/icon.svg", "type": "webview"}]}, "menus": {"view/container/title": [{"command": "llm-bridge.addConfig", "title": "添加配置", "icon": "$(add)", "group": "navigation@1"}, {"command": "llm-bridge.openSettings", "title": "设置", "icon": "$(settings-gear)", "group": "navigation@2"}], "view/title": []}}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@types/cors": "^2.8.19", "axios": "^1.7.2", "cors": "^2.8.5", "express": "^5.1.0", "uuid": "^9.0.1", "ws": "^8.18.3"}, "scripts": {"vscode:prepublish": "npm run compile", "copy-assets": "xcopy media dist\\media /S /I /Y && xcopy config dist\\config /S /I /Y", "compile": "npm run copy-assets && tsc -p ./", "watch": "tsc -watch -p ./", "lint": "eslint \"src/**/*.ts\" --fix", "lint:glue": "bash lint-glue.sh"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "20.x", "@types/uuid": "^10.0.0", "@types/vscode": "^1.80.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "prettier": "^3.2.5", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1"}}