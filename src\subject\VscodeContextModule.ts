import * as vscode from 'vscode';

/**
 * @description Represents the VS Code environment, providing access to its state.
 */
export class VscodeContextModule {
  public get workspace_root(): string | null {
    const folders = vscode.workspace.workspaceFolders;
    return folders && folders.length > 0 ? folders[0].uri.fsPath : null;
  }

  public get active_file_path(): string | null {
    return vscode.window.activeTextEditor?.document.uri.fsPath || null;
  }

  public get selected_text(): string | null {
    const editor = vscode.window.activeTextEditor;
    return editor ? editor.document.getText(editor.selection) : null;
  }
}
