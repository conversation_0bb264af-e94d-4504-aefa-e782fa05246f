/**
 * @description Represents a command parsed from the LLM's output.
 */
export interface LlmCommand {
  tool_name: string;
  parameters: Record<string, unknown>;
}

/**
 * @description Represents a tool available to the LLM.
 */
export interface LlmTool {
  name: string;
  description: string;
  parameters: object;
}

/**
 * @description Represents the result of a tool's execution.
 */
export interface ExecutionResult {
  is_success: boolean;
  command: LlmCommand;
  output: unknown;
  error?: string;
}

/**
 * @description Represents the complete context captured from the editor and user input.
 * This package is mutated and passed through the processing pipeline.
 */
export interface ContextPackage {
  // --- Initial context from editor ---
  user_request: string;
  full_text: string;
  selected_text: string;
  file_path: string | null;
  active_file_path: string | null;
  workspace_root: string | null;

  // --- Data added during pipeline execution ---
  model_key?: string;
  model_config?: any; // 模型配置对象，包含API设置、工具定义、提示词模板等
  available_tools?: LlmTool[];
  prompt_string?: string;
  tool_executions?: ExecutionResult[];
}

/**
 * @description Represents a command parsed from the LLM's output.
 * @deprecated Use LlmCommand instead.
 */
export type ParsedCommand = LlmCommand;

/**
 * @description Represents a single message in a chat conversation.
 */
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}
