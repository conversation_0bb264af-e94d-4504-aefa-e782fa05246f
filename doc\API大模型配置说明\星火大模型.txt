讯飞开放平台https://www.xfyun.cn/doc/spark/Web.html#_1-%E6%8E%A5%E5%8F%A3%E8%AF%B4%E6%98%8E
APIPassword:ClkATfbSHoKqleBWEQbR:EkAmkrTtQEbcCpVaiQUk
https://spark-api-open.xf-yun.com/v1/chat/completions

Websocket服务接口认证信息
鉴权信息
APPID
3f86f9c9
APISecret
ZWU5ZjMyMjI0N2RmZWM2Yzc5YzdkOWE4
APIKey
e2d8e0181478abaeffddb3dba610e0d5
服务信息
Spark Max
Websocket
wss://spark-api.xf-yun.com/v3.5/chat

配置示例
Content-Type: application/json
Authorization: Bearer 123456
curl -i -k -X POST 'https://spark-api-open.xf-yun.com/v1/chat/completions' \
--header 'Authorization: Bearer 123456' \#注意此处把“123456”替换为自己的APIPassword
--header 'Content-Type: application/json' \
--data '{
    "model":"generalv3.5",
    "messages": [
        {
            "role": "user",
            "content": "来一个只有程序员能听懂的笑话"
        }
    ],
    "stream": true
}'
#3.2. 请求参数
{
    "model": "generalv3.5",
    "user": "用户唯一id",
    "messages": [
        {
            "role": "system",
            "content": "你是知识渊博的助理"
        },
        {
            "role": "user",
            "content": "你好，讯飞星火"
        }
    ],
    // 下面是可选参数
    "temperature": 0.5,
    "top_k": 4,
    "stream": false,
    "max_tokens": 1024,
    "presence_penalty": 1,
    "frequency_penalty": 1,
    "tools": [
        {
            "type": "function",
            "function": {
                "name": "str2int",
                "description": "将字符串类型转为 int 类型",
                "parameters": {...} // 需要符合 json schema 格式
            }
        },
        {
            "type": "web_search",
            "web_search": {
                "enable": true
                "show_ref_label":true
                "search_mode":"deep" // deep:深度搜索 / normal:标准搜索,不同的搜索策略，效果不同，并且token消耗也有差异
            }
        }
    ],
    "response_format": {
        "type": "json_object"
    },
    "suppress_plugin": [
        "knowledge"
    ]
}

参数名称	类型	是否必传	取值范围	描述
model	string	是	lite
generalv3
pro-128k
generalv3.5
max-32k
4.0Ultra	指定访问的模型版本:
lite指向Lite版本;
generalv3指向Pro版本;
pro-128k指向Pro-128K版本;
generalv3.5指向Max版本;
max-32k指向Max-32K版本;
4.0Ultra指向4.0 Ultra版本;
user	string	否	自定义	用户的唯一id，表示一个用户，user_123456
messages	array	是		输入数组
messages.role	string	是	user
assistant
system
tool	角色，user表示用户，assistant表示大模型，system表示命令，tool代表function call执行结果
messages.content	string	是		角色对应的文本内容
temperature	float	否	取值范围[0, 2] 默认值1.0	核采样阈值
top_p	int	否	取值范围(0, 1] 默认值1	生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值越大，生成的随机性越高；取值越低，生成的确定性越高。
top_k	int	否	取值范围[1, 6] 默认值4	从k个中随机选择一个(非等概率)
presence_penalty	float	否	取值范围[-2.0,2.0] 默认0	重复词的惩罚值
frequency_penalty	float	否	取值范围[-2.0,2.0] 默认0	频率惩罚值
stream	bool	否	true
false	是否流式返回结果。默认是false 表示非流式。 如果使用流式，服务端使用SSE的方式推送结果，客户端自己适配处理结果。
max_tokens	int	否	Pro、Max、Max-32K、4.0 Ultra 取值为[1,8192]，默认为4096;
Lite、Pro-128K 取值为[1,4096]，默认为4096。	模型回答的tokens的最大长度
response_format	object	否		指定模型的输出格式
response_format.type	string	否	text
json_object	{ "type": "json_object" } 指定模型输出json格式
使用 JSON 模式时，请始终指示模型通过对话中的某些消息（例如通过系统或用户消息）生成 JSON
tools	array	否	在这里用户可以控制联网搜索工具或者自定义各类function	工具列表
tools.web_search	object	否，默认表示开启	

{
  "type": "web_search",
  "web_search": {
  "enable": true,
  "show_ref_label": true,
  "search_mode": "deep"
  }
 }
仅Pro、Max、Ultra系列模型支持
tools.web_search.enable	bool	否，默认开启（true）	true or false	
enable：是否开启搜索功能，设置为true,模型会根据用户输入判断是否触发联网搜索，false则完全不触发；
tools.web_search.show_ref_label	bool	否，默认关闭（false）	true or false	show_ref_label 开关控制触发联网搜索时是否返回信源信息（仅在enable为true时生效）
如果开启，则先返回搜索结果，之后再返回模型回复内容
tools.web_search.search_mode	string	否，默认标准搜索（normal）	deep/normal	search_mode 控制联网搜索策略（仅在enable为true时生效）
normal：标准搜索模式，模型引用搜索返回的摘要内容回答问题
deep：深度搜索模式，模型引用搜索返回的全文内容，回复的更准确；同时会带来额外的token消耗（返回search_prompt字段）
tools.function	object	否	示例：
{"type":"function", "function":{"name": "my_function", "description": "xxx", "parameters": {...}}}
tools.function.name	string	是	用户自定义，长度不超过32
工具函数的名称，必须是字母、数字，可以包含下划线
tools.function.description	string	是	用户自定义	工具函数的描述，供模型选择何时调用该工具
tools.function.parameters	string	是	用户自定义
工具的参数描述，需要是一个合法的JSON Schema
tool_calls_switch	bool	否，默认表示关闭	true or false	设置为true时，触发function call结果中tool_calls以数组格式返回，默认为 false，则以json格式返回
tool_choice	string or object Optional	否	auto
none
required
{"type": "function", "function": {"name": "my_function"}}	设置模型自动选择调用的函数：
auto：传了tool时默认为auto，模型自动选择调用的函数
none：模型禁用函数调用
required：模型始终选择一个或多个函数进行调用
{"type": "function", "function": {"name": "my_function"}} ：模型强制调用指定函数
#4. 响应参数
请求错误时的响应格式：

API

{
    "error": {
        "message": "invalid user",
        "type": "api_error",
        "param": null,
        "code": null
    }
}


SDK

openai.AuthenticationError: Error code: 401 - {'error': {'message': 'invalid user', 'type': 'api_error', 'param': None, 'code': None}}
非流式请求成功时的响应格式：

{
    "code": 0,
    "message": "Success",
    "sid": "cha000b0003@dx1905cd86d6bb86d552",
    "choices": [
        {
            "message": {
                "role": "assistant",
                "content": "你好，我是由科大讯飞构建的星火认知智能模型。\n如果你有任何问题或者需要帮助的地方，请随时告诉我！我会尽力为你提供解答和支持。请问有什么可以帮到你的吗？"
            },
            "index": 0
        }
    ],
    "usage": {
        "prompt_tokens": 6,
        "completion_tokens": 42,
        "total_tokens": 48
    }
}
响应的参数说明：

参数名称	类型	描述
code	int	错误码：0表示成功，非0表示错误
message	string	错误码的描述信息
sid	string	本次请求的唯一id
choices	array	大模型结果的数组
choices.message	object	大模型结果
choices.message.role	string	大模型的角色
choices.message.content	string	大模型输出的内容
choices.index	int	大模型的结果序号，在多候选中使用
usage	object	本次请求消耗的token数量
usage.prompt_tokens	int	用户输入信息，消耗的token数量
usage.completion_tokens	int	大模型输出信息，消耗的token数量
usage.total_tokens	int	用户输入+大模型输出，总的token数量
流式请求成功时的响应格式：

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546385,"choices":[{"delta":{"role":"assistant","content":"你好"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546385,"choices":[{"delta":{"role":"assistant","content":"，很高兴"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546385,"choices":[{"delta":{"role":"assistant","content":"为你解答问题"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546385,"choices":[{"delta":{"role":"assistant","content":"。\n"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546387,"choices":[{"delta":{"role":"assistant","content":"我是讯飞星火认知大模型，由科大讯飞构建的认知智能系统。"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546388,"choices":[{"delta":{"role":"assistant","content":"我具备与人类进行自然交流的能力，可以高效地满足各领域的认知智能需求。"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546389,"choices":[{"delta":{"role":"assistant","content":"无论你有什么问题或者需要帮助的地方，我都将尽我所能提供支持和解决方案。请随时告诉我你的需求！"},"index":0}]}

data:{"code":0,"message":"Success","sid":"cha000b000c@dx1905cf38fc8b86d552","id":"cha000b000c@dx1905cf38fc8b86d552","created":1719546389,"choices":[{"delta":{"role":"assistant","content":""},"index":0}],"usage":{"prompt_tokens":6,"completion_tokens":68,"total_tokens":74}}

data:[DONE]


响应的参数说明：

参数名称	类型	描述
code	int	错误码：0表示成功，非0表示错误
message	string	错误码的描述信息
sid	string	本次请求的唯一id
choices	array	大模型结果的数组
choices.delta	object	大模型结果
choices.delta.role	string	大模型的角色
choices.delta.content	string	大模型输出的内容
choices.index	int	大模型的结果序号，在多候选中使用
usage	object	本次请求消耗的token数量
usage.prompt_tokens	int	用户输入信息，消耗的token数量
usage.completion_tokens	int	大模型输出信息，消耗的token数量
usage.total_tokens	int	用户输入+大模型输出，总的token数量
#5. HTTP非流式请求示例
import requests

url = "https://spark-api-open.xf-yun.com/v1/chat/completions"
data = {
        "model": "generalv3.5", # 指定请求的模型
        "messages": [
            {
                "role": "user",
                "content": "你是谁"
            }
        ],
        "tools": [
        {
            "type": "function",
            "function": {
                "name": "get_current_weather",
                "description": "返回实时天气",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "河北省承德市双桥区",
                        },
                        "format": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "使用本地区常用的温度单位计量",
                        },
                    },
                    "required": ["location", "format"],
                }
            }
        }
    ]
    }
header = {
    "Authorization": "Bearer 123456" # 注意此处把“123456”替换为自己的APIPassword
}
response = requests.post(url, headers=header, json=data)
print(response.text)
#6. HTTP流式请求示例
import requests

url = "https://spark-api-open.xf-yun.com/v1/chat/completions"
data = {
        "model": "generalv3.5", # 指定请求的模型
        "messages": [
            {
                "role": "user",
                "content": "你是谁"
            }
        ],
   		"stream": True
    }
header = {
    "Authorization": "Bearer 123456" # 注意此处把“123456”替换为自己的APIPassword
}
response = requests.post(url, headers=header, json=data, stream=True)

# 流式响应解析示例
response.encoding = "utf-8"
for line in response.iter_lines(decode_unicode="utf-8"):
    print(line)
#7. 使用OpenAI SDK请求示例
# 安装openai SDK 
pip install openai
# 导入SDK，发起请求
from openai import OpenAI
client = OpenAI(
				# 控制台获取key和secret拼接，假使控制台获取的APIPassword是123456
        api_key="123456", 
        base_url = 'https://spark-api-open.xf-yun.com/v1' # 指向讯飞星火的请求地址
    )
completion = client.chat.completions.create(
    model='generalv3.5', # 指定请求的版本
    messages=[
        {
            "role": "user",
            "content": '说一个程序员才懂的笑话'
        }
    ]
)
print(completion.choices[0].message)
#8. 错误码说明
错误码	错误信息
0	成功
10007	用户流量受限：服务正在处理用户当前的问题，需等待处理完成后再发送新的请求。（必须要等大模型完全回复之后，才能发送下一个问题）
10013	输入内容审核不通过，涉嫌违规，请重新调整输入内容
10014	输出内容涉及敏感信息，审核不通过，后续结果无法展示给用户
10019	表示本次会话内容有涉及违规信息的倾向；建议开发者收到此错误码后给用户一个输入涉及违规的提示
10907	token数量超过上限。对话历史+问题的字数太多，需要精简输入
11200	授权错误：该appId没有相关功能的授权 或者 业务量超过限制
11201	授权错误：日流控超限。超过当日最大访问量的限制
11202	授权错误：秒级流控超限。秒级并发超过授权路数限制
11203	授权错误：并发流控超限。并发路数超过授权路数限制


MSC API
MSC for Windows&Linux API
这里介绍的是Windows&Linux API全部文件的函数及其说明，点击右侧目录可快速找到相应文件。

#msp_cmn.h通用接口
通用接口(Mobile Speech Platform Common Interface Header File)

#函数
类型	说明
int MSPAPI	MSPLogin(const char *usr, const char *pwd, const char *params)
初始化msc，用户登录。
const char *MSPAPI	MSPUploadData(const char *dataName, void *data, unsigned int dataLen, const char*params, int *errorCode)
用户数据上传。
int MSPAPI	MSPLogout
退出登录。
int MSPAPI	MSPSetParam(const char *paramName, const char *paramValue)
参数设置接口、离线引擎初始化接口。
int MSPAPI	MSPGetParam(const char *paramName, char *paramValue, unsigned int *valueLen)
获取MSC的设置信息。
const char *MSPAPI	MSPGetVersion(const char *verName, int *errorCode)
获取MSC或本地引擎版本信息。
#详细描述
通用接口(Mobile Speech Platform Common Interface Header File)

This file contains the quick common programming interface (API) declarations of MSP. Developer can include this file in your project to build applications. For more information, please read the developer guide.

Use of this software is subject to certain restrictions and limitations set forth in a license agreement entered into between iFLYTEK, Co,LTD. and the licensee of this software. Please refer to the license agreement for license use rights and restrictions.

Copyright (C) by iFLYTEK, Co,LTD. All rights reserved.

#函数说明
#MSPLogin()
int MSPAPI MSPLogin (const char * 	usr,
                     const char * 	pwd,
                     const char * 	params 
                    )
初始化msc，用户登录。

参数：

usr[in]
此参数保留，传入NULL即可。

pwd[in]
此参数保留，传入NULL即可。

params[in]
参见下表：
格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

在线/离线业务	参数	名称	说明
通用	appid	应用ID	SDK申请成功后获取到的appid。申请SDK请前往讯飞开放平台 ，此参数必须传入
离线	engine_start	离线引擎启动	启动离线引擎，支持参数，ivw：唤醒，asr：识别
离线	[xxx]_res_path	离线引擎资源路径	设置ivw、asr引擎离线资源路径，详细格式如下：fo|[path]|[offset]|[length]|xx|xx。
示例如下,单个资源路径：
ivw_res_path=fo|res/ivw/wakeupresource.jet，
多个资源路径：asr_res_path=fo|res/asr/common.jet;fo|res/asr/sms.jet
返回
  成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  使用其他接口前必须先调用MSPLogin，可以在应用程序启动时调用。

参见：

    const char* usr = NULL;
    const char* pwd = NULL;
    const char* lgi_param = "appid = ********";
    int ret = MSPLogin(usr, pwd, lgi_param);
    if( MSP_SUCCESS != ret )
    {
        printf( "MSPLogin failed, error code is: %d", ret );
    }
#MSPUploadData()

const char* MSPAPI MSPUploadData (const char * 	dataName,
                                  void *        data,
                                  unsigned int 	dataLen,
                                  const char * 	params,
                                  int *         errorCode 
                                 )	
用户数据上传。

参数：

dataName[in]
数据名称字符串。

data[in]
待上传数据缓冲区的起始地址。

dataLen[in]
数据长度(如果是字符串，则不包含'\0')。

params[in]
目前支持以下四种。具体如下:

在线/离线业务	参数	功能	应用业务	文件编码
在线	"sub = uup,dtt = userword"	上传用户词表	iat	UTF-8
在线	"sub = uup,dtt = contact"	上传联系人	iat	UTF-8
返回
  上传成功后，联系人、用户词表功能返回值NULL。

备注
  使用其他接口前必须先调用MSPLogin，可以在应用程序启动时调用。

参见：

    const char* dataname = "userword";
    const char* params = "sub=uup,dtt=userword";
    const char* result = NULL;
    char* data = NULL;
    int data_len = 0;
    FILE* fp = fopen("userwords.txt ", "rb");
    if(NULL==fp)
    {
        ...   //错误处理
    }
    fseek(fp, 0, SEEK_END);
    data_len = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    data = (char*)malloc(data_len+1);
    if(NULL==data)
    {
        ...   //错误处理
    }
    data_len = fread(data,1,data_len,fp);
    if(data_len == 0)
    {
        ...   //错误处理
    }
    data[data_len] = '\0';
    result = MSPUploadData( dataName, data, data_len, params, &errorcode);
    if( MSP_SUCCESS != errorcode )
    {
        printf( "MSPUploadData failed, error code is: %d", ret );
    }
    fclose(fp);
    .
#MSPLogout()
int MSPAPI MSPLogout()
退出登录。

返回
  如果函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  本接口和MSPLogin配合使用。确保其他接口调用结束之后调用MSPLogout，否则结果不可预期。

参见：

int ret = MSPLogout( );
if(MSP_SUCCESS != ret)
{
    printf("MSPLogout failed, error code is: %d", ret);
}
#MSPSetParam()
int MSPAPI MSPSetParam(const char * paramName,
                       const char * paramValue 
                      )
参数设置接口、离线引擎初始化接口。

参数：

paramName[in]
参数名，可设置参数如下：

在线/离线业务	参数	名称	说明
离线	engine_start	engine_start 启动离线引擎	启动引擎：
设置paramName为engine_start
paramValue为engine_start=asr、ivw离线业务
之后加资源所在路径
例如engine_start=asr,asr_res_path=fo|file_path|[offset]|[length]
离线	engine_destroy	销毁离线引擎	销毁引擎：
设置paramName 为engine_destroy
paramValue 为asr 、tts 、ivw （ 如engine_destroy=tts）离线业务
paramValue[in]
参数值。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  无

参见：

const char* paramsName= "engine_start";
const char* paramsValue="engine_start = ivw,ivw_res_path =fo|res/ivw/wakeupresource.jet";
errorcode = MSPSetParam (paramsName, paramsValue);
if( MSP_SUCCESS != errorcode )
{
    printf( "MSPSetParam failed, error code is: %d", ret );
}
.
#MSPGetParam()

int MSPAPI MSPGetParam (const char * 	paramName,
                        char *          paramValue,
                        unsigned int * 	valueLen 
                       )	
获取MSC的设置信息。

参数：

paramName[in]
参数名，一次调用只支持查询一个参数。参数如下：

在线/离线业务	参数	描述
在线	upflow	上行数据量
在线	downflow	下行数据量
paramValue[in/out]
输入:buffer首地址
输出:向该buffer写入获取到的信息

valueLen[in/out]
输入:buffer的大小
输出:信息实际长度(不含'\0')

返回
  函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  以查询上行流量为例，获取到的是当前累计的上行流量。下行流量查询与此相似。

参见：

    const char* para_name = "upflow";
    char para_value[32] = {'\0'};
    unsigned int value_len = 32;
    int ret = MSPGetParam (para_name, para_value, &value_len);
    if(MSP_SUCCESS != ret)
    {
        printf("MSPGetParam failed, error code is: %d", ret);
    }
    .
#MSPGetVersion()
const char* MSPAPI MSPGetVersion (const char * 	verName,
                                  int *         errorCode 
                                 )	
获取MSC或本地引擎版本信息

参数：

verName[in]
参数名，一次调用只支持查询一个参数。参数如下：

在线/离线业务	参数	描述
离线	ver_msc	MSC版本号
离线	ver_asr	离线识别版本号，目前不支持
离线	ver_tts	离线合成版本号
离线	ver_ivw	离线唤醒版本号
errorCode[out]
如果函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

返回
  成功返回缓冲区指针，失败或数据不存在返回NULL。

备注
  使用其他接口前必须先调用MSPLogin，可以在应用程序启动时调用。

参见
  无

#qisr.h 语音识别
语音识别(iFLY Speech Recognizer Header File)

#qisr.h 文件参考
语音识别(iFLY Speech Recognizer Header File)

#函数
类型	说明
const char *MSPAPI	QISRSessionBegin(const char *grammarList, const char *params, int *errorCode)
开始一次语音识别。
int MSPAPI	QISRAudioWrite(const char *sessionID, const void *waveData, unsigned int waveLen, int audioStatus, int *epStatus, int *recogStatus)
写入本次识别的音频。
const char *MSPAPI	QISRGetResult(const char *sessionID, int *rsltStatus, int waitTime, int *errorCode)
获取识别结果。
int MSPAPI	QISRSessionEnd(const char *sessionID, const char *hints)
结束本次语音识别。
int MSPAPI	QISRGetParam(const char *sessionID, const char *paramName, char *paramValue, unsigned int *valueLen)
获取当次语音识别信息，如上行流量、下行流量等。
int MSPAPI	QISRBuildGrammar(const char *grammarType, const char *grammarContent, unsigned int grammarLength, const char *params, GrammarCallBack callback, void *userData)
构建语法，生成语法ID。
int MSPAPI	QISRUpdateLexicon(const char *lexiconName, const char *lexiconContent, unsigned int lexiconLength, const char *params, LexiconCallBack callback, void *userData)
更新本地语法词典。
#详细描述
语音识别(iFLY Speech Recognizer Header File)

This file contains the quick application programming interface (API) declarations of ISR. Developer can include this file in your project to build applications. For more information, please read the developer guide.

Use of this software is subject to certain restrictions and limitations set forth in a license agreement entered into between iFLYTEK, Co,LTD. and the licensee of this software. Please refer to the license agreement for license use rights and restrictions.

Copyright (C) by iFLYTEK, Co,LTD. All rights reserved.

#函数说明
#QISRSessionBegin()
const char* MSPAPI QISRSessionBegin (const char *   grammarList,
                                     const char *   params,
                                     int *          errorCode  
                                    )
开始一次语音识别。

参数：

grammarList[in]
此参数保留，传入NULL即可。

params[in]
参见下表:

格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

在线/离线业务	参数	名称	说明
通用	engine_type	引擎类型	可取值：
cloud：在线引擎
local：离线引擎
默认值：cloud
在线	sub	本次识别请求的类型	iat:语音听写
asr:命令词识别。
默认为iat
在线	language	语言	可取值：
zh_cn：简体中文
en_us：英文
默认值：zh_cn
在线	domain	领域	iat：语音听写（默认值）
在线	accent	语言区域	可取值：
mandarin：普通话(默认值)
其他方言请前往讯飞开放平台控制台查看
通用	sample_rate	音频采样率	可取值：16000，8000
默认值：16000
离线识别不支持8000采样率音频
离线	asr_threshold	识别门限	离线语法识别结果门限值，设置只返回置信度得分大于此门限值的结果
可取值：0~100，默认值：0
离线	asr_denoise	是否开启降噪功能	可取值：
0：不开启，1：开启
默认不开启
离线	asr_res_path	离线识别资源路径	离线识别资源所在路径，对应格式如下：
access_type1|file_info1|[offset1]|[length1];access_type2|file_info2|[offset2]|[length2]
各字段含义如下：
access_type：文件访问方式，支持路径方式（fo）和文件描述符方式（fd）；
file_info：此字段和access_type 对应，文件路径对应fo，文件描述符对应fd，
离线	grm_build_path	离线语法生成路径	构建离线语法所生成数据的保存路径（文件夹）
通用	result_type	结果格式	可取值：plain，json
默认值：plain
通用	text_encoding	文本编码格式	表示参数中携带的文本编码格式
离线	local_grammar	离线语法id	构建离线语法后获得的语法ID
通用	ptt	添加标点符号(仅sub=iat时有效)	0:无标点符号;1:有标点符号。默认为1
在线	aue	音频编码格式和压缩等级	编码算法：raw；speex；speex-wb；ico
编码等级：raw：不进行压缩。speex系列：0-10； 默认为speex-wb;7
speex对应sample_rate=8000
speex-wb对应sample_rate=16000
ico对应sample_rate=16000
通用	result_encoding	识别结果字符串所用编码格式	GB2312;UTF-8;UNICODE
不同的格式支持不同的编码：
plain:UTF-8,GB2312 json:UTF-8
通用	vad_enable	VAD功能开关	是否启用VAD 默认为开启VAD 0（或false）为关闭
通用	vad_bos	允许头部静音的最长时间(目前未开启该功能)	0-10000毫秒。默认为10000
如果静音时长超过了此值，则认为用户此次无有效音频输入。此参数仅在打开VAD功能时有效。
通用	vad_eos	允许尾部静音的最长时间	0-10000毫秒。默认为2000
如果尾部静音时长超过了此值，则认为用户音频已经结束，此参数仅在打开VAD功能时有效。
errorCode[out]
函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表。
返回
  函数调用成功返回字符串格式的sessionID，失败返回NULL。sessionID是本次识别的句柄。 。

备注
  参数只在当次识别中生效。

参见：

const char * params = "engine_type = local, asr_res_path = ****, sample_rate = ****, grm_build_path = ****, local_grammar = ****, result_type = json, result_encoding = UTF-8";
int    ret = 0;
const char* sessionID = QISRSessionBegin( NULL, params, &ret );
if( MSP_SUCCESS != ret )
{
    printf( "QISRSessionBegin failed, error code is: %d", ret );
}
.
#QISRAudioWrite()
int MSPAPI QISRAudioWrite (const char *     sessionID,
                           const void *     waveData,
                           unsigned int     waveLen,
                           int              audioStatus,
                           int *            epStatus,
                           int *            recogStatus 
                         )	
写入本次识别的音频。

参数：

sessionID[in]
由QISRSessionBegin返回的句柄。

waveData[in]
音频数据缓冲区起始地址。

waveLen[in]
音频数据长度,单位字节。

audioStatus[in]
用来告知MSC音频发送是否完成，典型值如下：

枚举常量	简介
MSP_AUDIO_SAMPLE_FIRST = 1	第一块音频
MSP_AUDIO_SAMPLE_CONTINUE = 2	还有后继音频
MSP_AUDIO_SAMPLE_LAST = 4	最后一块音频
epStatus[out]
端点检测（End-point detected）器所处的状态，可能的值如下：

枚举常量	简介
MSP_EP_LOOKING_FOR_SPEECH = 0	还没有检测到音频的前端点。
MSP_EP_IN_SPEECH = 1	已经检测到了音频前端点，正在进行正常的音频处理。
MSP_EP_AFTER_SPEECH = 3	检测到音频的后端点，后继的音频会被MSC忽略。
MSP_EP_TIMEOUT = 4	超时。
MSP_EP_ERROR = 5	出现错误。
MSP_EP_MAX_SPEECH = 6	音频过大。
rsltStatus[out]
识别器返回的状态，提醒用户及时开始\停止获取识别结果。典型值如下：

枚举常量	简介
MSP_REC_STATUS_SUCCESS = 0	识别成功，此时用户可以调用QISRGetResult来获取（部分）结果。
MSP_REC_STATUS_NO_MATCH = 1	识别结束，没有识别结果。
MSP_REC_STATUS_INCOMPLETE = 2	正在识别中。
MSP_REC_STATUS_COMPLETE = 5	识别结束。
返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注：
  本接口需不断调用，直到音频全部写入为止。上传音频时，需更新audioStatus的值。具体来说:
  当写入首块音频时,将audioStatus置为MSP_AUDIO_SAMPLE_FIRST
  当写入最后一块音频时,将audioStatus置为MSP_AUDIO_SAMPLE_LAST
  其余情况下,将audioStatus置为MSP_AUDIO_SAMPLE_CONTINUE
  同时，需定时检查两个变量：epStatus和rsltStatus。具体来说:
  当epStatus显示已检测到后端点时，MSC已不再接收音频，应及时停止音频写入
  当rsltStatus显示有识别结果返回时，即可从MSC缓存中获取结果

参见：

char audio_data[ 5120 ] ={'\0'};
unsigned int audio_len = 0;
int audio_status = 2;
int ep_status = 0;
int rec_status = 0;
int ret = 0;
while(MSP_AUDIO_SAMPLE_LAST != audio_status )
{
    // 读取音频到缓冲区audio_data 中，设置音频长度audio_len，音频状态audio_status。
    ret = QISRAudioWrite( sessionID, audio_data, audio_len, audio_status, &ep_status, &rec_status );
    if( MSP_SUCCESS  ! = ret )
    {
        printf( "QISRAudioWrite failed, error code is: %d", ret );
        break;
    }
    else if(MSP_EP_AFTER_SPEECH == ep_status ) // 检测到音频后端点，停止写入音频
    {
        printf( "end point of speech has been detected!" );
        break;
    }
    // 如果是实时采集音频，可以省略此操作。5KB 大小的16KPCM 持续的时间是160 毫秒
    Sleep( 160 );
}
.
#QISRGetResult()

const char* MSPAPI QISRGetResult (const char * 	sessionID,
                                  int *         rsltStatus,
                                  int           waitTime,
                                  int *         errorCode 
                                 )	
获取识别结果。

参数：

sessionID[in]
由QISRSessionBegin返回的句柄。

rsltStatus[out]
识别结果的状态，其取值范围和含义请参考QISRAudioWrite 的参数recogStatus。

waitTime[in]
此参数做保留用。

errorCode[out]
函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表。

返回：
  函数执行成功且有识别结果时，返回结果字符串指针；其他情况(失败或无结果)返回NULL。

备注：

  当写入音频过程中已经有部分识别结果返回时，可以获取结果。在音频写入完毕后，用户需反复调用此接口，直到识别结果获取完毕（rlstStatus值为5）或返回错误码。 注意：如果某次成功调用后暂未获得识别结果，请将当前线程sleep一段时间，以防频繁调用浪费CPU资源。

参见：

char rslt_str[ 2048 ] ={'\0'};
const char* rec_result = NULL;
int rslt_status = 0;
int ret = 0;
while(MSP_REC_STATUS_SPEECH_COMPLETE != rslt_status )
{
    rec_result = QISRGetResult ( sessionID, &rslt_status, 5000, &ret );
    if( MSP_SUCCESS  != ret )
    {
        printf( "QISRGetResult failed, error code is: %d", ret );
        break;
    }
    if( NULL != rec_result )
    {
     // 用户可以用其他的方式保存识别结果
        strcat( rslt_str, rec_result );
        continue;
    }
    // sleep 一下很有必要，防止MSC 端无缓存的识别结果时浪费CPU 资源
    Sleep( 200 );
}
.
#QISRSessionEnd()
int MSPAPI QISRSessionEnd(const char * 	sessionID,
                          const char * 	hints 
                         )	
结束本次语音识别。

参数

sessionID[in]
由QISRSessionBegin返回的句柄。hints[in]结束本次语音识别的原因描述，为用户自定义内容。
返回   函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注   本接口和QISRSessionBegin对应,调用此接口后，该句柄对应的相关资源（参数、语法、音频、实例等）都会被释放，用户不应再使用该句柄。

参见

int ret = QISRSessionEnd ( sessionID, "normal end" );
if( MSP_SUCCESS  != ret )
{
    printf( "QISRSessionEnd failed, error code is: %d", ret );
}
.
#QISRGetParam()
int MSPAPI QISRGetParam (const char * 	sessionID,
                         const char * 	paramName,
                         char *         paramValue,
                         unsigned int * valueLen 
                        )	
获取当次语音识别信息，如上行流量、下行流量等。

参数

sessionID[in]
由QISRSessionbegin返回的句柄，如果为NULL，获取MSC的设置信息。

paramName[in]
参数名，一次调用只支持查询一个参数。参数如下：

在线/离线业务	参数	描述
在线	sid	服务端会话ID，长度为32字节
在线	upflow	上行数据量
在线	downflow	下行数据量
通用	volume	最近一次写入的音频的音量
paramValue[out]
输入:buffer首地址
输出:向该buffer写入获取到的信息

valueLen[out]
输入:buffer的大小
输出:信息实际长度(不含’\0’)

返回
  函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  以查询上行流量为例，获取到的是本次识别当前累计的上行流量。下行流量查询与此相似。

参见

const char * para_name = "upflow";
char para_value[32] = {'\0'};
unsigned int value_len = 32;
int ret = QISRGetParam ( sessionID, para_name, para_value, &value_len );
if( MSP_SUCCESS != ret )
{
    printf( "QISRGetParam failed, error code is: %d", ret );
}
.
#QISRBuildGrammar()
int MSPAPI QISRBuildGrammar (const char *       grammarType,
                             const char *       grammarContent,
                             unsigned int       grammarLength,
                             const char *       params,
                             GrammarCallBack    callback,
                             void *             userData 
                            )	
构建语法，生成语法ID。

参数

grammarType[in]
语法类型，离线识别采用bnf 格式语法。

grammarContent[in]
语法内容。

grammarLength[in]
语法长度。

params[in]
参见下表：
格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

在线/离线业务	参数	名称	说明
通用	engine_type	引擎类型	可取值：
local：离线引擎
通用	sample_rate	音频采样率	可取值：16000,8000,
默认值：16000
离线	asr_res_path	离线识别资源路径	离线识别资源所在路径，对应格式如下：
access_type1|file_info1|[offset1]|[length1];access_type2|file_info2|[offset2]|[length2]
各字段含义如下：
access_type：文件访问方式，支持路径方式（fo）和文件描述符方式（fd）；
file_info：此字段和access_type 对应，文件路径对应fo，文件描述符对应fd， 其中文件路径必须是包含文件名的完整路径；
offset：资源文件在此传入文件中的偏移；
length：资源文件大小。
离线	grm_build_path	离线语法生成路径	构建离线语法所生成数据的保存路径（文件夹）
callback[in]
构建语法回调接口。typedef int ( GrammarCallBack)( int errorCode, const char info, void* userData);

userData[in/out]
用户数据。

返回
  函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  无

参见
  无

#QISRUpdateLexicon()
int MSPAPI QISRUpdateLexicon(const char *       lexiconName,
                             const char *       lexiconContent,
                             unsigned int       lexiconLength,
                             const char *       params,
                             LexiconCallBack    callback,
                             void *             userData 
                            )	
更新本地语法词典。

参数

lexiconName[in]
词典名称。更新本地语法词典：传递语法中需要更新的词典槽名称

lexiconContent[in]
词典内容。本地语法词典：词典内容为换行符分割的字符串列表,如："词条1\n 词条2\n 词条3\n 词条4"

lexiconLength[in]
词典内容长度。

params[in]
参见下表：
格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

在线/离线业务	参数	名称	说明
通用	engine_type	引擎类型	可取值：
cloud：在线引擎,
local：离线引擎,
默认值：cloud
在线	subject	业务类型	必须由用户指定，更新云端词典设置为uup
在线	data_type	数据类型	必须由用户指定。
更新云端联系人：contact
更新云端个性化词表：userword
通用	text_encoding	文本编码格式	表示参数中携带的文本编码格式
可取值：GB2312,GBK,UTF-8,UTF-16LE,UTF-16BE
通用	sample_rate	音频采样率	可取值：16000,8000,
默认值：16000
离线	asr_res_path	离线识别资源路径	离线识别资源所在路径，对应格式如下：
access_type1|file_info1|[offset1]|[length1];access_type2|file_info2|[offset2]|[length2]
各字段含义如下：
access_type：文件访问方式，支持路径方式（fo）和文件描述符方式（fd）；
file_info：此字段和access_type 对应，文件路径对应fo，文件描述符对应fd， 其中文件路径必须是包含文件名的完整路径；
offset：资源文件在此传入文件中的偏移；
length：资源文件大小。
离线	grm_build_path	离线语法生成路径	构建离线语法所生成数据的保存路径（文件夹）
离线	grammar_list	语法id 列表	指定需要更新的离线语法id 列表，支持一次性更新多个语法，
格式如下：id1;id2
callback[in]
更新词典回调接口。typedef int (LexiconCallBack)(int errorCode, const char info, void* userData);

userData[in/out]
用户数据。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  无

参见
  无

#qtts.h 语音合成
语音合成(iFLY Speech Synthesizer Header File)

#函数
类型	说明
const char *MSPAPI	QTTSSessionBegin(const char *params, int *errorCode)
开始一次语音合成，分配语音合成资源。
int MSPAPI	QTTSTextPut(const char *sessionID, const char *textString, unsigned int textLen, const char *params)
写入要合成的文本。
const void *MSPAPI	QTTSAudioGet(const char *sessionID, unsigned int *audioLen, int *synthStatus, int *errorCode)
获取合成音频。
int MSPAPI	QTTSSessionEnd(const char *sessionID, const char *hints)
结束本次语音合成。
int MSPAPI	QTTSGetParam(const char *sessionID, const char *paramName, char *paramValue, unsigned int *valueLen)
获取当前语音合成信息，如当前合成音频对应文本结束位置、上行流量、下行流量等。
#详细描述
语音合成(iFLY Speech Synthesizer Header File)

This file contains the quick application programming interface (API) declarations of TTS. Developer can include this file in your project to build applications. For more information, please read the developer guide.

Use of this software is subject to certain restrictions and limitations set forth in a license agreement entered into between iFLYTEK, Co,LTD. and the licensee of this software. Please refer to the license agreement for license use rights and restrictions.

Copyright (C) by iFLYTEK, Co,LTD. All rights reserved.

#函数说明
#QTTSSessionBegin()
const char* MSPAPI QTTSSessionBegin(const char * params,
                                    int *        errorCode 
                                    )	
开始一次语音合成，分配语音合成资源。

参数

params[in]
传入的参数列表，支持以下参数：
格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

在线/离线业务	参数	名称	说明
通用	engine_type	引擎类型	可取值：
cloud：在线引擎
local：离线引擎，默认为cloud
通用	voice_name	发音人	不同的发音人代表了不同的音色，
如男声、女声、童声等，具体参数值请到控制台-发音人授权管理 确认
通用	speed	语速	合成音频对应的语速，
取值范围：[0,100]，数值越大语速越快。
默认值：50
通用	volume	音量	合成音频的音量，
取值范围：[0,100]，数值越大音量越大。
默认值：50
通用	pitch	语调	合成音频的音调，
取值范围：[0,100]，数值越大音调越高。
默认值：50
离线	tts_res_path	合成资源路径	合成资源所在路径，支持fo 方式参数设置，对应格式如下：
fo|[file_info]|[offset]|[length]
（1）若是合并资源，则只需传入一个资源路径，如：fo| combined.jet|0|1024
（2）若是分离资源，则需传两个资源路径，如：fo|common.jet|0|1024;fo| xiaoyan.jet|0|1024
通用	rdn	数字发音	合成音频数字发音，支持参数，
0 数值优先,
1 完全数值,
2 完全字符串，
3 字符串优先，
默认值：0
离线	rcn	1 的中文发音	支持参数：
0：表示发音为yao
1：表示发音为yi
默认值：0
通用	text_encoding	文本编码格式（必传）	合成文本编码格式，支持参数，GB2312，GBK，BIG5，UNICODE，GB18030，UTF8
通用	sample_rate	合成音频采样率	合成音频采样率，支持参数，16000，8000（离线高品质合成暂不支持），默认为16000
在线	background_sound	背景音	合成音频中的背景音，支持参数，
0：无背景音乐，
1：有背景音乐
在线	aue	音频编码格式和压缩等级	码算法：raw；speex；speex-wb；ico
编码等级：raw：不进行解压缩 speex系列：0-10；
默认为speex-wb;7
speex对应sample_rate=8000
speex-wb对应sample_rate=16000
ico对应sample_rate=16000
在线	ttp	文本类型	合成文本类型，支持参数，
text: 普通格式文本
cssml：cssml 格式文本
默认值：text
离线	speed_increase	语速增强	通过设置此参数控制合成音频语速基数，取值范围，
1：正常 2：2 倍语速 4：4 倍语速
离线	effect	合成音效	合成音频的音效，取值范围，
0 无音效，1 忽远忽近，2 回声，3 机器人，4 合唱，5 水下，6 混响，7 阴阳怪气
注意:没有默认值的参数必须由外部设定其值

errorCode[out]
函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

返回
  函数调用成功返回字符串格式的sessionID，失败返回NULL。sessionID是本次合成的句柄。

备注
  参数只在本次合成中生效。

参见

const char* params= "engine_type = local, voice_name=xiaoyan, tts_res_path =fo|res\\tts\\xiaoyan.jet;fo|res\\tts\\common.jet, sample_rate = 16000" ;
int ret = 0;
const char* sessionID = QTTSSessionBegin( params, &ret );
if( MSP_SUCCESS != ret )
{
    printf( "QTTSSessionBegin failed, error code is: %d", ret );
}
.
#QTTSTextPut()
int MSPAPI QTTSTextPut	(const char *   sessionID,
                         const char * 	textString,
                         unsigned int 	textLen,
                         const char * 	params 
                        )	
写入要合成的文本。
参数

sessionID[in]
由QTTSSessionBegin返回的句柄。

textString[in]
字符串指针。指向待合成的文本字符串。

textLen[in]
合成文本长度,最大支持8192个字节（不含’\0’）。

params[in]
本次合成所用的参数，只对本次合成的文本有效。目前为空。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  本接口不支持连续被调用。调用本接口写入合成文本后，用户需要反复调用QTTSAudioGet 接口来获取音频。

参见

const char* text_str = "科大讯飞股份有限公司";
unsigned int text_len = strlen( textString ); //textLen 参数为合成文本所占字节数
int ret = QTTSTextPut( sessionID, text_str, text_len, NULL );
if( MSP_SUCCESS != ret )
{
    printf( "QTTSTextPut failed, error code is: %d", ret );
}
.
#QTTSAudioGet()
const void* MSPAPI QTTSAudioGet	(const char * 	sessionID,
                                 unsigned int * audioLen,
                                 int * 	        synthStatus,
                                 int * 	        errorCode 
                                )	
获取合成音频。
参数

sessionID[in]
由QTTSSessionBegin返回的句柄。

audioLen[out]
合成音频长度，单位字节。

synthStatus[out]
合成音频状态，可能的值如下：

枚举常量	简介
MSP_TTS_FLAG_STILL_HAVE_DATA = 1	音频还没取完，还有后继的音频
MSP_TTS_FLAG_DATA_END = 2	音频已经取完
errorCode[out]
函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

返回
  函数调用成功且有音频数据时返回非空指针。调用失败或无音频数据时，返回NULL。

备注
  用户需要反复获取音频，直到音频获取完毕或函数调用失败。在重复获取音频时，如果暂未获得音频数据，需要将当前线程sleep一段时间，以防频繁调用浪费CPU资源。

参见

FILE* fp = fopen("tts.pcm", "wb");
while (1)
{
    const void * data = QTTSAudioGet(sessionID, &audio_len, &synth_status, &ret);
    if (NULL != data)
    {
        fwrite(data, audio_len, 1, fp);
    }
    if (MSP_TTS_FLAG_DATA_END == synth_status || MSP_SUCCESS != ret)
    {
        break;
    }
}
fclose(fp);
.
#QTTSSessionEnd()
int MSPAPI QTTSSessionEnd(const char * 	sessionID,
                          const char * 	hints 
                         )	
结束本次语音合成。

参数

sessionID[in]
由QTTSSessionBegin返回的句柄。

hints[in]
结束本次语音合成的原因描述，为用户自定义内容。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  本接口和QTTSSessionBegin对应，调用此接口后，该句柄对应的相关资源（参数，合成文本，实例等）都会被释放，用户不应再使用该句柄。

参见

int ret = QTTSSessionEnd ( sessionID, "normal end" );
if( MSP_SUCCESS != ret )
{
    printf( "QTTSSessionEnd failed, error code is: %d", ret );
}
.
#QTTSGetParam()
int MSPAPI QTTSGetParam	(const char * 	sessionID,
                         const char * 	paramName,
                         char *     	paramValue,
                         unsigned int * valueLen 
                        )	
获取当前语音合成信息，如当前合成音频对应文本结束位置、上行流量、下行流量等。

参数

sessionID[in]
由QTTSSessionBegin返回的句柄，如果为NULL，获取MSC的设置信息。

paramName[in]
参数名，一次调用只支持查询一个参数。参数如下：

在线/离线业务	参数名称	意义
在线	sid	服务端会话ID，长度为32字节
在线	upflow	上行数据量。
在线	downflow	下行数据量
通用	ced	当前合成音频对应文本结束位置
paraValue[in/out]
输入:buffer首地址
输出:向该buffer写入获取到的信息

valueLen[in/out]
输入:buffer的大小
输出:信息实际长度(不含'\0')

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  以查询上行流量为例，获取到的是本次合成当前累计的上行流量。下行流量查询与此相似。

参见

const char* para_name = "upflow";
char  para_value[ 32 ] = {'\0'};
unsigned int value_len = 32;
int ret = QTTSGetParam ( sessionID, para_name, para_value, &value_len );
if( MSP_SUCCESS  != ret )
{
   printf( "QTTSGetParam failed, error code is: %d", ret );
}
.
#qise.h 语音评测
语音评测(iFLY Speech Evaluation Header File)

#函数
类型	说明
const char *MSPAPI	QISESessionBegin(const char *params, const char *userModelId, int *errorCode)
开始一次语音评测。
int MSPAPI	QISETextPut(const char *sessionID, const char *textString, unsigned int textLen, const char *params)
写入待评测的文本。
int MSPAPI	QISEAudioWrite (const char *sessionID, const void *waveData, unsigned int waveLen, int audioStatus, int *epStatus, int *Status)
写入本次评测的音频。
const char *MSPAPI	QISEGetResult(const char *sessionID, unsigned int *rsltLen, int *rsltStatus, int *errorCode)
获取评测结果。
int MSPAPI	QISESessionEnd(const char *sessionID, const char *hints)
结束本次语音评测。
#详细描述
语音评测(iFLY Speech Evaluation Header File)

This file contains the quick application programming interface (API) declarations of evaluation. Developer can include this file in your project to build applications. For more information, please read the developer guide.

Use of this software is subject to certain restrictions and limitations set forth in a license agreement entered into between iFLYTEK, Co,LTD. and the licensee of this software. Please refer to the license agreement for license use rights and restrictions.

Copyright (C) by iFLYTEK, Co,LTD. All rights reserved.

#函数说明
#QISESessionBegin()
const char* MSPAPI QISESessionBegin(const char * params,
                                    const char * userModelId,
                                    int *        errorCode 
                                    )		
开始一次语音评测。
参数

params[in]
传入的参数列表，支持以下参数：
格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

参数	参数说明	值
sub	本次评测请求的类型	ise
aue	音频编码格式和压缩等级	编码算法：raw；speex；speex-wb；ico
编码等级：raw：无等级。speex系列：0-10；
默认为speex-wb;7
speex-wb对应audio/L16;rate=16000
ico对应audio/L16;rate=16000
auf	音频格式	audio/L16;rate=16000
默认为audio/L16;rate=16000
rse	评测结果字符串所用编码格式	gb2312
vad_timeout	允许头部静音的最长时间	0-10000毫秒。默认为10000
如果静音时长超过了此值，则认为用户此次无有效音频输入。此参数仅在打开VAD功能时有效。
vad_speech_tail	允许尾部静音的最长时间	0-10000毫秒。默认为2000
如果尾部静音时长超过了此值，则认为用户音频已经结束，此参数仅在打开VAD功能时有效。
vad_enable	VAD功能开关	是否启用VAD
默认为开启VAD
0（或false）为关闭
category	评测类型	read_syllable(当language=en_us时，不支持)
read_word
read_sentence
language	评测语言	zh_cn:简体中文
en_us:英语
默认为zh_cn
注意:没有默认值的参数必须由外部设定其值

userModelId[in]
此参数保留，传入NULL即可。

errorCode[out]
函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

返回
  函数调用成功返回字符串格式的sessionID，失败返回NULL。sessionID是本次评测的句柄。

备注
  参数只在当次评测中生效.

参见

const char * params = "sub=ise,category=read_syllable,language=zh_cn,aue=speex-wb;7,auf=audio/L16;rate=16000";
int    ret = 0;
const char* sessionID = QISESessionBegin( params,NULL,&ret );
if( MSP_SUCCESS != ret )
{
    printf( "QISESessionBegin failed, error code is: %d", ret );
}
.
#QISETextPut()
int MSPAPI QISETextPut(const char * 	sessionID,
                       const char * 	textString,
                       unsigned int 	textLen,
                       const char * 	params 
                       )
写入待评测的文本。

参数

sessionID[in]
由QISESessionBegin返回的句柄。

textString[in]
字符串指针。指向待评测的文本字符串。

textLen[in]
评测文本长度。

params[in]
本次评测所用的参数，只对本次评测的文本有效。

返回
  函数调用成功返回MSP_SUCCESS，否则返回错误代码。详见错误码列表 。

参见

const char* src_text = “科大讯飞股份有限公司”;
unsigned int text_len = strlen(src_text); //textLen参数为评测文本所占字节数
int ret = QISETextPut( sessionID, src_text, text_len, NULL );
if( MSP_SUCCESS != ret )
{
    printf( QISETextPut failed, error code is: %d”, ret );
}
.
#QISEAudioWrite()
int MSPAPI QISEAudioWrite(const char * 	sessionID,
                          const void * 	waveData,
                          unsigned int 	waveLen,
                          int 	        audioStatus,
                          int *         epStatus,
                          int *         Status 
                          )	
写入本次评测的音频。

参数

sessionID[in]
由QISESessionBegin返回的句柄。

waveData[in]
音频数据缓冲区起始地址。

waveLen[in]
音频数据长度,单位字节。

audioStatus[in]
用来告知MSC音频发送是否完成，典型值如下：

枚举常量	描述
MSP_AUDIO_SAMPLE_FIRST = 1	第一块音频
MSP_AUDIO_SAMPLE_CONTINUE = 2	还有后继音频
MSP_AUDIO_SAMPLE_LAST = 4	最后一块音频
epStatus[out]
端点检测（End-point detected）器所处的状态,可能的值如下：

枚举常量	描述
MSP_EP_LOOKING_FOR_SPEECH = 0	还没有检测到音频的前端点
MSP_EP_IN_SPEECH = 1	已经检测到了音频前端点，正在进行正常的音频处理
MSP_EP_AFTER_SPEECH = 3	检测到音频的后端点，后继的音频会被MSC忽略
MSP_EP_TIMEOUT = 4	超时
MSP_EP_ERROR = 5	出现错误
MSP_EP_MAX_SPEECH = 6	音频过大
注意：当epStatus大于等于3时,用户应当停止写入音频的操作,否则写入MSC的音频会被忽略。

recogStatus[out]
评测器返回的状态，提醒用户及时开始\停止获取评测结果。典型值如下：

枚举常量	描述
MSP_REC_STATUS_SUCCESS = 0	评测成功，有评测结果返回
MSP_REC_STATUS_NO_MATCH = 1	评测结束，没有评测结果
MSP_REC_STATUS_INCOMPLETE = 2	正在评测
MSP_REC_STATUS_COMPLETE = 5	评测结束，有评测结果返回
返回
  函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  本接口需不断调用，直到音频全部写入为止。上传音频时，需更新audioStatus的值。具体来说:
  当写入首块音频时,将audioStatus置为MSP_AUDIO_SAMPLE_FIRST
  当写入最后一块音频时,将audioStatus置为MSP_AUDIO_SAMPLE_LAST
  其余情况下,将audioStatus置为MSP_AUDIO_SAMPLE_CONTINUE
  同时，需定时检查两个变量：epStatus和rsltStatus。具体来说:
  当epStatus显示已检测到后端点时，MSC已不再接收音频，应及时停止音频写入
  当rsltStatus显示有评测结果返回时，即可从MSC缓存中获取结果
参见

char audio_data[5120] ={'\0'};
unsigned int   audio_len = 0;
int audio_status = 2;
int ep_status = 0;
int rec_status = 0;
int ret = 0;
while(MSP_AUDIO_SAMPLE_LAST != audio_status )
{
    // 读取音频到缓冲区audio_data中,设置音频长度audio_len,音频状态audio_status。
    ret = QISEAudioWrite( sessionID, audio_data, audio_len, audio_status, &ep_status, &rec_status );
    if( MSP_SUCCESS != ret )
    {
        printf( "QISEAudioWrite failed, error code is: %d", ret );
        break;
    }
    else if(MSP_EP_AFTER_SPEECH == ep_status )//检测到音频后端点,停止写入音频 
    {
        printf( "end point of speech has been detected!" );
        break;
    }
    //如果是实时采集音频,可以省略此操作。5KB大小的16KPCM持续的时间是160毫秒 
    Sleep( 160 );  
}
.
#QISEGetResult()
const char* MSPAPI QISEGetResult(const char * 	sessionID,
                                 unsigned int * rsltLen,
                                 int * 	        rsltStatus,
                                 int *          errorCode 
                                )	
获取评测结果。

参数

sessionID[in]
由QISESessionBegin返回的句柄。

rsltLen[out]
评测结果长度，单位字节。

rsltStatus[out]
评测结果的状态,其取值范围和含义请参考QISEAudioWrite的参数recogStatus。

errorCode[out]
函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表。

返回
  函数执行成功且有评测结果时，返回结果字符串指针；其他情况(失败或无结果)返回NULL。

备注
  当写入音频过程中已经有部分评测结果返回时，可以获取结果。在音频写入完毕后，用户需反复调用此接口，直到评测结果获取完毕（rlstStatus值为5）或返回错误码。

注意：如果某次成功调用后暂未获得评测结果，请将当前线程sleep一段时间，以防频繁调用浪费CPU资源。

参见

char rslt_str[2048] ={'\0'};
const char* rec_result = NULL;
int rslt_status = 0;
int rsltLen = 0；
int ret = 0;
while(MSP_REC_STATUS_COMPLETE != rslt_status )
{
    rec_result = QISEGetResult ( sessionID, &rsltLen, &rslt_status, &ret );
    if( MSP_SUCCESS != ret )
    {
        printf( "QISEGetResult failed, error code is: %d", ret );
        break;
    }
    if( NULL != rec_result )
    {
        strcat( rslt_str, rec_result );//用户可以用其他的方式保存评测结果
        continue;
    }
    //sleep一下很有必要,防止MSC端无缓存的评测结果时浪费CPU资源
    Sleep( 200 );
}
.
#QISESessionEnd()
int MSPAPI QISESessionEnd(const char * 	sessionID,
                          const char * 	hints 
                         )	
结束本次语音评测。

参数

sessionID[in]
由QISESessionBegin返回的句柄。

hints[in]
结束本次语音评测的原因描述，为用户自定义内容。

返回
  函数调用成功返回MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  本接口和QISESessionBegin对应,调用此接口后，该句柄对应的相关资源都会被释放，用户不应再使用该句柄。

参见

int ret = QISESessionEnd ( sessionID, "normal end" );
if( MSP_SUCCESS != ret )
{
    printf( "QISESessionEnd failed, error code is: %d", ret );
}
sessionID = NULL;
#qivw.h 语音唤醒
语音唤醒(iFLY Speech Voice Wakeup Header File)

#函数
类型	说明
const char *MSPAPI	QIVWSessionbegin(const char *grammarList, const char *params, int *errorCode)开始唤醒功能，并在参数中指定唤醒(唤醒+识别时)用到的语法列表，本次唤醒所用的参数等。
int MSPAPI	QIVWSessionEnd(const char *sessionID, const char *hints)结束本次语音唤醒。
int MSPAPI	QIVWAudioWrite(const char *sessionID, const void *audioData, unsigned int audioLen, int audioStatus)写入本次唤醒的音频，本接口需要反复调用直到音频写完为止。
int MSPAPI	QIVWRegisterNotify(const char *sessionID, ivw_ntf_handler msgProcCb, void *userData)注册回调。
#详细描述
语音唤醒(iFLY Speech Voice Wakeup Header File)

This file contains the quick application programming interface (API) declarations of IVW. Developer can include this file in your project to build applications. For more information, please read the developer guide.

Use of this software is subject to certain restrictions and limitations set forth in a license agreement entered into between iFLYTEK, Co,LTD. and the licensee of this software. Please refer to the license agreement for license use rights and restrictions.

Copyright (C) by iFLYTEK, Co,LTD. All rights reserved.

#函数说明
#QIVWSessionBegin()
const char* MSPAPI QIVWSessionBegin(const char * 	grammarList,
                                    const char * 	params,
                                    int * 	        errorCode 
                                   )		
开始唤醒功能，本次唤醒所用的参数等。

参数：

grammarList[in]
保留参数，设置为NULL即可。

params[in]
参见下表：
格式说明：每个参数和参数值通过key=value的形式组成参数对；如果有多个参数对，再用逗号进行拼接，如：key_1=value_1,key_2=value_2
注意：每个参数(key)和参数值(value)均不得含有逗号(,)和等号(=)，否则会被截断

参数	参数说明	是否必要
sst	业务类型。唤醒业务类型,可以设置如下参数：
wakeup：语音唤醒(默认)
oneshot：唤醒加识别	是
ivw_threshold	唤醒词门限。
设置格式如下：
id0:xx;id1:xx;….。
示例：0:1450;1:1450表示设置第一个唤醒词的门限值为1450，第二个唤醒的门限值是1450，门限值越低越容易唤醒成功。
注意：建议唤醒引擎门限值设为1450，且取值范围为非负数，一般可在0-3000之间调节。	否
ivw_shot_word	音频是否包含唤醒词。
用于唤醒加识别时场景，将音频送入识别引擎时是否包含唤醒词音频：
0：不包含，1：包含(默认)	否
errorCode[out]
函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

返回
  函数调用成功返回字符串格式的sessionID，失败返回NULL。sessionID是本次唤醒的句柄。

备注
  参数只在当次唤醒中生效。

参见：

    const char* params = "ivw_threshold=0:1450, ivw_res_path =fo|res/ivw/wakeupresource.jet";
	int ret = 0;
	const char* sessionID = QIVWSessionBegin( NULL, params, &ret );
	if( MSP_SUCCESS != ret )
	{
    printf( "QIVWSessionBegin failed, error code is: %d", ret );
	}
	.
#QIVWSessionEnd()

int MSPAPI QIVWSessionEnd(const char * 	sessionID,
                          const char * 	hints 
                          )		
结束本次语音唤醒。

参数：

sessionID[in]
由QIVWSessionBegin返回的句柄。

hints[in]
结束本次语音唤醒的原因描述，为用户自定义内容。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  本接口和QIVWSessionBegin 对应，用来本次语音唤醒。调用此接口后，该句柄对应的相关资源（参数、语法、音频、实例等）都会被释放，用户不应再使用该句柄。

参见：

  	int ret = QIVWSessionEnd ( sessionID, "normal end" );
	if( MSP_SUCCESS != ret )
	{
    printf( "QIVWSessionEnd failed, error code is: %d", ret );
	}
	.
#QIVWAudioWrite()
int MSPAPI QIVWAudioWrite(const char * 	sessionID,
                          const void * 	audioData,
                          unsigned int 	audioLen,
                          int 	        audioStatus 
                         )		
写入本次唤醒的音频，本接口需要反复调用直到音频写完为止。

参数：

sessionID[in]
由QIVWSessionBegin返回的句柄。

audioData[in]
音频数据缓冲区起始地址。

audioLen[in]
音频数据长度，单位字节。

audioStatus[in]
用来告知MSC音频发送是否完成，典型值如下：

枚举常量	简介
MSP_AUDIO_SAMPLE_FIRST = 1	第一块音频
MSP_AUDIO_SAMPLE_CONTINUE = 2	还有后继音频
MSP_AUDIO_SAMPLE_LAST = 4	最后一块音频
paramValue[in]
参数值。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  调用本接口时，推荐用户在写入音频时采取"边录边写"的方式，即每隔一小段时间将采集到的音频通过本接口写入MSC。

参见：

char audio_data[ 5120 ] ={'\0'};
unsigned int audio_len = 0;
int audio_status = 2;
int ret = 0;
while(MSP_AUDIO_SAMPLE_LAST != audio_status )
{
    // 读取音频到缓冲区audio_data 中，设置音频长度audio_len，音频状态audio_status。
    ret = QIVWAudioWrite( sessionID, audio_data, audio_len, audio_status);
    if( MSP_SUCCESS ! = ret )
    {
        printf( "QIVWAudioWrite failed, error code is: %d", ret );
        break;
    }
}
.
#QIVWRegisterNotify()
int MSPAPI QIVWRegisterNotify(const char *      sessionID,
                              ivw_ntf_handler 	msgProcCb,
                              void * 	        userData 
                             )		

注册回调。

参数：

sessionID[in]
由QIVWSessionBegin返回的句柄。

msgProcCb[in]
注册通知的回调函数，唤醒结果将在此注册回调中返回。格式为：typedef int( *ivw_ntf_handler)( const char *sessionID, int msg,int param1, int param2, const void *info, void *userData );参数说明:

参数	说明
sessionID	由QIVWSessionBegin返回的句柄。
msg	MSP_IVW_MSG_WAKEUP=1 唤醒消息，在info中给出唤醒结果缓存首地址，param2 给出唤醒结果的长度。
MSP_IVW_MSG_ERROR=2 出错通知消息，在param1 中给出错误码。
MSP_IVW_MSG_ISR_RESULT=3 唤醒+识别结果消息，在info 中给出识别结果缓存首地址，param2 给出识别结果的长度。param1 中给出给出结果状态，结果状态值参见QISRAudioWrite接口中结果状态说明。
MSP_IVW_MSG_ISR_EPS=4 唤醒+识别结果中vad 端点检测消息，param1 给出端点检测状态，状态值参见QISRAudioWrite接口中端点检测状态说明。
param1	参见msg 消息说明
param2	参见msg 消息说明
info	参见msg 消息说明，主要内容包括sst,id,score,bos,eos等
sst	本次业务标识：wakeup 表示语音唤醒；enroll 表示唤醒词训练（当前版本不支持）
id	当前唤醒词的id
keyword	当前唤醒词。注：中文唤醒词返回的是拼音，例：ding1dong1ding1dong1
score	当前唤醒得分
bos	当前唤醒音频的前端点
eos	当前唤醒音频的尾端点
userData	用户数据
userData[in]
用户数据。

返回
  函数调用成功则其值为MSP_SUCCESS，否则返回错误代码，详见错误码列表 。

备注
  通过此函数注册回调函数到msc。如果唤醒成功，msc 调用回调函数通知唤醒成功息同时给出相应唤醒数据。如果出错，msc 调用回调函数给出错误信息。

参见：

    int cb_ivw_msg_proc( const char *sessionID, int msg, int param1, int param2, const void *info,void *userData )
	{
    if (MSP_IVW_MSG_ERROR == msg) //唤醒出错消息
    {
        printf("\n\nMSP_IVW_MSG_ERROR errCode = %d\n\n", param1);
    }
    else if (MSP_IVW_MSG_WAKEUP == msg) //唤醒成功消息
    {
        printf("\n\nMSP_IVW_MSG_WAKEUP result = %s\n\n", info);
    }
    return 0;
	}
	err_code = QIVWRegisterNotify(sessionID, cb_ivw_msg_proc,NULL);
	if (err_code != MSP_SUCCESS)
	{
    printf("QIVWRegisterNotify failed! error code:%d\n",err_code);
	}
	.	