import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { z } from 'zod';

// 命令数据结构
export const command_schema = z.object({
  id: z.string(),
  title: z.string().optional(),
  category: z.string().optional(),
  source: z.enum(['builtin', 'extension', 'plugin']),
  plugin_name: z.string().optional(),
  is_active: z.boolean().default(true),
});

export type Command = z.infer<typeof command_schema>;

// MCP核心工具 - 不可移动的默认命令
const MCP_CORE_TOOLS = [
  'list_all_vscode_commands',
  'execute_vscode_command',
  'search_commands',
  'show_message',
  'capture_context',
  'add_command_to_whitelist',
  'remove_command_from_whitelist',
  'get_whitelisted_commands',
  'get_available_commands',
  'get_mcp_core_tools',
  'reply_to_user',
];

// 插件自身注册的命令
const PLUGIN_REGISTERED_COMMANDS = [
  'llm-bridge.openChatPanel',
  'llm-bridge.manageWhitelist',
  'llm-bridge.recaptureCommands',
  'llm-bridge.resetWhitelist',
];

// 默认白名单
const DEFAULT_WHITELIST = [
  'workbench.action.quickOpen',
  'workbench.action.showCommands',
  'workbench.action.files.save',
  'workbench.action.files.saveAll',
  'workbench.action.tasks.runTask',
  ...MCP_CORE_TOOLS,
];

/**
 * 增强的命令管理器
 * 实现完整的白名单/黑名单管理系统
 */
export class EnhancedCommandManager {
  private _all_commands: Command[] = [];
  private _whitelist: string[] = [];
  private _blacklist: string[] = [];
  private _captured_commands: string[] = [];
  
  private readonly _commands_file_path: string;
  private readonly _whitelist_file_path: string;
  private readonly _blacklist_file_path: string;

  constructor(private readonly _context: vscode.ExtensionContext) {
    const storage_path = this._context.globalStorageUri.fsPath;
    if (!fs.existsSync(storage_path)) {
      fs.mkdirSync(storage_path, { recursive: true });
    }
    
    this._commands_file_path = path.join(storage_path, 'enhanced_commands.json');
    this._whitelist_file_path = path.join(storage_path, 'enhanced_whitelist.json');
    this._blacklist_file_path = path.join(storage_path, 'enhanced_blacklist.json');

    this._load_data();
  }

  // ==================== Action 1: 列出白名单 ====================
  public get_whitelist(): Command[] {
    const whitelist_ids = new Set(this._whitelist);
    return this._all_commands.filter(cmd => whitelist_ids.has(cmd.id));
  }

  public get_whitelist_ids(): string[] {
    return [...this._whitelist];
  }

  // ==================== Action 2: 列出黑名单 ====================
  public get_blacklist(): Command[] {
    const blacklist_ids = new Set(this._blacklist);
    return this._all_commands.filter(cmd => blacklist_ids.has(cmd.id));
  }

  public get_blacklist_ids(): string[] {
    return [...this._blacklist];
  }

  // ==================== Action 3: 列出捕获的命令并自动添加到黑名单 ====================
  public async capture_and_update_commands(): Promise<{
    newly_captured: string[];
    removed_commands: string[];
    total_commands: number;
  }> {
    console.log('🔍 开始捕获命令...');
    
    // 获取当前所有可用命令
    const current_commands = await vscode.commands.getCommands(true);
    const previous_command_ids = new Set(this._all_commands.map(cmd => cmd.id));
    
    // 计算差值
    const newly_discovered = current_commands.filter(cmd => !previous_command_ids.has(cmd));
    const removed_commands = this._all_commands
      .filter(cmd => !current_commands.includes(cmd.id))
      .map(cmd => cmd.id);

    console.log(`📊 命令变化统计:
    - 新发现命令: ${newly_discovered.length}
    - 已移除命令: ${removed_commands.length}
    - 当前总命令: ${current_commands.length}`);

    // 更新命令列表
    await this._update_all_commands(current_commands);
    
    // 新发现的命令自动添加到黑名单
    if (newly_discovered.length > 0) {
      await this._add_to_blacklist(newly_discovered);
      console.log(`✅ 已将 ${newly_discovered.length} 个新命令添加到黑名单`);
    }

    // 从白名单和黑名单中移除已不存在的命令
    if (removed_commands.length > 0) {
      await this._remove_from_all_lists(removed_commands);
      console.log(`🗑️ 已从所有列表中移除 ${removed_commands.length} 个无效命令`);
    }

    this._captured_commands = newly_discovered;
    
    return {
      newly_captured: newly_discovered,
      removed_commands,
      total_commands: current_commands.length,
    };
  }

  // ==================== Action 4: 刷新机制 ====================
  public async refresh_commands(): Promise<void> {
    await this.capture_and_update_commands();
  }

  // ==================== 命令分类和验证 ====================
  public get_command_classification(): {
    whitelist: Command[];
    blacklist: Command[];
    plugin_registered: Command[];
    mcp_core: Command[];
    unclassified: Command[];
  } {
    const whitelist_ids = new Set(this._whitelist);
    const blacklist_ids = new Set(this._blacklist);
    const plugin_ids = new Set(PLUGIN_REGISTERED_COMMANDS);
    const mcp_ids = new Set(MCP_CORE_TOOLS);

    const whitelist = this._all_commands.filter(cmd => whitelist_ids.has(cmd.id));
    const blacklist = this._all_commands.filter(cmd => blacklist_ids.has(cmd.id));
    const plugin_registered = this._all_commands.filter(cmd => plugin_ids.has(cmd.id));
    const mcp_core = this._all_commands.filter(cmd => mcp_ids.has(cmd.id));
    
    const classified_ids = new Set([
      ...this._whitelist,
      ...this._blacklist,
      ...PLUGIN_REGISTERED_COMMANDS,
    ]);
    
    const unclassified = this._all_commands.filter(cmd => !classified_ids.has(cmd.id));

    return { whitelist, blacklist, plugin_registered, mcp_core, unclassified };
  }

  // ==================== 白名单管理 ====================
  public async move_to_whitelist(command_ids: string[]): Promise<{
    success: string[];
    failed: string[];
    already_in_whitelist: string[];
  }> {
    const success: string[] = [];
    const failed: string[] = [];
    const already_in_whitelist: string[] = [];

    for (const command_id of command_ids) {
      if (this._whitelist.includes(command_id)) {
        already_in_whitelist.push(command_id);
        continue;
      }

      // 检查命令是否存在于黑名单中
      if (this._blacklist.includes(command_id)) {
        // 从黑名单移除并添加到白名单
        this._blacklist = this._blacklist.filter(id => id !== command_id);
        this._whitelist.push(command_id);
        success.push(command_id);
      } else {
        failed.push(command_id);
      }
    }

    if (success.length > 0) {
      await this._save_lists();
    }

    return { success, failed, already_in_whitelist };
  }

  public async remove_from_whitelist(command_ids: string[]): Promise<{
    success: string[];
    protected_commands: string[];
    not_found: string[];
  }> {
    const success: string[] = [];
    const protected_commands: string[] = [];
    const not_found: string[] = [];

    for (const command_id of command_ids) {
      if (!this._whitelist.includes(command_id)) {
        not_found.push(command_id);
        continue;
      }

      // 检查是否为MCP核心工具（受保护）
      if (MCP_CORE_TOOLS.includes(command_id)) {
        protected_commands.push(command_id);
        continue;
      }

      // 从白名单移除并添加到黑名单
      this._whitelist = this._whitelist.filter(id => id !== command_id);
      if (!this._blacklist.includes(command_id)) {
        this._blacklist.push(command_id);
      }
      success.push(command_id);
    }

    if (success.length > 0) {
      await this._save_lists();
    }

    return { success, protected_commands, not_found };
  }

  // ==================== 执行权限检查 ====================
  public can_execute_command(command_id: string): boolean {
    return this._whitelist.includes(command_id);
  }

  public is_mcp_core_tool(command_id: string): boolean {
    return MCP_CORE_TOOLS.includes(command_id);
  }

  public is_plugin_registered_command(command_id: string): boolean {
    return PLUGIN_REGISTERED_COMMANDS.includes(command_id);
  }

  // ==================== 数据持久化 ====================
  private _load_data(): void {
    this._load_commands();
    this._load_whitelist();
    this._load_blacklist();
  }

  private _load_commands(): void {
    try {
      if (fs.existsSync(this._commands_file_path)) {
        const data = fs.readFileSync(this._commands_file_path, 'utf8');
        const parsed = JSON.parse(data);
        const result = z.array(command_schema).safeParse(parsed);
        if (result.success) {
          this._all_commands = result.data;
        }
      }
    } catch (error) {
      console.error('Error loading commands:', error);
      this._all_commands = [];
    }
  }

  private _load_whitelist(): void {
    try {
      if (fs.existsSync(this._whitelist_file_path)) {
        const data = fs.readFileSync(this._whitelist_file_path, 'utf8');
        this._whitelist = JSON.parse(data);
      } else {
        this._whitelist = [...DEFAULT_WHITELIST];
        this._save_whitelist();
      }
    } catch (error) {
      console.error('Error loading whitelist:', error);
      this._whitelist = [...DEFAULT_WHITELIST];
    }
  }

  private _load_blacklist(): void {
    try {
      if (fs.existsSync(this._blacklist_file_path)) {
        const data = fs.readFileSync(this._blacklist_file_path, 'utf8');
        this._blacklist = JSON.parse(data);
      } else {
        this._blacklist = [];
      }
    } catch (error) {
      console.error('Error loading blacklist:', error);
      this._blacklist = [];
    }
  }

  private async _save_lists(): Promise<void> {
    await Promise.all([
      this._save_commands(),
      this._save_whitelist(),
      this._save_blacklist(),
    ]);
  }

  private async _save_commands(): Promise<void> {
    try {
      await fs.promises.writeFile(
        this._commands_file_path,
        JSON.stringify(this._all_commands, null, 2)
      );
    } catch (error) {
      console.error('Error saving commands:', error);
    }
  }

  private async _save_whitelist(): Promise<void> {
    try {
      await fs.promises.writeFile(
        this._whitelist_file_path,
        JSON.stringify(this._whitelist, null, 2)
      );
    } catch (error) {
      console.error('Error saving whitelist:', error);
    }
  }

  private async _save_blacklist(): Promise<void> {
    try {
      await fs.promises.writeFile(
        this._blacklist_file_path,
        JSON.stringify(this._blacklist, null, 2)
      );
    } catch (error) {
      console.error('Error saving blacklist:', error);
    }
  }

  // ==================== 内部辅助方法 ====================
  private async _update_all_commands(command_ids: string[]): Promise<void> {
    const new_commands: Command[] = command_ids.map(id => ({
      id,
      title: this._extract_title_from_id(id),
      category: this._extract_category_from_id(id),
      source: this._determine_command_source(id),
      plugin_name: this._extract_plugin_name(id),
      is_active: true,
    }));

    this._all_commands = new_commands;
    await this._save_commands();
  }

  private async _add_to_blacklist(command_ids: string[]): Promise<void> {
    const new_blacklist_items = command_ids.filter(id => !this._blacklist.includes(id));
    this._blacklist.push(...new_blacklist_items);
    await this._save_blacklist();
  }

  private async _remove_from_all_lists(command_ids: string[]): Promise<void> {
    const ids_to_remove = new Set(command_ids);
    
    this._whitelist = this._whitelist.filter(id => !ids_to_remove.has(id));
    this._blacklist = this._blacklist.filter(id => !ids_to_remove.has(id));
    this._all_commands = this._all_commands.filter(cmd => !ids_to_remove.has(cmd.id));
    
    await this._save_lists();
  }

  private _extract_title_from_id(id: string): string {
    return id.split('.').pop()?.replace(/([A-Z])/g, ' $1').trim() || id;
  }

  private _extract_category_from_id(id: string): string {
    const parts = id.split('.');
    return parts.length > 1 ? parts[0] : 'general';
  }

  private _determine_command_source(id: string): 'builtin' | 'extension' | 'plugin' {
    if (id.startsWith('workbench.') || id.startsWith('editor.') || id.startsWith('debug.')) {
      return 'builtin';
    }
    if (PLUGIN_REGISTERED_COMMANDS.includes(id) || MCP_CORE_TOOLS.includes(id)) {
      return 'plugin';
    }
    return 'extension';
  }

  private _extract_plugin_name(id: string): string | undefined {
    const parts = id.split('.');
    return parts.length > 1 ? parts[0] : undefined;
  }
}
