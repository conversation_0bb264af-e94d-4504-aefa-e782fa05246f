{"id": "mistral", "name": "<PERSON><PERSON><PERSON>", "description": "Mistral AI 官方API配置", "version": "1.0.0", "api_type": "mistral", "capabilities": {"max_tokens": 4096, "supports_streaming": false, "supports_function_calling": true, "supports_vision": false, "supports_code_execution": false}, "available_tools": [{"name": "reply_to_user", "description": "直接回复用户消息", "parameters": {"type": "object", "properties": {"message": {"type": "string", "description": "回复内容"}}, "required": ["message"]}, "enabled": true}, {"name": "search_commands", "description": "搜索VS Code命令", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "搜索关键词"}}, "required": ["query"]}, "enabled": true}], "prompt_templates": {"system_prompt": "你是一个VS Code智能助手，可以帮助用户执行各种编程任务。你可以使用以下工具来完成用户的请求。", "user_prompt_template": "用户请求: {{user_request}}\n\n当前环境:\n- 工作区: {{workspace_root}}\n- 活动文件: {{active_file_path}}\n- 选中文本: {{selected_text}}", "tool_instruction_template": "请使用JSON格式返回工具调用指令:\n{\n  \"tool_name\": \"工具名称\",\n  \"parameters\": { \"参数名\": \"参数值\" }\n}", "context_template": "## 可用工具\n{{available_tools}}\n\n## 环境信息\n{{context_info}}\n\n## 用户请求\n{{user_request}}"}, "ui_config": {"panel_title": "<PERSON><PERSON><PERSON>", "panel_icon": "comment-discussion", "theme_color": "#FF7000", "welcome_message": "欢迎使用 Mistral AI 智能助手！我可以帮助您完成各种VS Code任务。"}, "api_config": {"api_key": "tsJOAMaKB2dZN8jitd0QM7n2BCg12kdM", "base_url": "https://api.mistral.ai/v1/chat/completions", "model": "mistral-large-latest", "timeout": 30000}}