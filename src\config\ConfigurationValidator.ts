import * as vscode from 'vscode';
import { z } from 'zod';

import { model_config_schema, type ModelConfig } from '../subject/modelConfigs.js';

/**
 * 配置验证结果
 */
export interface ValidationResult {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * 配置验证器 - 验证配置完整性和正确性
 */
export class ConfigurationValidator {
  /**
   * 验证单个配置
   */
  static validate_config(config: any, config_id: string): ValidationResult {
    const result: ValidationResult = {
      is_valid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    try {
      // 1. Schema验证
      model_config_schema.parse(config);
      
      // 2. 业务逻辑验证
      this.validate_business_logic(config, result);
      
      // 3. API配置验证
      this.validate_api_config(config, result);
      
      // 4. 工具配置验证
      this.validate_tools_config(config, result);
      
      // 5. 提示词模板验证
      this.validate_prompt_templates(config, result);
      
      // 6. UI配置验证
      this.validate_ui_config(config, result);
      
    } catch (error) {
      result.is_valid = false;
      if (error instanceof z.ZodError) {
        result.errors.push(...error.errors.map(e => `${e.path.join('.')}: ${e.message}`));
      } else {
        result.errors.push(`配置验证失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return result;
  }

  /**
   * 验证业务逻辑
   */
  private static validate_business_logic(config: ModelConfig, result: ValidationResult): void {
    // 检查ID和名称一致性
    if (config.id !== config.name.toLowerCase().replace(/\s+/g, '_')) {
      result.warnings.push('建议ID和名称保持一致性');
    }

    // 检查版本格式
    if (!/^\d+\.\d+\.\d+$/.test(config.version)) {
      result.warnings.push('版本号建议使用语义化版本格式 (x.y.z)');
    }

    // 检查能力配置合理性
    if (config.capabilities.max_tokens && config.capabilities.max_tokens < 1000) {
      result.warnings.push('max_tokens 设置过小，可能影响响应质量');
    }

    if (config.capabilities.max_tokens && config.capabilities.max_tokens > 32000) {
      result.warnings.push('max_tokens 设置过大，可能增加成本');
    }
  }

  /**
   * 验证API配置
   */
  private static validate_api_config(config: ModelConfig, result: ValidationResult): void {
    const api_config = config.api_config;

    switch (config.api_type) {
      case 'mistral':
        if ('api_key' in api_config && (!api_config.api_key || api_config.api_key.startsWith('YOUR_'))) {
          result.errors.push('Mistral API密钥未配置');
        }
        if ('base_url' in api_config && (!api_config.base_url || !this.is_valid_url(api_config.base_url))) {
          result.errors.push('Mistral API地址无效');
        }
        if ('model' in api_config && !api_config.model) {
          result.errors.push('Mistral 模型名称未配置');
        }
        break;

      case 'spark':
        if ('api_key' in api_config && (!api_config.api_key || api_config.api_key.startsWith('YOUR_'))) {
          result.errors.push('Spark API密钥未配置');
        }
        if ('api_secret' in api_config && (!api_config.api_secret || api_config.api_secret.startsWith('YOUR_'))) {
          result.errors.push('Spark API密钥未配置');
        }
        if ('appid' in api_config && (!api_config.appid || api_config.appid.startsWith('YOUR_'))) {
          result.errors.push('Spark AppID未配置');
        }
        if ('ws_url' in api_config && (!api_config.ws_url || !this.is_valid_websocket_url(api_config.ws_url))) {
          result.errors.push('Spark WebSocket地址无效');
        }
        break;

      case 'openai':
        if ('api_key' in api_config && (!api_config.api_key || api_config.api_key.startsWith('YOUR_'))) {
          result.errors.push('OpenAI API密钥未配置');
        }
        if ('base_url' in api_config && (!api_config.base_url || !this.is_valid_url(api_config.base_url))) {
          result.errors.push('OpenAI API地址无效');
        }
        if ('model' in api_config && !api_config.model) {
          result.errors.push('OpenAI 模型名称未配置');
        }
        break;
    }

    // 检查超时设置
    if ('timeout' in api_config && api_config.timeout && (api_config.timeout < 5000 || api_config.timeout > 120000)) {
      result.warnings.push('API超时时间建议设置在5-120秒之间');
    }
  }

  /**
   * 验证工具配置
   */
  private static validate_tools_config(config: ModelConfig, result: ValidationResult): void {
    if (!config.available_tools || config.available_tools.length === 0) {
      result.warnings.push('未配置任何可用工具');
      return;
    }

    const tool_names = new Set<string>();
    for (const tool of config.available_tools) {
      // 检查工具名称重复
      if (tool_names.has(tool.name)) {
        result.errors.push(`工具名称重复: ${tool.name}`);
      }
      tool_names.add(tool.name);

      // 检查工具参数定义
      if (!tool.parameters || typeof tool.parameters !== 'object') {
        result.warnings.push(`工具 ${tool.name} 缺少参数定义`);
      }

      // 检查必需参数
      if (tool.parameters.required && !Array.isArray(tool.parameters.required)) {
        result.errors.push(`工具 ${tool.name} 的required参数应为数组`);
      }
    }

    // 检查基础工具
    const has_reply_tool = config.available_tools.some(t => t.name === 'reply_to_user');
    if (!has_reply_tool) {
      result.suggestions.push('建议添加 reply_to_user 工具以支持基本对话');
    }
  }

  /**
   * 验证提示词模板
   */
  private static validate_prompt_templates(config: ModelConfig, result: ValidationResult): void {
    const templates = config.prompt_templates;

    if (!templates.system_prompt || templates.system_prompt.trim().length < 10) {
      result.warnings.push('系统提示词过短，可能影响AI行为');
    }

    // 检查模板变量
    const template_vars = ['{{user_request}}', '{{workspace_root}}', '{{active_file_path}}', '{{selected_text}}'];
    for (const template_name of ['user_prompt_template', 'context_template'] as const) {
      const template = templates[template_name];
      if (template) {
        const missing_vars = template_vars.filter(v => !template.includes(v));
        if (missing_vars.length > 0) {
          result.suggestions.push(`${template_name} 缺少变量: ${missing_vars.join(', ')}`);
        }
      }
    }
  }

  /**
   * 验证UI配置
   */
  private static validate_ui_config(config: ModelConfig, result: ValidationResult): void {
    const ui_config = config.ui_config;

    if (!ui_config.panel_title || ui_config.panel_title.trim().length === 0) {
      result.warnings.push('面板标题为空');
    }

    if (ui_config.theme_color && !this.is_valid_color(ui_config.theme_color)) {
      result.warnings.push('主题颜色格式无效');
    }

    if (!ui_config.welcome_message || ui_config.welcome_message.trim().length < 5) {
      result.suggestions.push('建议设置更详细的欢迎消息');
    }
  }

  /**
   * 验证所有配置
   */
  static validate_all_configs(configs: Map<string, ModelConfig>): Map<string, ValidationResult> {
    const results = new Map<string, ValidationResult>();
    
    for (const [config_id, config] of configs.entries()) {
      results.set(config_id, this.validate_config(config, config_id));
    }

    return results;
  }

  /**
   * 生成验证报告
   */
  static generate_validation_report(results: Map<string, ValidationResult>): string {
    let report = '# 配置验证报告\n\n';
    
    const total_configs = results.size;
    const valid_configs = Array.from(results.values()).filter(r => r.is_valid).length;
    const invalid_configs = total_configs - valid_configs;
    
    report += `## 总览\n`;
    report += `- 总配置数: ${total_configs}\n`;
    report += `- 有效配置: ${valid_configs}\n`;
    report += `- 无效配置: ${invalid_configs}\n\n`;

    for (const [config_id, result] of results.entries()) {
      report += `## ${config_id} ${result.is_valid ? '✅' : '❌'}\n\n`;
      
      if (result.errors.length > 0) {
        report += `### 错误\n`;
        result.errors.forEach(error => report += `- ❌ ${error}\n`);
        report += '\n';
      }
      
      if (result.warnings.length > 0) {
        report += `### 警告\n`;
        result.warnings.forEach(warning => report += `- ⚠️ ${warning}\n`);
        report += '\n';
      }
      
      if (result.suggestions.length > 0) {
        report += `### 建议\n`;
        result.suggestions.forEach(suggestion => report += `- 💡 ${suggestion}\n`);
        report += '\n';
      }
    }

    return report;
  }

  /**
   * 工具方法：验证URL
   */
  private static is_valid_url(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 工具方法：验证WebSocket URL
   */
  private static is_valid_websocket_url(url: string): boolean {
    return url.startsWith('ws://') || url.startsWith('wss://');
  }

  /**
   * 工具方法：验证颜色
   */
  private static is_valid_color(color: string): boolean {
    return /^#[0-9A-Fa-f]{6}$/.test(color);
  }
}
