{"name": "Cloudflare Llama 3.1", "provider": "cloudflare", "api_key": "c3bca45b1fb78720ccbae68aab295b064a77b", "base_url": "https://api.cloudflare.com/client/v4/accounts/edfc96ded4826f3aed180debc6331126/ai/run/", "api_base": "https://api.cloudflare.com/client/v4/accounts/edfc96ded4826f3aed180debc6331126/ai/run/", "model": "@cf/meta/llama-3.1-8b-instruct", "alternate_models": ["@cf/mistral/mistral-7b-instruct-v0.1", "@cf/meta/llama-3.1-70b-instruct"], "max_tokens": 4096, "temperature": 0.5, "top_p": 0.95, "timeout": 90, "retry_count": 3, "retry_delay": 3, "system_prompt": "你是Llama 3.1模型，一个专注于驱动程序开发的助手。请对Windows驱动代码进行精确、清晰的分析。\n\n请关注以下方面：\n1. 函数的调用关系和执行流程\n2. 驱动程序的初始化和卸载过程\n3. 设备I/O控制代码的验证方式\n4. 错误处理和资源清理机制\n\n请提供精细的中文注释，说明每个关键函数和变量的作用，并分析可能的改进方向。注释应该清晰、准确，并且有助于理解驱动程序的整体架构和工作原理。", "low_latency": true}