body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 10px;
    box-sizing: border-box;
}

.chat-window {
    border: 1px solid var(--vscode-side-bar-border);
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    border-radius: 5px;
}

.chat-window h2 {
    margin: 0;
    padding: 10px;
    font-size: 1.1em;
    border-bottom: 1px solid var(--vscode-side-bar-border);
    background-color: var(--vscode-side-bar-background);
}

.chat-history {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.message {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 15px;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background-color: var(--vscode-list-active-selection-background);
    color: var(--vscode-list-active-selection-foreground);
    align-self: flex-end;
    margin-left: auto;
}

.bot-message {
    background-color: var(--vscode-editor-widget-background);
    align-self: flex-start;
}

.error-message {
    background-color: var(--vscode-input-validation-error-background);
    color: var(--vscode-input-validation-error-foreground);
    border: 1px solid var(--vscode-input-validation-error-border);
    align-self: flex-start;
}

.chat-input {
    display: flex;
    padding: 10px;
    border-top: 1px solid var(--vscode-side-bar-border);
    gap: 10px;
    align-items: flex-end;
}

.input-wrapper {
    flex-grow: 1;
    position: relative;
}

.chat-input textarea {
    width: 100%;
    min-height: 36px;
    max-height: 120px;
    padding: 8px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border);
    border-radius: 5px;
    resize: none;
    font-family: inherit;
    font-size: inherit;
    line-height: 1.4;
    overflow-y: auto;
    box-sizing: border-box;
}

.chat-input input {
    flex-grow: 1;
    border: 1px solid var(--vscode-input-border);
    border-radius: 5px;
    padding: 8px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
}

.chat-input button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: 1px solid var(--vscode-button-border);
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    height: 36px;
    min-width: 60px;
}

.chat-input button:hover {
    background-color: var(--vscode-button-hover-background);
}

.chat-input button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 独立窗口样式 */
.model-header {
    background-color: var(--model-theme-color, var(--vscode-side-bar-background));
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
}

.welcome-message {
    padding: 15px;
    text-align: center;
    color: var(--vscode-description-foreground);
    font-style: italic;
    border-bottom: 1px solid var(--vscode-side-bar-border);
}

.message-content {
    margin-bottom: 4px;
}

.message-time {
    font-size: 0.8em;
    color: var(--vscode-description-foreground);
    text-align: right;
}

/* 独立窗口布局 */
body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
    padding: 0;
}

#chat-history {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.chat-input {
    margin-top: auto;
}