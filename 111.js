#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, ToolSchema } from '@modelcontextprotocol/sdk/types.js';
import { randomBytes } from 'crypto';
import { createTwoFilesPatch } from 'diff';
import fs from 'fs/promises';
import { minimatch } from 'minimatch';
import os from 'os';
import path from 'path';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';

import { isPathWithinAllowedDirectories } from './path-validation.js';

// Command line argument parsing
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('Usage: mcp-server-filesystem <allowed-directory> [additional-directories...]');
  process.exit(1);
}

// Normalize all paths consistently
function normalizePath(p) {
  return path.normalize(p);
}

function expandHome(filepath) {
  if (filepath.startsWith('~/') || filepath === '~') {
    return path.join(os.homedir(), filepath.slice(1));
  }
  return filepath;
}

// Store allowed directories in normalized and resolved form
const allowedDirectories = await Promise.all(
  args.map(async dir => {
    const expanded = expandHome(dir);
    const absolute = path.resolve(expanded);
    try {
      // Resolve symlinks in allowed directories during startup
      const resolved = await fs.realpath(absolute);
      return normalizePath(resolved);
    } catch (error) {
      // If we can't resolve (doesn't exist), use the normalized absolute path
      // This allows configuring allowed dirs that will be created later
      return normalizePath(absolute);
    }
  }),
);

// Validate that all directories exist and are accessible
await Promise.all(
  args.map(async dir => {
    try {
      const stats = await fs.stat(expandHome(dir));
      if (!stats.isDirectory()) {
        console.error(`Error: ${dir} is not a directory`);
        process.exit(1);
      }
    } catch (error) {
      console.error(`Error accessing directory ${dir}:`, error);
      process.exit(1);
    }
  }),
);

// Security utilities
async function validatePath(requestedPath) {
  const expandedPath = expandHome(requestedPath);
  const absolute = path.isAbsolute(expandedPath)
    ? path.resolve(expandedPath)
    : path.resolve(process.cwd(), expandedPath);
  const normalizedRequested = normalizePath(absolute);

  // Check if path is within allowed directories
  const isAllowed = isPathWithinAllowedDirectories(normalizedRequested, allowedDirectories);
  if (!isAllowed) {
    throw new Error(
      `Access denied - path outside allowed directories: ${absolute} not in ${allowedDirectories.join(', ')}`,
    );
  }

  // Handle symlinks by checking their real path
  try {
    const realPath = await fs.realpath(absolute);
    const normalizedReal = normalizePath(realPath);
    if (!isPathWithinAllowedDirectories(normalizedReal, allowedDirectories)) {
      throw new Error(
        `Access denied - symlink target outside allowed directories: ${realPath} not in ${allowedDirectories.join(', ')}`,
      );
    }
    return realPath;
  } catch (error) {
    // For new files that don't exist yet, verify parent directory
    if (error.code === 'ENOENT') {
      const parentDir = path.dirname(absolute);
      try {
        const realParentPath = await fs.realpath(parentDir);
        const normalizedParent = normalizePath(realParentPath);
        if (!isPathWithinAllowedDirectories(normalizedParent, allowedDirectories)) {
          throw new Error(
            `Access denied - parent directory outside allowed directories: ${realParentPath} not in ${allowedDirectories.join(', ')}`,
          );
        }
        return absolute;
      } catch {
        throw new Error(`Parent directory does not exist: ${parentDir}`);
      }
    }
    throw error;
  }
}

// Schema definitions
const ReadFileArgsSchema = z.object({
  path: z.string(),
  tail: z.number().optional().describe('If provided, returns only the last N lines of the file'),
  head: z.number().optional().describe('If provided, returns only the first N lines of the file'),
});

const ReadMultipleFilesArgsSchema = z.object({
  paths: z.array(z.string()),
});

const WriteFileArgsSchema = z.object({
  path: z.string(),
  content: z.string(),
});

const EditOperation = z.object({
  oldText: z.string().describe('Text to search for - must match exactly'),
  newText: z.string().describe('Text to replace with'),
  lineNumber: z.number().optional().describe('Optional line number for precise, unambiguous edits'),
});

const EditFileArgsSchema = z.object({
  path: z.string(),
  edits: z.array(EditOperation),
  dryRun: z.boolean().default(false).describe('Preview changes using git-style diff format'),
});

const CreateDirectoryArgsSchema = z.object({
  path: z.string(),
});

const ListDirectoryArgsSchema = z.object({
  path: z.string(),
});

const ListDirectoryWithSizesArgsSchema = z.object({
  path: z.string(),
  sortBy: z.enum(['name', 'size']).optional().default('name').describe('Sort entries by name or size'),
});

const DirectoryTreeArgsSchema = z.object({
  path: z.string(),
});

const MoveFileArgsSchema = z.object({
  source: z.string(),
  destination: z.string(),
});

const SearchFilesArgsSchema = z.object({
  path: z.string(),
  pattern: z.string(),
  excludePatterns: z.array(z.string()).optional().default([]),
});

const GetFileInfoArgsSchema = z.object({
  path: z.string(),
});

const ToolInputSchema = ToolSchema.shape.inputSchema;

// Server setup
const server = new Server(
  {
    name: 'secure-filesystem-server',
    version: '0.2.0',
  },
  {
    capabilities: {
      tools: {},
    },
  },
);

// Tool implementations
async function getFileStats(filePath) {
  const stats = await fs.stat(filePath);
  return {
    size: stats.size,
    created: stats.birthtime,
    modified: stats.mtime,
    accessed: stats.atime,
    isDirectory: stats.isDirectory(),
    isFile: stats.isFile(),
    permissions: stats.mode.toString(8).slice(-3),
  };
}

async function searchFiles(rootPath, pattern, excludePatterns = []) {
  const results = [];
  async function search(currentPath) {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);
      try {
        await validatePath(fullPath);
        const relativePath = path.relative(rootPath, fullPath);
        const shouldExclude = excludePatterns.some(p => minimatch(relativePath, p, { dot: true }));
        if (shouldExclude) {
          continue;
        }

        if (entry.isDirectory()) {
          await search(fullPath);
        } else if (entry.isFile()) {
          // For files, read content and search for pattern line by line
          try {
            const content = await fs.readFile(fullPath, 'utf8');
            const lines = content.split(/\r?\n/);
            lines.forEach((lineContent, index) => {
              if (lineContent.toLowerCase().includes(pattern.toLowerCase())) {
                results.push({
                  path: fullPath,
                  lineNumber: index + 1,
                  lineContent: lineContent.trim(),
                });
              }
            });
          } catch (e) {
            // Ignore files that can't be read (e.g., binary files)
          }
        }
      } catch (error) {
        continue; // Skip inaccessible paths
      }
    }
  }
  await search(rootPath);
  return results;
}

// file editing and diffing utilities
function normalizeLineEndings(text) {
  return text.replace(/\r\n/g, '\n');
}

function createUnifiedDiff(originalContent, newContent, filepath = 'file') {
  // Ensure consistent line endings for diff
  const normalizedOriginal = normalizeLineEndings(originalContent);
  const normalizedNew = normalizeLineEndings(newContent);
  return createTwoFilesPatch(filepath, filepath, normalizedOriginal, normalizedNew, 'original', 'modified');
}

async function applyFileEdits(filePath, edits, dryRun = false) {
  const originalContent = await fs.readFile(filePath, 'utf-8');
  let content = originalContent;
  let lines = content.split(/\r?\n/);

  for (const edit of edits) {
    // New logic: Prioritize lineNumber if provided
    if (edit.lineNumber) {
      const lineIndex = edit.lineNumber - 1;
      if (lineIndex < 0 || lineIndex >= lines.length) {
        throw new Error(`Invalid line number: ${edit.lineNumber}. File has only ${lines.length} lines.`);
      }
      if (!lines[lineIndex].includes(edit.oldText)) {
        throw new Error(`Precondition failed: oldText not found at specified line number ${edit.lineNumber}.`);
      }
      // If line number is specified, we can safely replace just that line's content
      lines[lineIndex] = lines[lineIndex].replace(edit.oldText, edit.newText);
    } else {
      // Fallback to original behavior, but with a strict ambiguity check
      const occurrences = (content.match(new RegExp(edit.oldText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || [])
        .length;
      if (occurrences === 0) {
        throw new Error(`oldText not found in file: ${edit.oldText.substring(0, 100)}...`);
      }
      if (occurrences > 1) {
        throw new Error(
          `Ambiguous edit: oldText found multiple times. Please specify a lineNumber to resolve ambiguity.`,
        );
      }
      content = content.replace(edit.oldText, edit.newText);
      // re-split lines if we used the full content replace
      lines = content.split(/\r?\n/);
    }
  }

  const modifiedContent = lines.join(os.EOL); // Use OS-specific line endings for consistency
  const diff = createUnifiedDiff(originalContent, modifiedContent, filePath);

  if (!dryRun) {
    // Atomic write
    const tempPath = `${filePath}.${randomBytes(16).toString('hex')}.tmp`;
    try {
      await fs.writeFile(tempPath, modifiedContent, 'utf-8');
      await fs.rename(tempPath, filePath);
    } catch (error) {
      try {
        await fs.unlink(tempPath);
      } catch {}
      throw error;
    }
  }

  // Format diff with appropriate number of backticks
  let numBackticks = 3;
  while (diff.includes('`'.repeat(numBackticks))) {
    numBackticks++;
  }
  return `${'`'.repeat(numBackticks)}diff\n${diff}\n${'`'.repeat(numBackticks)}\n\n`;
}

// Helper functions
function formatSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  if (i === 0) return `${bytes} ${units[i]}`;
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
}

// Memory-efficient implementation to get the last N lines of a file
async function tailFile(filePath, numLines) {
  const CHUNK_SIZE = 1024; // Read 1KB at a time
  const stats = await fs.stat(filePath);
  const fileSize = stats.size;
  if (fileSize === 0) return '';

  // Open file for reading
  const fileHandle = await fs.open(filePath, 'r');
  try {
    const lines = [];
    let position = fileSize;
    let chunk = Buffer.alloc(CHUNK_SIZE);
    let linesFound = 0;
    let remainingText = '';

    // Read chunks from the end of the file until we have enough lines
    while (position > 0 && linesFound < numLines) {
      const size = Math.min(CHUNK_SIZE, position);
      position -= size;

      const { bytesRead } = await fileHandle.read(chunk, 0, size, position);
      if (!bytesRead) break;

      // Get the chunk as a string and prepend any remaining text from previous iteration
      const readData = chunk.slice(0, bytesRead).toString('utf-8');
      const chunkText = readData + remainingText;

      // Split by newlines and count
      const chunkLines = normalizeLineEndings(chunkText).split('\n');

      // If this isn't the end of the file, the first line is likely incomplete
      // Save it to prepend to the next chunk
      if (position > 0) {
        remainingText = chunkLines[0];
        chunkLines.shift(); // Remove the first (incomplete) line
      }

      // Add lines to our result (up to the number we need)
      for (let i = chunkLines.length - 1; i >= 0 && linesFound < numLines; i--) {
        lines.unshift(chunkLines[i]);
        linesFound++;
      }
    }

    return lines.join('\n');
  } finally {
    await fileHandle.close();
  }
}

// New function to get the first N lines of a file
async function headFile(filePath, numLines) {
  const fileHandle = await fs.open(filePath, 'r');
  try {
    const lines = [];
    let buffer = '';
    let bytesRead = 0;
    const chunk = Buffer.alloc(1024); // 1KB buffer

    // Read chunks and count lines until we have enough or reach EOF
    while (lines.length < numLines) {
      const result = await fileHandle.read(chunk, 0, chunk.length, bytesRead);
      if (result.bytesRead === 0) break; // End of file

      bytesRead += result.bytesRead;
      buffer += chunk.slice(0, result.bytesRead).toString('utf-8');

      const newLineIndex = buffer.lastIndexOf('\n');
      if (newLineIndex !== -1) {
        const completeLines = buffer.slice(0, newLineIndex).split('\n');
        buffer = buffer.slice(newLineIndex + 1);

        for (const line of completeLines) {
          lines.push(line);
          if (lines.length >= numLines) break;
        }
      }
    }

    // If there is leftover content and we still need lines, add it
    if (buffer.length > 0 && lines.length < numLines) {
      lines.push(buffer);
    }

    return lines.join('\n');
  } finally {
    await fileHandle.close();
  }
}

// Tool handlers
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'read_file',
        description:
          'Reads the complete contents of a file. ' +
          "**IMPORTANT**: For subsequent edits with 'edit_file', you MUST read the ENTIRE file to get an accurate snapshot for the 'oldText' parameter. " +
          "Path must be absolute (e.g., 'C:\\path\\to\\file.txt'). " +
          "Use 'head' or 'tail' for partial reads, but NOT before an edit.",
        inputSchema: zodToJsonSchema(ReadFileArgsSchema),
      },
      {
        name: 'read_multiple_files',
        description:
          'Read the contents of multiple files simultaneously. This is more ' +
          'efficient than reading files one by one when you need to analyze ' +
          "or compare multiple files. Each file's content is returned with its " +
          "path as a reference. Failed reads for individual files won't stop " +
          'the entire operation. Only works within allowed directories.',
        inputSchema: zodToJsonSchema(ReadMultipleFilesArgsSchema),
      },
      {
        name: 'write_file',
        description:
          'Creates a new file or COMPLETELY OVERWRITES an existing file. ' +
          '**CAUTION**: This tool is destructive and will replace existing content. ' +
          'To create a new file safely, first check if it exists. ' +
          "Path must be absolute (e.g., 'C:\\path\\to\\file.txt').",
        inputSchema: zodToJsonSchema(WriteFileArgsSchema),
      },
      {
        name: 'edit_file',
        description:
          "Atomically edits a file. 'oldText' MUST be an EXACT substring from a recent read. " +
          "For enhanced safety, provide an optional 'lineNumber' for each edit to ensure the change happens at the exact line, creating a dual content-and-location validation. " +
          'Path must be absolute.',
        inputSchema: zodToJsonSchema(EditFileArgsSchema),
      },
      {
        name: 'create_directory',
        description:
          'Creates a directory, including any necessary parent directories. ' +
          'Succeeds silently if the directory already exists. ' +
          "Path must be absolute (e.g., 'C:\\path\\to\\dir').",
        inputSchema: zodToJsonSchema(CreateDirectoryArgsSchema),
      },
      {
        name: 'list_directory',
        description:
          'Lists files and subdirectories in a given directory. ' +
          "Path must be absolute (e.g., 'C:\\path\\to\\dir').",
        inputSchema: zodToJsonSchema(ListDirectoryArgsSchema),
      },
      {
        name: 'list_directory_with_sizes',
        description:
          'Get a detailed listing of all files and directories in a specified path, including sizes. ' +
          'Results clearly distinguish between files and directories with [FILE] and [DIR] ' +
          'prefixes. This tool is useful for understanding directory structure and ' +
          'finding specific files within a directory. Only works within allowed directories.',
        inputSchema: zodToJsonSchema(ListDirectoryWithSizesArgsSchema),
      },
      {
        name: 'directory_tree',
        description:
          'Get a recursive tree view of files and directories as a JSON structure. ' +
          "Each entry includes 'name', 'type' (file/directory), and 'children' for directories. " +
          'Files have no children array, while directories always have a children array (which may be empty). ' +
          'The output is formatted with 2-space indentation for readability. Only works within allowed directories.',
        inputSchema: zodToJsonSchema(DirectoryTreeArgsSchema),
      },
      {
        name: 'move_file',
        description:
          'Move or rename files and directories. Can move files between directories ' +
          'and rename them in a single operation. If the destination exists, the ' +
          'operation will fail. Works across different directories and can be used ' +
          'for simple renaming within the same directory. Both source and destination must be within allowed directories.',
        inputSchema: zodToJsonSchema(MoveFileArgsSchema),
      },
      {
        name: 'search_files',
        description:
          'Recursively search for lines containing the given text pattern within files. ' +
          "Returns a JSON string array of objects, each with 'path', 'lineNumber', and 'lineContent'. " +
          'Ideal for getting precise locations for edits. ' +
          'Only searches within allowed directories.',
        inputSchema: zodToJsonSchema(SearchFilesArgsSchema),
      },
      {
        name: 'get_file_info',
        description:
          'Retrieve detailed metadata about a file or directory. Returns comprehensive ' +
          'information including size, creation time, last modified time, permissions, ' +
          'and type. This tool is perfect for understanding file characteristics ' +
          'without reading the actual content. Only works within allowed directories.',
        inputSchema: zodToJsonSchema(GetFileInfoArgsSchema),
      },
      {
        name: 'list_allowed_directories',
        description:
          'Returns the list of directories that this server is allowed to access. ' +
          'Use this to understand which directories are available before trying to access files.',
        inputSchema: {
          type: 'object',
          properties: {},
          required: [],
        },
      },
    ],
  };
});

server.setRequestHandler(CallToolRequestSchema, async request => {
  try {
    const { name, arguments: args } = request.params;
    switch (name) {
      case 'read_file': {
        const parsed = ReadFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for read_file: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);

        if (parsed.data.head && parsed.data.tail) {
          throw new Error('Cannot specify both head and tail parameters simultaneously');
        }

        if (parsed.data.tail) {
          // Use memory-efficient tail implementation for large files
          const tailContent = await tailFile(validPath, parsed.data.tail);
          return {
            content: [{ type: 'text', text: tailContent }],
          };
        }

        if (parsed.data.head) {
          // Use memory-efficient head implementation for large files
          const headContent = await headFile(validPath, parsed.data.head);
          return {
            content: [{ type: 'text', text: headContent }],
          };
        }

        const content = await fs.readFile(validPath, 'utf-8');
        return {
          content: [{ type: 'text', text: content }],
        };
      }
      case 'read_multiple_files': {
        const parsed = ReadMultipleFilesArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for read_multiple_files: ${parsed.error}`);
        }
        const results = await Promise.all(
          parsed.data.paths.map(async filePath => {
            try {
              const validPath = await validatePath(filePath);
              const content = await fs.readFile(validPath, 'utf-8');
              return `${filePath}:\n${content}\n`;
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              return `${filePath}: Error - ${errorMessage}`;
            }
          }),
        );
        return {
          content: [{ type: 'text', text: results.join('\n---\n') }],
        };
      }
      case 'write_file': {
        const parsed = WriteFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for write_file: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        try {
          // Security: 'wx' flag ensures exclusive creation - fails if file/symlink exists,
          // preventing writes through pre-existing symlinks
          await fs.writeFile(validPath, parsed.data.content, { encoding: 'utf-8', flag: 'wx' });
        } catch (error) {
          if (error.code === 'EEXIST') {
            // Security: Use atomic rename to prevent race conditions where symlinks
            // could be created between validation and write. Rename operations
            // replace the target file atomically and don't follow symlinks.
            const tempPath = `${validPath}.${randomBytes(16).toString('hex')}.tmp`;
            try {
              await fs.writeFile(tempPath, parsed.data.content, 'utf-8');
              await fs.rename(tempPath, validPath);
            } catch (renameError) {
              try {
                await fs.unlink(tempPath);
              } catch {}
              throw renameError;
            }
          } else {
            throw error;
          }
        }
        return {
          content: [{ type: 'text', text: `Successfully wrote to ${parsed.data.path}` }],
        };
      }
      case 'edit_file': {
        const parsed = EditFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for edit_file: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const result = await applyFileEdits(validPath, parsed.data.edits, parsed.data.dryRun);
        return {
          content: [{ type: 'text', text: result }],
        };
      }
      case 'create_directory': {
        const parsed = CreateDirectoryArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for create_directory: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        await fs.mkdir(validPath, { recursive: true });
        return {
          content: [{ type: 'text', text: `Successfully created directory ${parsed.data.path}` }],
        };
      }
      case 'list_directory': {
        const parsed = ListDirectoryArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for list_directory: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const entries = await fs.readdir(validPath, { withFileTypes: true });
        const formatted = entries.map(entry => `${entry.isDirectory() ? '[DIR]' : '[FILE]'} ${entry.name}`).join('\n');
        return {
          content: [{ type: 'text', text: formatted }],
        };
      }
      case 'list_directory_with_sizes': {
        const parsed = ListDirectoryWithSizesArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for list_directory_with_sizes: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const entries = await fs.readdir(validPath, { withFileTypes: true });

        // Get detailed information for each entry
        const detailedEntries = await Promise.all(
          entries.map(async entry => {
            const entryPath = path.join(validPath, entry.name);
            try {
              const stats = await fs.stat(entryPath);
              return {
                name: entry.name,
                isDirectory: entry.isDirectory(),
                size: stats.size,
                mtime: stats.mtime,
              };
            } catch (error) {
              return {
                name: entry.name,
                isDirectory: entry.isDirectory(),
                size: 0,
                mtime: new Date(0),
              };
            }
          }),
        );

        // Sort entries based on sortBy parameter
        const sortedEntries = [...detailedEntries].sort((a, b) => {
          if (parsed.data.sortBy === 'size') {
            return b.size - a.size; // Descending by size
          }
          // Default sort by name
          return a.name.localeCompare(b.name);
        });

        // Format the output
        const formattedEntries = sortedEntries.map(
          entry =>
            `${entry.isDirectory ? '[DIR]' : '[FILE]'} ${entry.name.padEnd(30)} ${entry.isDirectory ? '' : formatSize(entry.size).padStart(10)}`,
        );

        // Add summary
        const totalFiles = detailedEntries.filter(e => !e.isDirectory).length;
        const totalDirs = detailedEntries.filter(e => e.isDirectory).length;
        const totalSize = detailedEntries.reduce((sum, entry) => sum + (entry.isDirectory ? 0 : entry.size), 0);
        const summary = [
          '',
          `Total: ${totalFiles} files, ${totalDirs} directories`,
          `Combined size: ${formatSize(totalSize)}`,
        ];

        return {
          content: [
            {
              type: 'text',
              text: [...formattedEntries, ...summary].join('\n'),
            },
          ],
        };
      }
      case 'directory_tree': {
        const parsed = DirectoryTreeArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for directory_tree: ${parsed.error}`);
        }
        async function buildTree(currentPath) {
          const validPath = await validatePath(currentPath);
          const entries = await fs.readdir(validPath, { withFileTypes: true });
          const result = [];
          for (const entry of entries) {
            const entryData = {
              name: entry.name,
              type: entry.isDirectory() ? 'directory' : 'file',
            };
            if (entry.isDirectory()) {
              const subPath = path.join(currentPath, entry.name);
              entryData.children = await buildTree(subPath);
            }
            result.push(entryData);
          }
          return result;
        }
        const treeData = await buildTree(parsed.data.path);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(treeData, null, 2),
            },
          ],
        };
      }
      case 'move_file': {
        const parsed = MoveFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for move_file: ${parsed.error}`);
        }
        const validSourcePath = await validatePath(parsed.data.source);
        const validDestPath = await validatePath(parsed.data.destination);
        await fs.rename(validSourcePath, validDestPath);
        return {
          content: [{ type: 'text', text: `Successfully moved ${parsed.data.source} to ${parsed.data.destination}` }],
        };
      }
      case 'search_files': {
        const parsed = SearchFilesArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for search_files: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const results = await searchFiles(validPath, parsed.data.pattern, parsed.data.excludePatterns);
        const jsonResult = JSON.stringify(results, null, 2);
        return {
          content: [{ type: 'text', text: results.length > 0 ? jsonResult : 'No matches found' }],
        };
      }
      case 'get_file_info': {
        const parsed = GetFileInfoArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for get_file_info: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const info = await getFileStats(validPath);
        return {
          content: [
            {
              type: 'text',
              text: Object.entries(info)
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n'),
            },
          ],
        };
      }
      case 'list_allowed_directories': {
        return {
          content: [
            {
              type: 'text',
              text: `Allowed directories:\n${allowedDirectories.join('\n')}`,
            },
          ],
        };
      }
      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      content: [{ type: 'text', text: `Error: ${errorMessage}` }],
      isError: true,
    };
  }
});

// Start server
async function runServer() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('Secure MCP Filesystem Server running on stdio');
  console.error('Allowed directories:', allowedDirectories);
}

runServer().catch(error => {
  console.error('Fatal error running server:', error);
  process.exit(1);
});
