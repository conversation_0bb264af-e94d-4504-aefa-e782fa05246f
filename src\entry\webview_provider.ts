import * as vscode from 'vscode';

// A function to generate a random string for the nonce.
function get_nonce(): string {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

/**
 * Provides a command to create a webview panel that demonstrates best practices.
 * This function should be called from your extension's `activate` function.
 *
 * @param context The extension context.
 */
export function register_webview_provider(context: vscode.ExtensionContext): void {
  const command = vscode.commands.registerCommand('myExtension.showWebview', () => {
    // Create and show a new webview panel
    const panel = vscode.window.createWebviewPanel(
      'myWebview', // Identifies the type of the webview. Used internally
      'My Webview', // Title of the panel displayed to the user
      vscode.ViewColumn.One, // Editor column to show the new webview panel in.
      {
        // Enable scripts in the webview
        enableScripts: true,

        // And restrict the webview to only loading content from our extension's `media` directory.
        localResourceRoots: [vscode.Uri.joinPath(context.extensionUri, 'media')],
      },
    );

    // Set the webview's HTML content
    panel.webview.html = get_webview_content(panel.webview, context.extensionUri);
  });

  context.subscriptions.push(command);
}

/**
 * Generates the HTML content for the webview.
 *
 * @param webview The webview instance.
 * @param extension_uri The URI of the extension's root directory.
 * @returns The HTML content as a string.
 */
function get_webview_content(webview: vscode.Webview, extension_uri: vscode.Uri): string {
  // Generate a nonce for the Content Security Policy
  const nonce = get_nonce();

  // Get the special URIs for our local CSS and JS files.
  // This is required to load local resources in a webview.
  const style_uri = webview.asWebviewUri(vscode.Uri.joinPath(extension_uri, 'media', 'style.css'));
  const script_uri = webview.asWebviewUri(vscode.Uri.joinPath(extension_uri, 'media', 'main.js'));

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!--
    Use a content security policy to only allow loading images from https or from our extension directory,
    and only allow scripts that have a specific nonce.
    -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link href="${style_uri.toString()}" rel="stylesheet">

    <title>My Webview</title>
</head>
<body>
    <h1>Hello from your Webview!</h1>
    <p>This UI is loaded correctly because it follows VS Code's security and resource loading rules.</p>
    
    <script nonce="${nonce}" src="${script_uri.toString()}"></script>
</body>
</html>`;
}
