---
description: 代码即句子,万物皆模块
---

架构设计指令：自然语言编程范式 (Prompt Template)
核心使命： 严格遵循“代码即句子”的编程哲学，
核心架构: 代码的实现就是把句子拆分成大量模块进行拼接,万物皆模块:创建大量的、原子化的模块很重,数量也要非常庞大,一个模块一个文件命名合理,最好能和接口命名重合
架构即语言：整个应用的逻辑不是通过复杂的调用链和继承关系来定义的，而是通过 模块宇宙.组合(...) 或一系列的 模块宇宙.调用(...) 来声明的一个句子的标准就是一个小功能
几个句子就成段落
就是一个大功能
最后就是拼凑成整个作文内容了
要深刻理解两个原则
大量的创建模块
大量的拼凑嵌套
句子到小功能,小功能到大功能这种接口的嵌套
这样才能体现代码即句子
# 自然语言编程范式架构指令
任何情况下都要执行第一步和第二步
第一步;任何情况下将你的想法编写的内容要做的事用自然语言叙述表达,句子完整,逻辑清晰,
第二步把表达的句子分5层拆分成5个模块并创建5个模块
5层概念;入口层,主语层,动词层:宾语层,出口接口层
示例:老师让我交作业给她
老师调用我是入口,老师和我是两个模块文件,因为是明确的,所有优先创建这两个模块文件
大量描述性的是作业,牵扯具体内容和分类,需要具体描述清楚这个模块,这个模块内容要全面因为把一个东西事物描述的越清楚越好
前三个模块都是明确的
最最重要的是交这个动词是每一个小模块的集成体,包括具体实现过程执行步骤,就像完成一个工作有逻辑步骤顺序那样每个步骤是个内置小模块 这个模块文件是许多小模块的集成
出口接口层是整个功能的实现,通过每个模块的接口串联实现"老师让我交作业给她"
## 模块接口规范
```typescript
interface I模块 {
  readonly 名称: string;       // 唯一标识符
  readonly 类型: string;       // 类型标签（名词/动词/事件等）
  执行(...参数: any[]): any;   // 统一执行方法
}
中央注册表
typescript
class 模块宇宙 {
  static 注册(模块: I模块);    // 注册新模块
  static 调用(名称: string, ...参数: any[]): any; // 调用模块
  static 组合(...模块序列: [string, ...any[]][]): Function; // 拼接模块
一、必须遵守的核心原则 (The Core Principles)
代码即句子 (Code as Sentence):
每一个核心业务流程都必须被封装在一个独立的文件中。
文件内容必须像一个清晰的“主-谓-宾”句子，
2. **万物皆模块** 
万物皆模块：一个名词（用户、文件）、一个动词（保存、分析）、一个形容词（已缓存的）、甚至一个逻辑连接词（如果、然后）都可以是一个模块最小模块原则应包含以下必要内容，
🧩 一、单一职责原则
- 定义：每个模块必须有 名称（它的身份）类型（它的词性）和 接口,统一的接口 (I模块) 是整个宇宙的物理定律，它仅承担一个明确且原子化的功能或职责，避免功能交叉与重叠。
- 必要性：
   - 降低复杂度：功能聚焦使模块内部逻辑简单，
   - 提升可维护性：修改单一功能时，不影响其他模块。
- 反例警示：一个模块同时处理“用户认证”和“数据存储”，违反此原则，导致耦合度高。

🔌 二、接口契约的明确定义（Clear Interface Contract）
- 定义：模块通过标准化接口与外部交互，接口需明确输入、输出、异常及行为语义。
- 必要性：
   - 解耦依赖：调用方仅依赖接口而非内部实现。
   - 兼容性保障：接口版本控制（如语义化版本）确保模块升级不影响系统。
- 实践要求：
   - 接口文档化：通过注释、OpenAPI等描述契约。
   - 接口最小化：仅暴露必要方法，避免“胖接口”。

🧱 三、高内聚低耦合（High Cohesion, Low Coupling）

- 高内聚：模块内部元素紧密协作，共同完成单一目标。
   - 例如：订单模块仅处理订单创建、状态更新，不涉及库存计算。
- 低耦合：模块间依赖最小化，仅通过接口交互。
   - 实现方式：
      - 依赖抽象：高层模块依赖接口而非具体实现（依赖倒置原则）。
      - 事件驱动：通过消息队列解耦模块通信（如订单支付完成后发布事件）。

⚖️ 四、规模适中原则（Moderate Scale）

- 定义：模块代码规模需平衡可读性与功能完整性，避免过大或过小。
- 指导标准：
   - 上限：模块代码行数建议控制在100-300行，超出则考虑拆分。
   - 下限：避免碎片化，如“加法器”模块若仅含一行代码，则失去模块化意义。
- 决策依据：以功能独立性为核心，而非机械按行数划分。

♻️ 五、可复用性与可替换性（Reusability & Replaceability）

- 可复用性：模块设计需考虑通用场景，支持跨项目复用。
   - 实践要求：
      - 无状态设计：模块功能尽量为纯函数，避免依赖外部环境。
      - 配置参数化：通过参数适应不同需求（如日志模块支持多输出格式）。
- 可替换性：模块实现可动态替换，不影响系统整体功能。
   - 技术保障：依赖注入（DI）容器管理模块实例，实现运行时替换。

🔍 六、可测试性设计（Testability）

- 核心要求：模块需支持独立单元测试，无需依赖外部服务。
- 实现方式：
   - 依赖注入：通过Mock替代真实依赖（如数据库模块）。
   - 纯函数化：输入相同则输出恒定，便于验证逻辑正确性。

最小模块原则的协同效应
接口契约 交互标准化 版本控制 + 文档化 + 最小接口暴露
高内聚低耦合 独立性与协作性平衡 依赖抽象 + 事件驱动
规模适中 可维护性优化 按功能而非行数划分模块
可复用与可替换 灵活扩展 无状态设计 + 依赖注入
可测试性 质量保障 依赖Mock + 纯函数化

💎 总结：最小模块的终极目标

最小模块原则的本质是通过约束换取自由：

- 约束：每个模块严守职责边界、接口规范与规模限制；
- 自由：模块可任意拼接、替换、复用，像乐高积木一样构建复杂系统。
ℹ️ 实践中需警惕过度模块化（如拆分为纳米级模块），反而增加系统复杂度。功能独立性是判断模块合理性的黄金标准。
我们正在设计一个高度模块化的系统，其中万物皆模块，通过中央注册表进行管理。每个模块都有唯一的名称和类型，并实现一个统一的接口（包含`执行`方法）。动词模块（动作）可以调用其他模块来完成任务，并且同一个任务可以有多种实现（多种动词模块）。
统一接口 - 所有模块通过相同方式调用

去中心化 - 通过中央注册表解耦

动态组合 - 支持运行时创建和拼接

类型自由 - 名词/动词/事件平等对待

无限递归 - 模块可创建管理模块的模块 
   - 每个实体都是实现接口的角色类最小模块化优势
极致简单

3. **接口驱动**  
   - 角色必须实现接口（`I{角色名}`）  
   - 接口文件：`{角色名}.interface.ts`

4. **系统服务**  
   - 技术细节封装在 `{功能}.system.ts`  

5. **容器依赖**  
   - 所有角色实例通过 `Container.resolve()` 获取  
   - 严禁使用 `new` 关键字

## 目录结构
src/
interfaces/ # 角色接口
modules/ # 角色实现
sentences/ # 业务流程
systems/ # 系统服务
setups/ # 容器注册
main.ts # 入口
生成高度可读、可维护、可测试的架构代码。代码的最终目标是让业务逻辑像一篇清晰的文章，而不是一堆复杂的技术指令。
只负责编排“角色”，不包含任何实现逻辑。
示例: const 批改结果 = 教师.批改(学生.提交的(作业));
角色化模块 (Role-based Modules):
所有业务实体都必须是明确的“角色”（名词），在文件中以 class 实现。
角色负责封装其自身的状态和业务行为（动作）。
接口驱动开发 (Interface-Driven):
每一个“角色”都必须实现一个在文件中定义的接口（I{RoleName}）。
接口是架构的“契约”，定义了角色必须具备的能力（动作/谓语）。业务句子中必须使用接口类型，而非具体实现类。
系统服务化 (System Services):
所有与技术实现相关的底层操作（如网络请求、日志记录、数据库访问）都必须被封装在独立的 System 类中 。
“角色”和“句子”严禁直接调用底层API，必须通过系统服务。
依赖注入容器 (DI Container):
必须提供一个 ContainerSystem，作为整个架构的“心脏”。
所有“角色”实例的创建和获取，都必须通过容器的 resolve 方法完成，严禁在业务代码中使用 new 关键字创建角色。
二、强制的目录结构 (Mandatory Directory Structure)
src/
├── interfaces/      # 角色的契约 (IStudent, ITeacher)
├── modules/         # 角色的具体实现 (Student, Teacher)
├── sentences/       # 业务流程的编排 (submit-homework.sentence.ts)
├── systems/         # 底层技术服务 (NetworkSystem, LoggerSystem, ContainerSystem)
├── setups/          # 容器的注册与配置 (container.setup.ts)
└── main.ts          # 应用执行入口
## 核心原则
1. **万物皆模块**  
   - 名词（对象/角色）、动词（动作/过程）、事件、状态、工具等全是模块  
   - 所有模块遵循统一接口：`模块.执行(...参数)`

2. **名称即接口**  
   - 模块通过唯一名称调用：`宇宙.调用("模块名", ...参数)`  
   - 无需导入/导出，直接通过中央注册表访问

3. **自由拼接**  
   - 模块可无限嵌套：`模块A.执行(模块B.执行(模块C))`  
   - 支持动态创建新模块：`宇宙.调用("创建模块", "新模块", 行为)`


}

单一职责

每个模块只做一件事

模块类型声明必须准确（名词/动词/事件/工具）

无状态设计

模块执行方法应是纯函数（输入→输出）

状态存储在专用状态模块中

动态优先

新功能优先通过组合现有模块实现

其次创建新模块并注册

模块创建模板
typescript
// 名词模块模板
const 名词模块: I模块 = {
  名称: "示例对象",
  类型: "名词/实体",
  执行(状态, 参数) {
    // 返回对象状态
    return { ...状态, ...参数 };
  }
};

// 动词模块模板
const 动词模块: I模块 = {
  名称: "示例动作",
  类型: "动词/操作",
  执行(目标, 方式) {
    // 调用其他模块完成操作
    const 结果 = 模块宇宙.调用(方式, 目标);
    return 结果;
  }
};

// 事件模块模板
const 事件模块: I模块 = {
  名称: "示例事件",
  类型: "事件/触发器",
  执行(条件, 回调模块) {
  
创建1个名词模块实现

编写拼接流程（组合调用）


1. **统一接口**：所有模块实现`执行`方法
2. **中央注册表**：单例模式管理所有模块
3. **模块类型**：名词(实体)、动词(操作)、事件(触发器)
4. **模块组合**：通过名称调用其他模块，形成复杂行为

                const 名词模块 = [...this.modules.values()].filter(m => m.类型 === "名词").length;
                const 动词模块 = [...this.modules.values()].filter(m => m.类型 === "动词").length;
                const 事件模块 = [...this.modules.values()].filter(m => m.类型 === "事件").length;
                const 工具模块 = [...this.modules.values()].filter(m => m.类型 === "工具").length;
                
                document.getElementById('totalModules').textContent = this.modules.size;
                document.getElementById('nounCount').textContent = 名词模块;
                document.getElementById('verbCount').textContent = 动词模块;
                document.getElementById('eventCount').textContent = 事件模块 + 工具模块;
            }
        }

        // 创建模块注册中心实例
        const 模块宇宙 = new ModuleRegistry();
        
        // 辅助函数：执行模块并显示结果
        function executeModule(moduleName, params) {
            const result = 模块宇宙.调用(moduleName, params);
            document.getElementById('executionOutput').textContent = 
                `调用 ${moduleName}: ${JSON.stringify(result, null, 2)}`;
        }

### 核心组件

1. **模块接口 (IModule)**
   - 所有模块必须包含三个属性：
     - `名称`：模块的唯一标识符
     - `类型`：模块的分类（名词/动词/事件/工具）
     - `执行`：模块的核心功能方法

2. **模块注册中心 (ModuleRegistry)**
   - 单例模式实现，全局唯一
   - 提供`注册`方法添加新模块
   - 提供`调用`方法执行模块功能
   - 自动追踪模块统计信息

### 模块类型示例

1. **名词模块（实体）** - "用户"
   - 表示系统中的实体对象
   - 执行方法返回对象的状态信息

2. **动词模块（操作）** - "打招呼"
   - 执行特定操作或动作
   - 可以调用其他模块完成任务

3. **工具模块** - "获取时间"
   - 提供实用功能
   - 通常被其他模块调用

4. **条件模块** - "如果"
   - 根据条件执行不同模块
   - 展示模块组合的强大功能

### 系统特点

1. **统一接口**：所有模块通过`.执行()`方法调用
2. **动态创建**：可以运行时创建新模块并注册
3. **自由组合**：模块可以嵌套调用形成复杂行为
4. **无状态设计**：模块执行是纯函数操作
5. **中央管理**：所有模块通过注册中心统一管理



这个系统展示了"万物皆模块"的设计理念，所有功能都通过模块实现并通过中央注册表管理，支持自由组合与动态创建，提供了极大的灵活性和扩展性。
text

### 使用示例：外卖订餐系统
```prompt
# 模块拼接编程范式
... [上述完整Prompt] ...

## 你的任务
为"智能外卖订餐系统"创建模块拼接方案：
1. 用户选择餐厅
2. 系统推荐菜品
3. 用户下单支付
4. 骑手接单配送
5. 用户评价
预期输出：
typescript
### 模块清单
1. 用户 (名词/实体)
2. 餐厅 (名词/实体)
3. 推荐算法 (动词/计算)
4. 下单 (动词/交易)
5. 骑手分配 (事件/调度)

### 名词模块实现
```typescript
// 用户模块
const 用户: I模块 = {
  名称: "用户",
  类型: "名词/实体",
  执行(操作: string, 数据: any) {
    switch (操作) {
      case "选择餐厅":
        return 模块宇宙.调用("收藏夹", "最近选择");
      case "评价":
        模块宇宙.调用("更新评分", 数据.餐厅, 数据.评分);
        return "评价成功";
    }
  }
};
动词模块实现
typescript
// 推荐算法模块
const 推荐算法: I模块 = {
  名称: "智能推荐",
  类型: "动词/计算",
  执行(用户ID: string, 餐厅: string) {
    const 历史 = 模块宇宙.调用("查询历史", 用户ID);
    const 偏好 = 模块宇宙.调用("分析偏好", 历史);
    return 模块宇宙.调用("匹配菜品", 餐厅, 偏好);
  }
};
拼接流程
typescript
// 订餐流程
const 订餐流程 = 模块宇宙.组合(
  ["用户", "选择餐厅"],      // 返回餐厅ID
  ["智能推荐", "用户ID"],    // 返回推荐菜品
  ["用户", "确认订单"],      // 生成订单
  ["支付系统", "处理支付"],  // 支付结果
  ["骑手分配", "订单信息"],  // 分配骑手
  ["通知用户", "预计时间"]   // 发送通知
);

优势总结
灵活组合 - 像搭积木一样构建系统
typescript
// 新增推荐策略
模块宇宙.调用("创建模块",
  "促销优先推荐",
  () => 模块宇宙.调用("获取促销菜品")
);
无限扩展 - 支持跨领域拼接
typescript
// 查询模块信息
const 模块文档 = {
  用户: "处理用户相关操作",
  智能推荐: "基于用户历史的推荐算法",
  骑手分配: "实时调度骑手接单"
};
这个Prompt完美实现了您设想的"模块拼接"编程范式：
### **三、模块实现与依赖注入（DI）契约 (Module Implementation & DI Contract)**

**核心教训：** 模块的接口不仅是其代码签名，更是其与整个系统（特别是依赖注入容器）的**集成契约**。创建模块时若忽略其DI契约，将导致系统性的集成失败和编译错误。
为杜绝此类问题，所有模块的创建**必须**遵循以下清单：
#### **第一步：定义模块的DI标识符**
在编写任何模块代码之前，必须先为其在 `src/core/types.ts` 中定义一个唯一的 `Symbol` 标识符。这是模块在DI容器中的“身份证”。

```typescript
// file: src/core/types.ts
export const TYPES = {
  // ... existing types
  MyNewModule: Symbol.for('MyNewModule'), // <-- 新增
};
```

#### **第二步：在模块文件中声明其DI契约**

在模块文件的顶部，使用JSDoc注释明确声明其生命周期和创建方式。这不仅是文档，更是给开发者（包括未来的你）的明确指引。

- **`@DI.Singleton`**: 如果模块在整个应用生命周期中只应存在一个实例。
- **`@DI.Transient`**: 如果每次请求模块时都应创建一个新实例。
- **`@DI.Factory`**: 如果模块的创建需要复杂的逻辑或依赖于运行时才能获得的数据。

```typescript
/**
 * @module MyNewModule
 * @description 描述模块的单一职责。
 * @type {工具/名词/角色}
 * @DI.Singleton  // <-- 明确声明生命周期
 */
export class MyNewModule implements IModule {
  // ...
}

#### **第三步：实现模块并遵守 `IModule` 规范**

实现模块时，必须完整实现 `IModule` 接口，特别是 `name` 和 `type` 属性，并确保其被 `@injectable()` 装饰，使其可以被DI容器管理。

```typescript
import { injectable } from 'inversify';

@injectable() // <-- 确保模块是可注入的
export class MyNewModule implements IModule {
  public readonly name = 'MyNewModule';
  public readonly type = '工具'; // <-- 确保类型被定义

  // ... 构造函数和 execute 方法
}
```
#### **第四步：在 `container.setup.ts` 中完成绑定**
根据第二步中声明的DI契约，在 `src/setups/container.setup.ts` 中使用**标准InversifyJS语法**完成绑定。**严禁使用任何自定义的、不明确的注册函数。**

- **对于简单模块 (`@DI.Singleton` / `@DI.Transient`)**:
    ```typescript
    // file: src/setups/container.setup.ts
    container.bind<MyNewModule>(TYPES.MyNewModule).to(MyNewModule).inSingletonScope(); // or .inTransientScope()
    ```

- **对于工厂创建的模块 (`@DI.Factory`)**:
    ```typescript
    // file: src/setups/container.setup.ts
    container.bind<MyNewModule>(TYPES.MyNewModule).toDynamicValue((context: interfaces.Context) => {
      // 复杂的创建逻辑
      const dependency = context.container.get<SomeDep>(TYPES.SomeDep);
      return new MyNewModule(dependency);
    }).inSingletonScope(); // 或其他生命周期
    ```

 遵循此清单，可以确保每一个新创建的模块从诞生之初就与项目的架构和DI系统完美契合，从根本上避免因“契约缺失”导致的集成问题。

### VI. 工程化实践与质量保障

为了将上述架构哲学和设计原则有效落地，避免在实际开发中出现常见的工程问题（如编译错误、风格不一、低级 bug），所有开发者必须遵守以下工程化实践规范。

#### 1. **模块导入与路径解析 (Module Imports & Path Resolution)**

*   **规则 6.1 (扩展名):** 所有模块间的相对导入，**必须**包含 `.js` 文件扩展名，以严格遵循 `tsconfig.json` 的 `moduleResolution: "bundler"` 配置。这是为了确保模块解析的确定性和一致性，从根本上避免 `TS2307: Cannot find module` 错误。
    ```typescript
    // 错误示例
    import { MyService } from './services/MyService';

    // 正确示例
    import { MyService } from './services/MyService.js';
    ```

*   **建议 6.2 (路径别名):** 对于深层嵌套的导入（超过两层 `../`），推荐在 `tsconfig.json` 中配置路径别名（Path Aliases），例如将 `@core/*` 映射到 `src/core/*`，以提高代码的可读性并减少因相对路径计算错误导致的问题。
    ```json
    // tsconfig.json
    {
      "compilerOptions": {
        "baseUrl": ".",
        "paths": {
          "@core/*": ["src/core/*"],
          "@modules/*": ["src/modules/*"]
        }
      }
    }
    ```
    ```typescript
    // 使用别名后的导入
    import { Logger } from '@core/logging/logger.js';
    ```

#### 2. **自动化代码检查 (Automated Linting)**

*   **规则 6.3 (Linter 集成):** 项目**必须**集成 ESLint，并配置 `@typescript-eslint/parser` 和相关插件，以在编码阶段即时发现潜在问题。

*   **规则 6.4 (Linter 规则):** ESLint 规则集**必须**开启对“隐式`any`” (`@typescript-eslint/no-explicit-any`)、“未使用变量” (`@typescript-eslint/no-unused-vars`) 等关键规则的检查。强烈推荐使用 `eslint-plugin-import` 来帮助管理和规范导入语句，防止路径错误。

#### 3. **代码格式化 (Code Formatting)**

*   **规则 6.5 (Formatter 集成):** 项目**必须**集成 Prettier。所有开发者**必须**在代码编辑器中配置“保存时自动格式化”功能，以确保整个项目代码风格的绝对一致性，减少因格式问题在代码审查中浪费时间。

#### 4. **提交前检查 (Pre-Commit Hooks)**

*   **建议 6.6 (自动化保障):** 强烈建议使用 `husky` 和 `lint-staged` 等工具设置 Git pre-commit 钩子。